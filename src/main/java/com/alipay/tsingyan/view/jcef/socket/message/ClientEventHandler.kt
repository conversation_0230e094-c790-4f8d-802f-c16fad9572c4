package com.alipay.tsingyan.view.jcef.socket.message

import com.alipay.tsingyan.utils.json.JsonUtil
import com.alipay.tsingyan.view.jcef.socket.handler.rpc.RpcMsgService
import com.alipay.tsingyan.view.jcef.socket.handler.stream.StreamService
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.Logger

/**
 * 客户端消息处理器
 * 这个类要抽象为接口吗? 但目前看来只会引入 Netty ?貌似没有必要
 *
 */
@Service
class ClientEventHandler {
    private val logger = Logger.getInstance(this::class.java)

    /**
     * 消息处理器
     */
    fun handleMessage(wrapper: ClientWrapper, message: String) {
        val msg = try {
            JsonUtil.parseMessageV3(message)
        } catch (e: Exception) {
            e.printStackTrace()
            logger.error(e)
            wrapper.disconnect("Message Error")
            return
        }

        /**
         * 不想写状态机来执行下面的流程了，先 if else 把
         * 调试模式下，仍需要做校验
         */
        if (wrapper.status != AuthStatus.AUTH_SUCCESS) {
            when (val event = msg.data) {
                //握手事件里
                is HandShakeEvent -> {
                    //根据握手事件解析出对应的项目ID,映射到对应的项目上.
                    wrapper.authToken(event)
                }

                else -> {
                    wrapper.disconnect(wrapper.status.name)
                }
            }
        } else {
            when (val event = msg.data) {
                //握手事件里
                is PingEvent -> {

                }

                is RPCRequestEvent -> {
                    RpcMsgService.handlerRequest(wrapper.getProject(), event) { rsp -> wrapper.send(rsp) }
                }

                is RPCResponseEvent -> {
                    RpcMsgService.handlerResponse(event)
                }

                is StreamEvent -> {
                    StreamService.handlerUnidirectionalRequest(event)
                }

                is StreamRequestEvent -> {
                    StreamService.handlerBilateralRequest(wrapper.getProject(), event) { rsp -> wrapper.send(rsp) }
                }

                is StreamResponseEvent -> {
                    StreamService.handlerBilateralResponse(event)
                }

                else -> {
                    logger.info("未找到对应事件")
                }
            }
        }
    }

    /**
     * 客户端连接
     */
    fun onClientConnected(wrapper: ClientWrapper) {

    }

    /**
     * 客户端断联
     */
    fun onClientDisConnect(wrapper: ClientWrapper) {

    }
}