package com.alipay.tsingyan.view.jcef.common.websocket;

import cn.hutool.core.lang.Console;
import cn.hutool.core.thread.ThreadUtil;
import com.alipay.tsingyan.utils.CommonUtils;
import com.alipay.tsingyan.view.jcef.common.NetUtils;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.stream.ChunkedWriteHandler;

import java.net.InetAddress;

/**
 * <AUTHOR>
 */
public class WebSocketInitService {
    private static final Logger LOGGER = Logger.getInstance(WebSocketInitService.class);
    private int port;

    private final Project project;

    public WebSocketInitService(Project project) {
        //随时生成一个可用的端口
        port = NetUtils.getAvailablePort();
        this.project = project;
        //启动端口监听
        if (CommonUtils.INSTANCE.isCloudIDE()){
            ThreadUtil.execAsync(() -> start(port));
        }
    }

    /**
     * 获取当前的port
     * TODO 这步会堵住UI进程，原因未知
     *
     * @return
     */
    public int getPort() {
        return port;
    }


    /**
     * 开启端口监听
     *
     * @param port
     */
    private void start(int port) {
        // 创建主从线程池
        LOGGER.warn("开始启动WebSocket:" + NetUtils.getIp() + ":" + port);
        EventLoopGroup masterEventLoopGroup = new NioEventLoopGroup();
        EventLoopGroup slaveEventLoopGroup = new NioEventLoopGroup();

        try {
            // 创建服务的启动类
            ServerBootstrap bootstrap = new ServerBootstrap();
            // 设置Netty的主从线程模式
            bootstrap.group(masterEventLoopGroup, slaveEventLoopGroup);
            // 设置Netty管道类型
            bootstrap.channel(NioServerSocketChannel.class);
            // 设置客户端消息处理对象
            bootstrap.childHandler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel channel) throws Exception {
                    final InetAddress address = channel.remoteAddress().getAddress();
                    LOGGER.warn( "收到新连接:" + address);
                    ChannelPipeline pipeline = channel.pipeline();
                    //websocket基于http协议 所以要有一个http编解码器
                    pipeline.addLast(new HttpServerCodec())
                            // 支持异步发送大的码流(大的文件传输),但不占用过多的内存，防止java内存溢出
                            .addLast("httpChunked", new ChunkedWriteHandler())
                            // http 消息聚合器  10M为接收的最大contentlength
                            .addLast("httpAggregator", new HttpObjectAggregator(10 * 1024 * 1024))
                            .addLast(JcefSocketHandler.getInstance(project));
                }
            });

            //设置绑定端口，端口绑定是一个异步操作，所以下面需要阻塞
            ChannelFuture bind = bootstrap.bind(port);
            // 阻塞当前线程，端口绑定成功后往下执行
            bind.sync();
            LOGGER.warn("启动监听:" + NetUtils.getIp() + ":" + port);
            // 等待通道被关闭
            bind.channel().closeFuture().sync();
        } catch (InterruptedException e) {
            LOGGER.warn("initChannel.error", e);
        } finally {
            //释放线程池资源
            masterEventLoopGroup.shutdownGracefully();
            slaveEventLoopGroup.shutdownGracefully();
        }
    }
}
