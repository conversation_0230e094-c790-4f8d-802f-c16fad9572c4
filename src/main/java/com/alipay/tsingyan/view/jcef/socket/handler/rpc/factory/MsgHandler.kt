package com.alipay.tsingyan.view.jcef.socket.handler.rpc.factory

import com.alipay.tsingyan.view.jcef.socket.handler.DataCallBack
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import kotlin.reflect.KClass

abstract class MsgHandler<T : Any> {
    protected val logger = Logger.getInstance(this::class.java)
    abstract val type: String

    init {
        doInit()
    }

    private fun doInit() {
        // 注册实现到工厂里
        @Suppress("UNCHECKED_CAST")
        RPCHandlerFactory.register(this as MsgHandler<Any>)
        logger.info("注册RPC接口:[$type]")
    }

    /**
     * 调用对一个接口实现,此实现的返回可能是异步的,也可能是同步的
     * 回调
     */
    abstract fun execute(
        project: Project,
        param: T?,
        callBack: DataCallBack,
    )

    /**
     * 获取子类的泛型
     */
    fun getImplementClass(): Class<*> {
        val paramClass = this::class.supertypes.first().arguments[0].type!!.classifier as KClass<*>
        return paramClass.java
//        val genType = javaClass.genericSuperclass as? ParameterizedType ?: return Any::class.java
//        val params = genType.actualTypeArguments
//
//        if (params.isEmpty()) {
//            return Any::class.java
//        }
//
//        if (params[0] !is Class<*>) {
//            return Any::class.java
//        }
//        return params[0] as Class<*>
    }
}

