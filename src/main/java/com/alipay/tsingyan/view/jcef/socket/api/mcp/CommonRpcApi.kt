package com.alipay.tsingyan.view.jcef.socket.api.mcp

import com.alipay.tsingyan.mcp.server.ide.IDEMcpServer
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.view.jcef.socket.handler.DataCallBack
import com.alipay.tsingyan.view.jcef.socket.handler.rpc.factory.MsgHandler
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project

/**
 * 获取MCP结果集及工具映射
 */
class McpToolApi : MsgHandler<Unit>() {
    override val type: String
        get() = "mcpToolType"

    override fun execute(project: Project, param: Unit?, callBack: DataCallBack) {
        val result = try {
            project.service<IDEMcpServer>().getToolTypeList()
        } catch (e: Exception) {
            LogUtil.info("mcpToolType query fail", e)
            emptyMap()
        }
        return callBack.invoke(result)
    }
}