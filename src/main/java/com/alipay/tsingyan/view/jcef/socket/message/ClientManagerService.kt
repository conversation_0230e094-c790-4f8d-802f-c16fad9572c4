package com.alipay.tsingyan.view.jcef.socket.message

import cn.hutool.core.collection.ConcurrentHashSet
import com.intellij.openapi.components.Service

/**
 * 客户端管理器服务
 */
@Service
class ClientManagerService {
    private val wrapperSet = ConcurrentHashSet<ClientWrapper>()

    /**
     * 添加客户端 Wrapper
     */
    fun addWrapper(wrapper: ClientWrapper) {
        wrapperSet.add(wrapper)
    }

    /**
     * 移除管理的Wrapper
     */
    fun remove(wrapper: ClientWrapper) {
        wrapperSet.remove(wrapper)
    }

    /**
     * 广播消息
     */
    fun sendMessage(data: Event, type: SendType, projectID: String? = null) {
        when (type) {
            SendType.PROJECT -> {
                wrapperSet.filter { it.projectId == projectID }.forEach { it.doSend(data) }
            }

            SendType.ALL -> {
                wrapperSet.forEach { it.doSend(data) }
            }

            else -> {
                // 理论上也走不到这里
            }
        }
    }
}