package com.alipay.tsingyan.view.diff;

import javax.swing.*;
import java.awt.event.ActionEvent;

public abstract class DiffButtonAction extends AbstractAction {
    CodeFuseDiffWindow window;

    public abstract void doAction();

    public DiffButtonAction() {
    }

    public DiffButtonAction(String name) {
        super(name);
    }

    public DiffButtonAction(String name, Icon icon) {
        super(name, icon);
    }

    public void actionPerformed(ActionEvent e) {
        doAction();
        if (this.window != null) {
            this.window.close(0);
        }
    }

    public CodeFuseDiffWindow getWindow() {
        return this.window;
    }

    public void setWindow(CodeFuseDiffWindow window) {
        this.window = window;
    }
}