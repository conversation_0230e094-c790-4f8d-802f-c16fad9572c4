package com.alipay.tsingyan.util;

import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.process.CapturingProcessHandler;
import com.intellij.execution.process.ProcessListener;
import com.intellij.execution.process.ProcessOutput;
import org.jetbrains.annotations.NotNull;
import com.intellij.execution.process.KillableProcessHandler;

import java.util.List;

public class CommandLineUtil {
    /**
     * 执行命令并返回输出
     * @param command 要执行的命令列表
     * @return 命令的输出
     */
    public static ProcessOutput executeCommand(@NotNull List<String> command, int waitTime) {
        GeneralCommandLine commandLine = new GeneralCommandLine(command);

        // 设置合适的工作目录，如果需要的话
        // commandLine.setWorkDirectory(workDirectory);

        try {
            // 创建一个捕获输出的处理器
            CapturingProcessHandler processHandler = new CapturingProcessHandler(commandLine);

            // 运行命令并等待结果，这里可以指定一个时间限制
            // 如果你不需要时间限制，可以使用 runProcess() 方法
            ProcessOutput output = processHandler.runProcess(waitTime); // 等待最多5000毫秒

            // 检查进程是否成功执行
//            if (output.getExitCode() == 0) {
//                // 处理标准输出和错误输出
//                String stdout = output.getStdout();
//                String stderr = output.getStderr();
//
//                // 处理输出...
//            }

            return output;

        } catch (ExecutionException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 运行可中止的进程
     * MyKillableProcess 类创建了一个KillableProcessHandler来运行一个外部命令。processHandler对象用于启动进程、添加监听器和杀死进程。
     * 使用KillableProcessHandler是管理外部进程的优雅方式，尤其是当这些进程需要与IDEA的用户界面和其他IDEA的执行管理组件协同工作时。它可以确保外部进程的生命周期与IDE的运行/调试会话紧密集成，并且可以根据需要轻松终止。
     */
    public static KillableProcessHandler runKillableProcess(@NotNull List<String> command, ProcessListener listener) {
        GeneralCommandLine commandLine = new GeneralCommandLine(command);

        try {
            KillableProcessHandler processHandler = new KillableProcessHandler(commandLine);

            // 监听进程输出
            if (listener != null){
                processHandler.addProcessListener(listener);
            }

            // 启动进程
            processHandler.startNotify();

            // 在需要的时候，你可以杀死这个进程
            // processHandler.killProcess();

            return processHandler;
        } catch (ExecutionException e) {
            e.printStackTrace();
        }

        return null;
    }



}
