package com.alipay.http;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Get方法构建器
 *
 * <AUTHOR>
 * 创建时间 2022-10-17
 */
public class GetBuilder extends AbstractRequestBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetBuilder.class);

    private Map<String, String> param;

    /**
     * 头部
     */
    private final List<String> HEADER = new ArrayList<>();

    /**
     * get请求构造函数
     *
     * @param url
     * @param httpClient
     */
    public GetBuilder(String url, java.net.http.HttpClient httpClient) {
        super(url, httpClient);
    }


    /**
     * 增加get参数
     *
     * @param key
     * @param value
     * @return
     */
    public GetBuilder addParameter(String key, String value) {
        if (param == null) {
            param = new HashMap<>();
        }
        param.put(key, value);
        return this;
    }

    /**
     * 增加头部
     *
     * @param key   不可为空
     * @param value 不可为空
     * @return
     */
    public GetBuilder header(String key, String value) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("header key and value can not be null!");
        }
        if(HEADER.contains(key)){
            throw new IllegalArgumentException("header can not be duplicate!");

        }
        this.HEADER.add(key);
        this.HEADER.add(value);
        return this;
    }

    /**
     * 同步发起get请求
     *
     * @return
     */
    @Override
    public String syncExecute(long timeout) {
        try {
            if (param != null && param.size() > 0) {
                StringBuilder urlBuilder = new StringBuilder(url);
                urlBuilder.append("?");
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
                url = urlBuilder.substring(0, urlBuilder.length() - 1);
            }
            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(new URI(url))
                    .headers(HEADER.toArray(String[]::new))
                    .GET()
                    .timeout(Duration.ofMillis(timeout))
                    .build();
            return httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString()).body();
        } catch (Throwable e) {
            LOGGER.error("请求远程服务Http(GET)异常。地址: {}", url, e);
            return null;
        }
    }

    @Override
    protected String syncExecute2(long timeout) throws Exception {
        return null;
    }


}
