package com.alipay.tsingyan.talk

import com.alipay.tsingyan.BuildInfo
import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.IconConstant
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.UpdateInBackground
import com.intellij.openapi.components.service

/**
 * <p>
 * </p>
 * <AUTHOR>
 * 创建时间 2023-06-22
 */
class GoHelpDocToolWindowAction(private var themeEnum: ThemeEnum) : AnAction("帮助文档", "跳转帮助文档", IconConstant.HELP_ICON), UpdateInBackground{

    /**
     * 点击后跳转到网页版
     */
    override fun actionPerformed(e: AnActionEvent) {
        if (!BuildInfo.isDebug){
            CommonUtils.openUrl(AppConstant.HELP_DOC)
        }else{
            val project = e.project ?: return
            if (BuildInfo.isDebug){
                project.service<WebViewService>().getWebView().getJbCefBrowser().openDevtools()
            }
        }

    }

    override fun update(e: AnActionEvent) {
        if (themeEnum == ThemeEnum.LIGHT) {
            e.presentation.icon = IconConstant.HELP_ICON_LIGHT
        }
    }

}
