package com.alipay.tsingyan.talk

import DiffAction
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.agent.bean.Level2Instruction
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.application.EventBusTopic
import com.alipay.tsingyan.application.EventBusType
import com.alipay.tsingyan.application.ProjectUpdateApplicationListener
import com.alipay.tsingyan.application.TsingYanConfig
import com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable.Companion.isAParAgentEnabledStatic
import com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable.Companion.isExplainCodeEnabledStatic
import com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable.Companion.isRepoQAEnabledStatic
import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.inline2.bean.FastApplyBean
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.MergeTestCodeModel
import com.alipay.tsingyan.model.UserModel
import com.alipay.tsingyan.model.completion.IntentionType
import com.alipay.tsingyan.model.completion.QueryConfigRequestBean
import com.alipay.tsingyan.model.composer.FileActionInfoModel
import com.alipay.tsingyan.model.composer.FileStatus
import com.alipay.tsingyan.model.composer.FileTag
import com.alipay.tsingyan.model.composer.Tag
import com.alipay.tsingyan.model.enums.*
import com.alipay.tsingyan.model.request.UserInfoRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.composer.ComposerService
import com.alipay.tsingyan.services.composer.SnapShotWrapper
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.file.FilePathUtils
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.services.jgit.model.SnapShotClean
import com.alipay.tsingyan.services.jgit.model.SnapShotRecord
import com.alipay.tsingyan.services.jgit.model.SnapShotReset
import com.alipay.tsingyan.services.search.FileSearchService
import com.alipay.tsingyan.services.test.action.TestService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.ui.config.UserState
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.VERSION_UPDATE_STATUS
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.utils.json.JsonUtil
import com.alipay.tsingyan.utils.json.getString
import com.alipay.tsingyan.utils.ui.UIUtils
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.webview.service.JSApiService
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.intellij.diff.DiffManager
import com.intellij.diff.contents.DocumentContentImpl
import com.intellij.diff.contents.FileDocumentContentImpl
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.ide.DataManager
import com.intellij.ide.scratch.ScratchFileService
import com.intellij.ide.scratch.ScratchRootType
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.Presentation
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.keymap.impl.ui.KeymapPanel
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Ref
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.jcef.JBCefJSQuery
import javax.swing.JComponent
import com.jetbrains.rd.util.getThrowableText
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.apache.http.util.TextUtils
import org.jetbrains.concurrency.runAsync
import java.awt.event.InputEvent
import java.io.File
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import java.security.MessageDigest

/**
 * 页面消息接收器
 * <AUTHOR>
 * 创建时间 2023-05-23
 */
class JsMsgRouter(val project: Project) {
    private val LOGGER: Logger = Logger.getInstance(JsMsgRouter::class.java)

    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()

    private val settingData = service<TsingYanSettingStore>()

    /**
     * 本地存储用户信息服务
     */
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    val tracerService = service<CodeFuseTracerService>()

     var agentServiceImpl: AgentService = service<AgentService>()

     val editorManagerService = service<EditorManagerService>()




    fun handleWebSocket(receiveMessage: MessageModel, webView: CodeFuseDialogWebBrowser, traceId: String?) {
        LOGGER.warn("消息处理2" + JSONObject.toJSONString(receiveMessage))
        when (receiveMessage.target) {
            WebTargetEnum.OPEN_CONFIG_CONFIG.name -> {
                if (CommonUtils.isCloudIDE) {
                    //如果是cloudIDE环境, 由于会加载多个iframe,可能短时间内会打开多个网页,所以需要做限制
                    if (System.currentTimeMillis() - AppConstant.OPEN_URL_TIMESTAMP.get() <= 500) {
                        AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                        return
                    }
                    AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                }
                ApplicationManager.getApplication().invokeLater {
                    ShowSettingsUtil.getInstance().showSettingsDialog(project, TsingYanConfig::class.java)
                }
            }

            WebTargetEnum.OPEN_KEYMAP_CONFIG.name -> {
                if (CommonUtils.isCloudIDE) {
                    //如果是cloudIDE环境, 由于会加载多个iframe,可能短时间内会打开多个网页,所以需要做限制
                    if (System.currentTimeMillis() - AppConstant.OPEN_URL_TIMESTAMP.get() <= 500) {
                        AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                        return
                    }
                    AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                }
                ApplicationManager.getApplication().invokeLater() {
                    ShowSettingsUtil.getInstance().showSettingsDialog(project, KeymapPanel::class.java)
                }
            }

            //只有cloudIDE会发这个消息，当ws建立连接成功后，会发送这个消息。
            WebTargetEnum.WEB_INIT_END.name -> {
                var userInfo: UserModel? = null
                //重试3次，防止网络问题导致的失败
                for (i in 0..8) {
                    userInfo = queryUserInfo()
                    if (userInfo != null) {
                        break
                    }
                    Thread.sleep(500)
                }

                if (userInfo != null) {
                    val result = JSONObject()
                    result.put("userInfoModel", userInfo.userInfoModel)
                    result.put("loginStatus", AppConstant.LOGIN_STATUS.get())
                    result.put("queueNum", userInfo.queueNum)
//                    result.put("publicKey", localUserStore.state?.publicKey)
                    val data = JSONObject()
                    data["codegenSwitchCb"] = settingData.state.codegenSwitch
                    data["pluginVersion"] = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                    data["enableGetSelectedCode"] = true
                    data["showH5BottomTips"] = settingData.state.SHOW_H5_BOOTOM_TIPS
                    data["showTestTips"] = settingData.state.SHOW_TEST_TIPS
                    // 获取当前DeepSearch的实际开关状态（服务端开关 && 用户勾选）
                    data["enableRepoDeepSearch"] = isRepoQAEnabledStatic()
                    data["enableExplainDeepSearch"] = isExplainCodeEnabledStatic()
                    data["enableAPDeepSearch"] = isAParAgentEnabledStatic()
                    //当前项目的本地绝对路径
                    data["localPath"] = project.basePath
                    result.put("settings", data)
                    val messageModel = MessageModel()
                    messageModel.actionType = WebActionTypeEnum.TO_JS.name
                    messageModel.target = WebTargetEnum.WEB_INIT_END_IDE.name
                    messageModel.message = JSONObject.toJSONString(result)
                    webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
                    updateConfig()
                }
            }

            WebTargetEnum.SUBMIT_TRACE.name -> {
                val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                val traceType = receiveMessageObject.getString("traceType")
                val traceData = receiveMessageObject.getJSONObject("traceData")
                tracerService.submitTypeStrAndData(project, traceType, traceData)
            }

            WebTargetEnum.INSERT_CODE.name -> {
                getAndInsertCode(receiveMessage)
                tracerService.submitTypeAndData(project, TraceTypeEnum.FUN_INSERTCODE, receiveMessage.message)
            }

            WebTargetEnum.REPLACE_CODE.name -> {
                val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                val code = receiveMessageObject.getString("code").trimEnd()

                ApplicationManager.getApplication().invokeLater {
                    val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
                    if (selectedEditor != null) {
                        if (selectedEditor is TextEditor) {
                            val editor = selectedEditor.editor
                            val document = editor.document
                            val virtualFile = FileDocumentManager.getInstance().getFile(
                                document
                            ) ?: return@invokeLater

                            val selection = editor.selectionModel
                            if (selection.selectionStart == selection.selectionEnd) {
                                NotificationUtils.notifyMessage(
                                    AppConstant.PLUGIN_NAME,
                                    AppConstant.NotificationMessage.MISSING_CODE_SELECTION.message,
                                    project,
                                    null
                                )
                                return@invokeLater
                            }
                            if (document.text.substring(selection.selectionStart, selection.selectionEnd)
                                    .trim() == code
                            ) {
                                NotificationUtils.notifyMessage(
                                    AppConstant.PLUGIN_NAME,
                                    AppConstant.NotificationMessage.NO_REFACTORING_FOR_IDENTICAL_CODE.message,
                                    project,
                                    null
                                )
                                return@invokeLater
                            }
                            //触发补全埋点
                            editorManagerService.mockEditModifiedStart(editor)
                            val newContent = StringBuilder(document.text.subSequence(0, selection.selectionStart))
                                .append(code)
                                .append(document.text.subSequence(selection.selectionEnd, document.text.length))
                                .toString()
                            val newDoc = EditorFactory.getInstance().createDocument(newContent)
                            newDoc.setReadOnly(true)

                            val diffAIVersion = DocumentContentImpl(project, newDoc, virtualFile.fileType)
                            val diffCurrentVersion = FileDocumentContentImpl(project, document, virtualFile)

                            val simpleDiffRequest = SimpleDiffRequest(
                                "CodeFuse AI 编码建议 ", diffAIVersion, diffCurrentVersion,
                                "AI 优化建议", "您的当前代码"
                            )

                            // 创建自定义的 Disposable 来监听关闭事件
                            val diffDisposable = object : Disposable {
                                override fun dispose() {
                                    LogUtil.info("DiffView 已关闭，执行关闭后的业务逻辑")
                                    // 在这里执行您需要的业务逻辑
                                    onDiffViewClosed(receiveMessage, project, editor)
                                }
                            }

                            // 使用 DiffRequestPanel 方式，这样可以监听到关闭事件
                            val diffPanel = DiffManager.getInstance().createRequestPanel(project, diffDisposable, null)
                            diffPanel.setRequest(simpleDiffRequest)

                            // 显示 Diff 对话框
                            val dialogWrapper = object : DialogWrapper(project) {
                                init {
                                    init()
                                    title = "CodeFuse AI 编码建议"
                                    setOKButtonText("应用")
                                    setCancelButtonText("取消")
                                }

                                override fun createCenterPanel(): JComponent {
                                    return diffPanel.component
                                }

                                override fun doOKAction() {
                                    LogUtil.info("用户选择应用差异")
                                    // 在这里可以处理用户选择应用差异的逻辑
                                    onDiffViewAccepted(receiveMessage, project, editor)
                                    super.doOKAction()
                                }

                                override fun doCancelAction() {
                                    LogUtil.info("用户取消差异")
                                    // 在这里可以处理用户取消的逻辑
                                    onDiffViewCancelled(receiveMessage, project, editor)
                                    super.doCancelAction()
                                }
                            }

                            dialogWrapper.show()
                        }
                    }
                }

                tracerService.submitTypeAndData(project, TraceTypeEnum.FUN_REPLACE_CODE, receiveMessage.message)
            }

            WebTargetEnum.COPY_CODE.name -> {
                if (!TextUtils.isEmpty(receiveMessage.message)){
                    tracerService.submitTypeAndData(project, TraceTypeEnum.FUN_COPY, receiveMessage.message)
                }
            }

            WebTargetEnum.REFERENCE_FILE_LIST.name -> {
                val projectBaseDir = project.basePath ?: return
                val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                val fileListArray = receiveMessageObject.getJSONArray("fileList")

                val fileList = fileListArray.toJavaList(JSONObject::class.java)
                val existingFiles = fileList.filter { item ->
                    val filePath = item.getString("filePath")
                    val filterFile = Paths.get(projectBaseDir, filePath).toFile()
                    filePath.isNotEmpty() && filterFile.exists()
                }
                val filteredFileList = JSONArray()
                filteredFileList.addAll(existingFiles)
                val result = JSONObject()
                result["fileList"] = filteredFileList
                val messageModel = MessageModel()
                messageModel.actionType = WebActionTypeEnum.TO_JS.name
                messageModel.target = WebTargetEnum.FILTER_REFERENCE_FILE_LIST.name
                messageModel.message = JSONObject.toJSONString(result)
                LogUtil.debug("REFERENCE_FILE_LIST sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
                webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
            }

//            WebTargetEnum.ERROR_MSG.name -> {
//                val errorMesg = receiveMessage.message
//                val errorCode = JSONObject.parseObject(errorMesg).get("errorCode")
//                if (errorCode != null && errorCode.toString().equals("302")) {
//                    val pubKey = tsingYanProdService.queryAndUpdatePubKey()
//                    if (pubKey != null) {
//                        val pubObj = JSONObject()
//                        pubObj.put(
//                            "publicKey",
//                            pubKey
//                        )
//                        val messageModel = MessageModel()
//                        messageModel.actionType = WebActionTypeEnum.TO_JS.name
//                        messageModel.target = WebTargetEnum.CHANGE_PUBKEY.name
//                        messageModel.message = JSONObject.toJSONString(pubObj)
//                        webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
//                    }
//                }
//                tracerService.submitTypeAndData(project, TraceTypeEnum.ERROR_MSG, receiveMessage.message)
//
//            }

            WebTargetEnum.REFACTOR_CODE.name -> {
                ApplicationManager.getApplication().runReadAction {
                    val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
                    if (selectedEditor != null) {
                        if (selectedEditor is TextEditor) {
                            val editor = selectedEditor.editor
                            val selection = editor.selectionModel
                            if (selection.selectionStart == selection.selectionEnd) {
                                NotificationUtils.notifyMessage(
                                    AppConstant.PLUGIN_NAME,
                                    AppConstant.NotificationMessage.MISSING_CODE_SELECTION.message,
                                    project,
                                    null
                                )
                                return@runReadAction
                            }
                        }
                    }

                    val messageString = receiveMessage.message
                    val messageObject = JSONObject.parseObject(messageString);
                    val suggestion: String = messageObject.getString("suggestion")
                    val chatItemMessage = messageObject.getJSONObject("chatItem" )
                    val messageModel = MessageModel()
                    messageModel.actionType = WebActionTypeEnum.TO_JS.name
                    messageModel.target = WebTargetEnum.CHAT_DATA.name

                    val jsonObject = JSONObject()
                    jsonObject.put("question", "```\n${getSelectedText()} \n```")
                    jsonObject.put("intention", AppConstant.CHAT_INTENTION_REFACTOR_CODE)
                    jsonObject.put("chatIntentionContent", suggestion.trim())
                    jsonObject.put("chatItemMessage", chatItemMessage)
                    jsonObject.put("repo", ProjectCache.getGitData(project))
                    jsonObject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
                    messageModel.message = jsonObject.toString()
                    LogUtil.debug("analysisyToWeb sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
                    webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
                }
            }

            WebTargetEnum.BEGIN_LOGIN.name -> {
                val userInfoModel = localUserStore.getUserInfoModel()
                val oldUserStatus = userInfoModel?.userStatus
                val userModel = queryUserInfo()
                if (userModel != null) {
                    val userModelMessage = beginLogin(userModel)
                    if ((oldUserStatus == null || oldUserStatus.equals(UserStatusEnum.NONE_USER.name)) && userModel.userInfoModel.userStatus.equals(
                            UserStatusEnum.LOGGED_IN.name
                        ) && userModel.userInfoModel.permissionsGpu.equals(PermissionsEnum.NONE.name)
                    ) {
                        NotificationUtils.showNeedPermissionMsg(project)
                    }

                    //登录态改变成登录成功，通知LoginOutWindowAction
                    if ((oldUserStatus == null || oldUserStatus.equals(UserStatusEnum.NONE_USER.name)) && userModel.userInfoModel.userStatus.equals(UserStatusEnum.LOGGED_IN.name)){
                        ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.loginMessage, "login")
                    }

                    LOGGER.info("BEGIN_LOGIN " + JSON.toJSONString(userModelMessage))
                    webView.sendMsgToBrowser(userModelMessage, userModelMessage.target!!, traceId)

                }
            }

            WebTargetEnum.END_LOGIN.name -> {
                changeNotifyToFinishLogin(receiveMessage, project)
            }

            WebTargetEnum.OPEN_URL.name -> {
                if (CommonUtils.isCloudIDE) {
                    //如果是cloudIDE环境, 由于会加载多个iframe,可能短时间内会打开多个网页,所以需要做限制
                    if (System.currentTimeMillis() - AppConstant.OPEN_URL_TIMESTAMP.get() <= 500) {
                        AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                        return
                    }
                    AppConstant.OPEN_URL_TIMESTAMP.set(System.currentTimeMillis())
                }
                CommonUtils.queryTokenAndOpenUrl(receiveMessage.message!!)
                NotificationUtils.changeNotifyByLoginStatus(LoginStatusEnum.LOGINING, project)
            }

            WebTargetEnum.RE_REGISTER.name -> {
                GlobalScope.launch {
                    webView.registerUserObject()
                    val messageModel = MessageModel()
                    messageModel.actionType = WebActionTypeEnum.TO_JS.name
                    messageModel.target = WebTargetEnum.RE_REGISTER.name
                    webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
                }
            }

            WebTargetEnum.WEB_SHOWN_FINISHED.name -> {
                val messageModel = MessageModel()
                messageModel.actionType = WebActionTypeEnum.TO_JS.name
                messageModel.target = WebTargetEnum.SETTING_INIT.name
                val data = JSONObject()
                data["codegenSwitchCb"] = settingData.state.codegenSwitch
                data["pluginVersion"] = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)

                if (AppConstant.IDEA_VERSION.isBlank()){
                    AppConstant.IDEA_VERSION = CommonUtils.getIdeVersion()
                }
                data["ideVersion"] = AppConstant.IDEA_VERSION
                data["enableGetSelectedCode"] = true
                data["showH5BottomTips"] = settingData.state.SHOW_H5_BOOTOM_TIPS
                data["showTestTips"] = settingData.state.SHOW_TEST_TIPS
                data["functionPermissions"] = AppConstant.COMPLETION_CONFIG.functionPermissions
                data["agentExtendInfo"] = AppConstant.COMPLETION_CONFIG.agentExtInfo
                data["productType"] = CommonUtils.getProductType()
                data["repo"] = ProjectCache.getGitData(project)
                data["branch"] = CommonUtils.getBranch(project)
                data["userToken"] = localUserStore.getUserInfoModel()?.userToken
                data["needUpdate"] = AppConstant.NEED_UPDATE
                //当前项目的本地绝对路径
                data["localPath"] = project.basePath
                // 获取当前DeepSearch的实际开关状态（服务端开关 && 用户勾选）
                data["enableRepoDeepSearch"] = isRepoQAEnabledStatic()
                data["enableExplainDeepSearch"] = isExplainCodeEnabledStatic()
                data["enableAPDeepSearch"] = isAParAgentEnabledStatic()
                messageModel.message = data.toJSONString()
                LogUtil.info("WEB_SHOWN_FINISHED sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
                webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
                //登录态改变成登录成功，通知LoginOutWindowAction
                if (UserStatusEnum.LOGGED_IN.name == localUserStore.getUserInfoModel()?.userStatus) {
                    ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.loginMessage, "login")
                } else {
                    ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.loginMessage, "loginOut")
                }
                ApplicationManager.getApplication().invokeLater {
                    sendWebShowFinishedSelectedCodeMsg()
                }
            }

            WebTargetEnum.SETTING_CHANGED.name -> {
                if (receiveMessage.message != null){
                    val codegenSwitch = JSONObject.parseObject(receiveMessage.message).getBoolean("codegenSwitch")
                    settingData.state.codegenSwitch = codegenSwitch
                    ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(
                        EventBusType.settingChangeReceive, receiveMessage.message)

                }
            }

            //获取选中的代码
            WebTargetEnum.GET_SELECTED_CODE.name -> {
                ApplicationManager.getApplication().invokeLater {
                    val messageModel = MessageModel()
                    val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                    val isNeedFullContent = receiveMessageObject.getBoolean("isFullContent")
                    messageModel.actionType = WebActionTypeEnum.TO_JS.name
                    messageModel.target = WebTargetEnum.GET_SELECTED_CODE.name
                    val data = JSONObject()
                    data["code"] = getSelectedText()
                    if (isNeedFullContent){
                        data["fileContent"] = getFileContentText()
                    }
                    data["repo"] = ProjectCache.getGitData(project)
                    data["branch"] = CommonUtils.getBranch(project)
                    data["fileUrl"] = AppConstant.FILE_URL
                    val recentFilesInfo = project.getService(RecentFilesService::class.java)?.getRecentFiles(project)
                    data["recentFilesInfo"] = recentFilesInfo


                    val projectUrl = project.basePath ?:""
                    val currentFileUrl = recentFilesInfo?.currentOpenFile
                    val referenceList = receiveMessageObject.getString("referenceList")?:""
                    val branch = CommonUtils.getBranch(project)
                    val projectGitUrl = ProjectCache.getGitData(project)
                    val level1Instruction = receiveMessageObject.getLong("level1Instruction") ?: 0
                    val level2Instruction = receiveMessageObject.getString("level2Instruction") ?: ""
                    val chatHistory = receiveMessageObject.getString("chatHistory")?:""
                    val isXiaoJinAgent = receiveMessageObject.getBoolean("isXiaoJinAgent") ?: false
                    val sessionId = receiveMessageObject.getString("sessionId") ?: ""
                    val questionUid = receiveMessageObject.getString("questionUid") ?: ""
                    if (!TextUtils.isEmpty(referenceList)) {
                        data["referenceList"] = project.getService(RecentFilesService::class.java)?.getReferenceFileContent(referenceList,project)
                    }else{
                        data["referenceList"] = ""
                    }

                    /**
                     * queryChatRelatedInfo 是个耗时的操作，不要放在UI线程里做这样的操作。
                     */
                    runAsync {
                        val query = receiveMessageObject.getString("query")?:""
                        val time = when{
                            isXiaoJinAgent -> 5
                            CommonUtils.isEnableChatAgent() -> {
                                receiveMessageObject.getInteger("chatAgentTimeout") ?: 10
                            }
                            else -> 0
                        }
                        if (time > 0) {
                            var chatRelatedInfo:JSONObject? = null;
                            try {
                                chatRelatedInfo = agentServiceImpl.queryChatRelatedInfo(projectUrl, query, currentFileUrl, referenceList, recentFilesInfo, branch, projectGitUrl, level1Instruction, JSON.parseObject(level2Instruction,Level2Instruction::class.java), chatHistory,time,sessionId,questionUid)
                            }catch (e:Exception){
                                LogUtil.info("AgentService queryChatRelatedInfo,exception ${e.getThrowableText()}")
                            }
                            if (chatRelatedInfo !=null) {
                                data["agentRag"] = chatRelatedInfo
                            } else {
                                data["agentRag"] = ""
                            }
                        }
                        data["sessionId"] = sessionId
                        data["questionUid"] = questionUid
                        messageModel.message = data.toJSONString()
                        webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
                    }
                }
            }

            //获取选中的代码
            WebTargetEnum.CREATE_NEW_DOCUMENT.name -> {
                ApplicationManager.getApplication().invokeLater {
                    val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                    val fileContent = receiveMessageObject.getString("content")
                    var fileType = receiveMessageObject.getString("fileType")
                    if (TextUtils.isEmpty(fileType)){
                        fileType = ".java"
                    }
                    createScratchFile(project, fileType, fileContent)
                }
            }

            WebTargetEnum.CREATE_TEST_DOCUMENT.name -> {
                if (receiveMessage.message != null){
                    handleCreateTestDocument(receiveMessage.message!!, project)
                }
            }

            //打开对应的文件
            WebTargetEnum.OPEN_FILE.name -> {
                ApplicationManager.getApplication().invokeLater {
                    val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
                    val filePath = receiveMessageObject.getString("filePath")
                    project.service<JSApiService>().navigateToFile(filePath, editorManagerService)
                }
            }

            WebTargetEnum.WRITE_H5_LOG.name -> {
                if (receiveMessage.message != null){
                    val log = JSONObject.parseObject(receiveMessage.message).getString("log")
                    LogUtil.info("H5_LOG: $log")
                }
            }

            WebTargetEnum.OPEN_DINGDING.name -> {
                openDingDingGroup()
            }

            WebTargetEnum.OPEN_XIAOMI.name -> {
                openXiaoMi()
            }

            WebTargetEnum.MODIFY_STORAGE.name -> {
                if (receiveMessage.message != null){
                    val showH5BottomTips = JSONObject.parseObject(receiveMessage.message).getBoolean("showH5BottomTips")
                    if (showH5BottomTips != null && showH5BottomTips != settingData.state.SHOW_H5_BOOTOM_TIPS){
                        LOGGER.info("MODIFY_STORAGE showH5BottomTips ${showH5BottomTips}")
                        settingData.state.SHOW_H5_BOOTOM_TIPS = showH5BottomTips
                    }
                    val showTestTips = JSONObject.parseObject(receiveMessage.message).getBoolean("showTestTips")
                    if (showTestTips != null && showTestTips != settingData.state.SHOW_TEST_TIPS){
                        LOGGER.info("MODIFY_STORAGE showTestTips ${showTestTips}")
                        settingData.state.SHOW_TEST_TIPS = showTestTips
                    }
                }
            }

            WebTargetEnum.CREATE_TEST_CASE.name -> {
                if (receiveMessage.message != null){
                    val caseType = JSONObject.parseObject(receiveMessage.message).getString("caseType")
                    val enterTypeStr = JSONObject.parseObject(receiveMessage.message).getString("enterType")
                    //enterTypeStr 转成ClickType枚举类型
                    var enterType = ClickType.RIGHT
                    when (enterTypeStr) {
                        ClickType.RIGHT.name -> {
                            enterType = ClickType.RIGHT
                        }
                        ClickType.ICON.name -> {
                            enterType = ClickType.ICON
                        }
                        ClickType.BUTTON.name -> {
                            enterType = ClickType.BUTTON
                        }
                        ClickType.INSTRUCT.name -> {
                            enterType = ClickType.INSTRUCT
                        }
                    }
                    when (caseType) {
                        IntentionType.UNIT_TEST.name -> {
                            createTestCase(project, IntentionType.UNIT_TEST, enterType)
                        }
                        IntentionType.INTERFACE_TEST.name -> {
                            createTestCase(project, IntentionType.INTERFACE_TEST, enterType)
                        }
                        else -> {
                            createTestCase(project, IntentionType.AUTO, enterType)
                        }
                    }
                }
            }

            WebTargetEnum.SEND_STATUS_INFO.name -> {
                if (receiveMessage.message != null){
                    val isReading:Boolean = JSONObject.parseObject(receiveMessage.message).getBoolean("chatting")
                    val service = project.service<InlineChatService>()
                    service.isH5ReadingJob = isReading
                }
            }

            WebTargetEnum.SEARCH_FILES_FROM_H5.name -> {
                if (receiveMessage.message != null) {
                    val keyword = JSONObject.parseObject(receiveMessage.message).getString("keywords")
                    val maxCount = JSONObject.parseObject(receiveMessage.message).getIntValue("maxCount")
                    val fileSearchService = project.service<FileSearchService>()
                    val searchFile = fileSearchService.search(keyword,maxCount)
                    project.service<JSApiService>().sendFileSearchMsg(searchFile)
                }
            }

            WebTargetEnum.VERSION_UPDATE.name -> {
                GlobalScope.launch {
                    try {
                        if (VERSION_UPDATE_STATUS == 0) {
                            VERSION_UPDATE_STATUS = 1;
                            val marketInfo = AppConstant.MARKET_INFO
                            var pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                            var latestVersion = marketInfo?.latestVersion
                            if ((ProjectUpdateApplicationListener.Version(latestVersion.toString()).compareTo(ProjectUpdateApplicationListener.Version(pluginVersion))) <= 0) {
                                return@launch
                            }
                            val projectUpdateApplicationListener = ProjectUpdateApplicationListener();
                            projectUpdateApplicationListener.installAndShowMessage(marketInfo!!, AppConstant.UPDATE_SUCCESS_NOTIFY)
                        }
                    }catch (e: Exception){
                        LOGGER.info("update plugin error,", e)
                    }
                }
            }

            WebTargetEnum.QUERY_RECENT_FILE_FROM_H5.name -> {
                val recentFilesService = RecentFilesService()
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId")
                val param = recentFilesService.parseRecentFileParam(receiveMessage.message) ?: return
                project.service<JSApiService>().sendRecentFile(traceId, recentFilesService.getFileRelativePath(project, param.limit, param.onlyProjectFile))
            }

            WebTargetEnum.INJECTED_CONTENT_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId")
                val fileList = JSONObject.parseObject(receiveMessage.message).getString("fileList")
                val listType  = object : TypeToken<List<FileActionInfoModel>>() {}.type
                val files: List<FileActionInfoModel> = Gson().fromJson(fileList, listType)

                files.forEach { file ->
                    if (StringUtils.isBlank(file.filePath)) {
                        file.content = ""
                        return@forEach
                    }
                    val (content,lineCount) =
                        project.service<RecentFilesService>().getFileContentByUrl(file.filePath, project)
                    file.content = content
                    if(lineCount > 0){
                        file.startLine = 1
                        file.endLine = lineCount
                    }else{
                        file.startLine = 0
                        file.endLine = 0
                    }
                }

                project.service<JSApiService>().sendInjectedContent(traceId, files)
            }

            WebTargetEnum.COMPOSER_EXECUTE_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId")
                val fileList = JSONObject.parseObject(receiveMessage.message).getString("fileList")
                val listType  = object : TypeToken<List<FileActionInfoModel>>() {}.type
                val files: List<FileActionInfoModel> = Gson().fromJson(fileList, listType)
                val applyAction = project.service<ComposerService>().acceptOrReject(files)
                // 默认切换焦点到 ToolWindow
                // UIUtils.activeToolWindow(project)
                project.service<JSApiService>().sendComposerAction(traceId, applyAction)
            }


            /**
             * H5 通知IDE，此文件正在Apply
             */
            WebTargetEnum.FILE_APPLY_FROM_H5.name -> {
                // TODO 上下文逻辑此时还不清晰，或许应该限制一下用户的输入输出。
            }

            /**
             * 获取项目基本信息
             */
            WebTargetEnum.PROJECT_INFO_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId") ?: ""
                invokeLater {
                    val result = project.service<ComposerService>().getProjectInfo()
                    project.service<JSApiService>().sendProjectInfo(traceId, result)
                }
            }

            WebTargetEnum.UPDATE_FILE_TAG_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId") ?: ""
                val fileList = JSONObject.parseObject(receiveMessage.message).getString("fileList") ?: return
                val listType  = object : TypeToken<List<FileTag>>() {}.type
                val files: List<FileTag> = Gson().fromJson(fileList, listType)
                val result = project.service<FileAITagService>().putActionToCodeTag(files)
                project.service<JSApiService>().sendFileTagInfo(result, traceId)
            }

            // 文件状态相关操作
            WebTargetEnum.QUERY_FILE_TAG_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId") ?: ""
                val fileList = JSONObject.parseObject(receiveMessage.message).getString("fileList") ?: ""
                val listType  = object : TypeToken<List<FileTag>>() {}.type
                val files: List<FileTag> = Gson().fromJson(fileList, listType)
                val result = project.service<FileAITagService>().queryList(files)
                project.service<JSApiService>().sendFileTagInfo(result, traceId)
            }

            WebTargetEnum.QUERY_FILE_EXIST_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId") ?: ""
                val fileList = JSONObject.parseObject(receiveMessage.message).getString("fileList") ?: ""
                val listType  = object : TypeToken<List<FileStatus>>() {}.type
                val files: List<FileStatus> = Gson().fromJson(fileList, listType)
                files.forEach {
                    it.exist = VfsUtils.vfsFileExist(it.filePath,project)
                }
                project.service<JSApiService>().sendFileStatus(files, traceId)
            }

            WebTargetEnum.APPLY_FILE_FROM_H5.name -> {
                val message = receiveMessage.message
                GlobalScope.launch {
                    val beanType  = object : TypeToken<FastApplyBean>() {}.type
                    val bean: FastApplyBean = Gson().fromJson(message, beanType)
                    project.service<ComposerService>().applyCodeStreamQueue(bean)
                }
            }

            WebTargetEnum.APPLY_STOP_FROM_H5.name -> {
                val traceId = JSONObject.parseObject(receiveMessage.message).getString("traceId") ?: ""
                val list = JSONObject.parseObject(receiveMessage.message).getJSONArray("fileList").toJavaList(String::class.java)
                project.service<ComposerService>().cancelApply(list)
                project.service<JSApiService>().sendCommonResult(traceId,CommonResultEnum.SUCCESS)
            }

            WebTargetEnum.SNAPSHOT_RECORD_TO_IDE.name -> {
                GlobalScope.launch {
                    // 因为我们在 Apply 的时候，使用了队列来做流式，并且流式的时候会有一定延迟。所以这里也尽量等一下，避免在做快照的时候，文件还未添加到 Git 暂存区。
                    // @see com.alipay.tsingyan.services.composer.ComposerService.applyCodeStreamQueue
                    delay(150)
                    val bean = JsonUtil.parseObject(receiveMessage.message!!, SnapShotRecord::class.java)
                    project.service<SnapShotWrapper>().doSnapShot(bean)
                }
            }

            WebTargetEnum.SNAPSHOT_RESET_TO_IDE.name -> {
                GlobalScope.launch {
                    val bean = JsonUtil.parseObject(receiveMessage.message!!, SnapShotReset::class.java)
                    project.service<SnapShotWrapper>().historyReview(bean)
                }
            }

            WebTargetEnum.SNAPSHOT_RESET_VERIFY_TO_IDE.name -> {
                GlobalScope.launch {
                    val bean = JsonUtil.parseObject(receiveMessage.message!!, SnapShotReset::class.java)
                    project.service<SnapShotWrapper>().resetSnapshot(bean)
                }
            }

            WebTargetEnum.SNAPSHOT_CLEAN_TO_IDE.name -> {
                GlobalScope.launch {
                    val bean = JsonUtil.parseObject(receiveMessage.message!!, SnapShotClean::class.java)
                    project.service<SnapShotWrapper>().cleanSnap(bean)
                }
            }

            // 获取当前编辑器内正在使用的文件
            WebTargetEnum.GET_SELECT_EDITOR_FILE_FROM_H5.name -> {
                val traceId = JsonUtil.parseObject(receiveMessage.message)?.getString("traceId") ?: ""
                GlobalScope.launch {
                    runInEdt {
                        val selectEditorFile = project.service<RecentFilesService>().getSelectEditorFile(project)
                        project.service<JSApiService>().sendSelectEditorFile(traceId,selectEditorFile)
                    }
                }
            }

            WebTargetEnum.REFRESH_WEB.name -> {
                LOGGER.info("前端触发刷新界面")
                invokeLater {
                    UIUtils.refreshWeb(project)
                }
            }
        }
    }

    private fun sendWebShowFinishedSelectedCodeMsg() {
        try {
            if (!project.isDisposed) {
                val editor = FileEditorManager.getInstance(project).selectedTextEditor
                if (editor!=null&&AntEditorUtil.isSelectedEditor(editor)) {
                    if (editorManagerService.checkLogin() && editor.selectionModel.hasSelection()) {
                        val selectedCode = editor.selectionModel.selectedText
                        val virtualFile = FileDocumentManager.getInstance().getFile(editor.document)
                        val language = AntEditorUtil.getLanguage(editor)
                        val startOffset = editor.selectionModel.selectionStart
                        val endOffset = editor.selectionModel.selectionEnd
                        val startLine = editor.document.getLineNumber(startOffset) + 1
                        val endLine = editor.document.getLineNumber(endOffset) + 1
                        //获取文件相对路径
                        val filePath =
                            FilePathUtils.getRelativePath(virtualFile!!.path, project.basePath!!)
                        GlobalScope.launch {
                           //为了等待H5页面加载完成，延迟1s发送消息
                            delay(1000)
                            project.service<JSApiService>()
                                    .selectedCodeMsg(filePath, virtualFile?.name, selectedCode, language, startLine, endLine)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogUtil.info("sendWebShowFinishedSelectedCodeMsg error:" + e.getThrowableText())
        }
    }

    /**
     * 消息处理函数
     */
    fun handle(message: String): JBCefJSQuery.Response {
        LOGGER.info("received message:$message")
        val webView = project.getService(WebViewService::class.java).getWebView()
        val receiveMessage: MessageModel = JSON.parseObject(message, MessageModel::class.java)
        try {
            handleWebSocket(receiveMessage, webView, null)

        } catch (e: Exception) {
            LogUtil.info("消息处理异常 message:$message",e)
        } finally {
            return JBCefJSQuery.Response("OK", 0, "PASS")
        }
    }


    fun changeNotifyToFinishLogin(receiveMessage: MessageModel, project: Project) {
        val message = receiveMessage.message
        if (message == null || message.length == 0) {
            return
        }
        var type = JSONObject.parseObject(message).getIntValue("type")
        if (type == 3) {
            NotificationUtils.changeNotifyByLoginStatus(LoginStatusEnum.LOGIN_FINISH, project)
            updateConfig()
        } else {
            NotificationUtils.changeNotifyByLoginStatus(LoginStatusEnum.LOGIN_TIMEOUT, project)
        }
    }

    private fun getAndInsertCode(receiveMessage: MessageModel){
        val message = receiveMessage.message
        if (message == null || message.length == 0) {
            return
        }
        val code = JSONObject.parseObject(message).getString("code")
        if (code == null || code.length == 0) {
            return
        }
        ApplicationManager.getApplication().invokeLater {
            val editor = FileEditorManager.getInstance(project).selectedTextEditor
            if (editor == null) {
                return@invokeLater
            }
            editorManagerService.mockEditModifiedStart(editor)
            WriteCommandAction.runWriteCommandAction(project) {
                    LOGGER.info("getAndInsertCode editor...")
                    editor?.let {
                        val document = it.document
                        val caretModel = it.caretModel
                        val offset = caretModel.offset
                        LOGGER.info("getAndInsertCode start")
                        document.insertString(offset, code)
                        LOGGER.info("getAndInsertCode end")
                    }

                    ApplicationManager.getApplication().invokeLater {
                        editorManagerService.mockEditModifiedEnd(editor)
                    }
                }
            }
    }

    private fun getSelectedText(): String{
        var selectedText:String = ""
        ApplicationManager.getApplication().runReadAction {
            val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
            if (selectedEditor != null) {
                if (selectedEditor is TextEditor) {
                    val selectionModel = selectedEditor.editor.selectionModel
                    selectedText = selectionModel.selectedText ?: ""
                }
            }
        }

        return selectedText
    }


    private fun getFileContentText(): String{
        var fileContent:String = ""
        ApplicationManager.getApplication().runReadAction {
            val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
            if (selectedEditor != null) {
                if (selectedEditor is TextEditor) {
                    fileContent = selectedEditor.editor.document.text
                }
            }
        }

        return fileContent
    }

    /**
     * 网页侧点击登陆按钮.idea需要做
     * 1. 查询用户最新数据
     * 2. 改变右下角通知的状态
     */
    fun beginLogin(userModel: UserModel): MessageModel {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.REGIST_USER.name
        messageModel.message = JSON.toJSONString(userModel)
        return messageModel
    }


    fun queryUserInfo(): UserModel? {
        val userInfoRequestBean = UserInfoRequestBean()
        userInfoRequestBean.ideVersion = CommonUtils.getIdeVersion()
        userInfoRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        var userState = localUserStore.state
        if (userState != null) {
            userInfoRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        } else {
            userState = UserState()
        }
        var userModel = tsingYanProdService.queryUserInfo(userInfoRequestBean) ?: return null
        localUserStore.setUserInfoModel(userState, userModel.userInfoModel)
        localUserStore.setUserState(userState)
        return userModel
    }

    fun queryKeyShorts(): String? {
        // 获取ActionManager实例
        val actionManager = ActionManager.getInstance()

        // 获取指定ID的action对象
        val key = actionManager.getKeyboardShortcut("codefuseApply")

        LOGGER.info("key: " + key)
        if (key != null) {
            LOGGER.info("firstKeyStroke: " + key.firstKeyStroke)
            return key.firstKeyStroke.toString()
        }

        return null
    }

    private fun openDingDingGroup(){
        ApplicationManager.getApplication().invokeLater {
            CommonUtils.openUrl(AppConstant.COMPLETION_CONFIG.dingdingGroupUrl)
        }
    }

    private fun openXiaoMi(){
        ApplicationManager.getApplication().invokeLater {
            CommonUtils.openUrl(AppConstant.COMPLETION_CONFIG.developerHelpUrl)
        }
    }

    private fun createScratchFile(project: Project, fileSuffix: String, fileContent:String): Boolean {
        // 创建 Scratch 文件的配置
        val scratchFileService = ScratchFileService.getInstance()

        // 创建 Scratch 文件，并打开编辑器中
        val currentTime = System.currentTimeMillis().toString()
        // 检查fileSuffix是否已经包含点（.`）
        val finalSuffix = if (fileSuffix.startsWith(".")) fileSuffix else ".$fileSuffix"
        val fileName = "temp$currentTime$finalSuffix"

        val scratchFileName: Ref<String> = Ref.create(fileName) // 名字可以随便取
        val scratchRootType = ScratchRootType.getInstance()
        val scratchFile =
            scratchFileService.findFile(scratchRootType, scratchFileName.get(), ScratchFileService.Option.create_if_missing)

        // 文件打开后的回调 （写入内容到文件）
        WriteCommandAction.runWriteCommandAction(project) {
            val document: Document? =
                FileDocumentManager.getInstance().getDocument(scratchFile!!)
            if (document != null) {
                document.insertString(0, fileContent)
            }
            // 打开新创建的 Scratch 文件
            FileEditorManager.getInstance(project).openFile(scratchFile, true)
            ApplicationManager.getApplication().invokeLater {
                val editor = FileEditorManager.getInstance(project).selectedTextEditor
                if (editor != null) {
                    editorManagerService.mockEditModifiedEnd(editor)
                }
            }
        }

        return true
    }

    private fun createTestCase(project: Project, intentionType: IntentionType, enterType: ClickType){
        LogUtil.info("createTestCase enterType: $enterType, intentionType: $intentionType", false)
        ApplicationManager.getApplication().invokeLater {
            val selectedText: String = getSelectedText()
            var fileContent:String = ""
            var startOffset = 0
            var endOffset = 0
            var startLine = 0
            var endLine = 0
            ApplicationManager.getApplication().runReadAction {
                val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
                if (selectedEditor != null) {
                    if (selectedEditor is TextEditor) {
                        fileContent = selectedEditor.editor.document.text
                        val selectionModel = selectedEditor.editor.selectionModel
                        startOffset = selectionModel.selectionStart
                        endOffset = selectionModel.selectionEnd
                        val document = selectedEditor.editor.document
                        startLine = document.getLineNumber(startOffset)
                        endLine = document.getLineNumber(endOffset)
                    }
                }
            }
            val language = AntEditorUtil.getLanguage(getCurrentEditor(project)!!)
            project.service<TestService>().generateUnitTest(
                project,
                selectedText,
                fileContent,
                language,
                enterType,
                intentionType,
                startOffset,
                endOffset,
                startLine,
                endLine,
                false
            )
        }
    }

    private fun getCurrentEditor(project: Project): Editor? {
        val fileEditorManager = FileEditorManager.getInstance(project)
        return fileEditorManager.selectedTextEditor
    }

    fun getFileContent(filePath: String): String? {
        val virtualFile = LocalFileSystem.getInstance().findFileByPath(filePath)
        if (virtualFile != null) {
            val document = FileDocumentManager.getInstance().getDocument(virtualFile)
            if (document != null) {
                return document.text
            }
        }
        return null
    }


    private fun getMergeCode(sourceContent:String, gptGenContent:String, fileType:String): String {
        val mergeModel = MergeTestCodeModel()
        mergeModel.ideVersion = CommonUtils.getIdeVersion()
        mergeModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        mergeModel.userToken = localUserStore.getUserInfoModel()?.userToken
        mergeModel.originalContent = sourceContent
        mergeModel.gptGenContent = gptGenContent
        mergeModel.lang = fileType
        val mergeStr = tsingYanProdService.getMergeTestCode(mergeModel) ?: return ""
        return mergeStr
    }

    private fun createDiffAction(project: Project, sourceContent:String, mergedContent:String, ext:String, testFilePath: String, receiveMessageObject: JSONObject){
        val editor = getCurrentEditor(project)
        if (editor != null && !editor.isDisposed){
            //根据editor获取路径
            editorManagerService.mockEditModifiedStart(editor)
        }
        val diffAction = DiffAction(project, sourceContent, mergedContent, ext, testFilePath, receiveMessageObject)
        val dataContext = DataManager.getInstance().getDataContext()
        diffAction.actionPerformed(
            AnActionEvent(
                null as InputEvent?,
                dataContext,
                "EditorPopup",
                Presentation(),
                ActionManager.getInstance(),
                0
            )
        )
    }

    private fun findFileInPath(path: String, fileName: String): String? {
        val virtualFile = LocalFileSystem.getInstance().findFileByPath(path) ?: return null
        return findFileRecursively(virtualFile, fileName)
    }

    private fun findFileRecursively(directory: VirtualFile, fileName: String): String? {
        directory.children.forEach { file ->
            when {
                file.isDirectory -> findFileRecursively(file, fileName)?.let { return it }
                file.name == fileName -> return file.path
            }
        }
        return null
    }

    private fun getTestRootPathFromSource(sourceFile: VirtualFile): String? {
        val testDirectoryPath = sourceFile.parent.path.replace("src/main", "src/test")
        LogUtil.info("CREATE_TEST_DOCUMENT 1 testDirectoryPath:$testDirectoryPath", false)
        val testIndex = testDirectoryPath.indexOf("src/test")

        return if (testIndex != -1) {
            testDirectoryPath.substring(0, testIndex + "src/test".length)
        } else {
            null // 根据需要进行处理
        }
    }

    private val EndTestSuffix = "Test"
    private fun handleCreateTestDocument(receiveMessage: String, project: Project) {
        val receiveMessageObject = JSONObject.parseObject(receiveMessage)
        val fileContent = receiveMessageObject.getString("content")
        var fileType = receiveMessageObject.getString("fileType") ?: ".java"
        val filePath = receiveMessageObject.getString("filePath")

        val sourceFile: VirtualFile?
        //CloudIde H5那边取到的值是相对路径 需要转换
        if (!filePath.startsWith(project.basePath!!)) {
            sourceFile = LocalFileSystem.getInstance().findFileByPath(project.basePath + File.separatorChar + filePath)
        } else {
            sourceFile = LocalFileSystem.getInstance().findFileByPath(filePath)
        }
        if (sourceFile == null || !sourceFile.isValid) {
            return
        }
        LogUtil.info("CREATE_TEST_DOCUMENT 1 sourceFile:$sourceFile")

        //判断file的名称是不是Test结尾
        if (!TextUtils.isEmpty(sourceFile.nameWithoutExtension) && sourceFile.nameWithoutExtension.endsWith(EndTestSuffix) && CommonUtils.isInTestBundle(filePath)) {
            val file = File(sourceFile.path)
            fileType = fileType.removePrefix(".")
            LogUtil.info("CREATE_TEST_DOCUMENT 1.1 findFilePath:${sourceFile.path} fileType:$fileType")
            if (file.exists()) {
                // 当文件被Composer占用时，不做处理
                val virtualFile = LocalFileSystem.getInstance().findFileByIoFile(file)
                if (null != virtualFile && !project.service<FileAITagService>().isTextToCodeEnable(virtualFile)){
                    val fileTag = FileTag("CodeFuse", Tag.ACTION_TO_CODE)
                    project.service<JSApiService>().sendFileTagInfo(listOf(fileTag))
                    return
                }

                val readFileToString = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
                val mergedContent = getMergeCode(readFileToString, fileContent, fileType).takeIf { it.isNotEmpty() } ?: fileContent
                ApplicationManager.getApplication().invokeLater {
                    createDiffAction(project, readFileToString, mergedContent, "", sourceFile.path, receiveMessageObject)
                }
            }
            return
        }

        val fileTypeNew = if (fileType.startsWith(".")) fileType else ".$fileType"
        val testFileName = sourceFile.nameWithoutExtension + EndTestSuffix + fileTypeNew
        val testRootPath = getTestRootPathFromSource(sourceFile)
        LogUtil.info("CREATE_TEST_DOCUMENT 1 testRootPath:$testRootPath", false)
        if (testRootPath == null) {
            ApplicationManager.getApplication().invokeLater {
                NotificationUtils.notifyMessage("CodeFuse", "未找到该文件对应的测试目录src/test", project, null)
            }
            return
        }
        ensureDirectoryExists(testRootPath, project)

        val findFilePath = findFileInPath(testRootPath, testFileName)
        if (findFilePath == null) {
            val testDirectoryPath = sourceFile.parent.path.replace("src/main", "src/test")
            ensureDirectoryExists(testDirectoryPath, project)

            val testDirectory = LocalFileSystem.getInstance().refreshAndFindFileByPath(testDirectoryPath)
            if (testDirectory == null || !testDirectory.isValid || !testDirectory.isDirectory) {
                return
            }

            val testFilePath = "${testDirectory.path}${CommonUtils.getFileSeparator(testDirectory.path)}$testFileName"
            LogUtil.info("CREATE_TEST_DOCUMENT 2 testFilePath:$testFilePath")
            WriteCommandAction.runWriteCommandAction(project) {
                try {
                    val testFile = testDirectory.createChildData(this, testFileName)
                    // 检查fileContent是否包含包名，如果不包含，则添加包名
                    val packageName = calculatePackageName(testDirectoryPath)
                    var updatedFileContent = fileContent
                    if (!TextUtils.isEmpty(packageName)){
                        updatedFileContent = if (!fileContent.startsWith("package")) {
                            "package $packageName;\n\n$fileContent"
                        } else {
                            fileContent
                        }
                    }

                    VfsUtil.saveText(testFile, updatedFileContent)
                } catch (e: IOException) {
                    LogUtil.info("CREATE_TEST_DOCUMENT 2 error", e)
                }
            }
            ApplicationManager.getApplication().invokeLater {
                project.service<JSApiService>().navigateToFile(testFilePath, editorManagerService, "test")
            }
        } else {
            val file = File(findFilePath)
            fileType = fileType.removePrefix(".")
            LogUtil.info("CREATE_TEST_DOCUMENT 3 findFilePath:$findFilePath fileType:$fileType")
            if (file.exists()) {
                // 当文件被Composer占用时，不做处理
                val virtualFile = LocalFileSystem.getInstance().findFileByIoFile(file)
                if (null != virtualFile && !project.service<FileAITagService>().isTextToCodeEnable(virtualFile)){
                    val fileTag = FileTag("CodeFuse", Tag.ACTION_TO_CODE)
                    project.service<JSApiService>().sendFileTagInfo(listOf(fileTag))
                    return
                }

                val readFileToString = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
                val mergedContent = getMergeCode(readFileToString, fileContent, fileType).takeIf { it.isNotEmpty() } ?: fileContent
                ApplicationManager.getApplication().invokeLater {
                    createDiffAction(project, readFileToString, mergedContent, "", findFilePath, receiveMessageObject)
                }
            }
        }
    }

    private fun calculatePackageName(testDirectoryPath: String): String {
        val fileSeparator = CommonUtils.getFileSeparator(testDirectoryPath)
        val splitStr = "src${fileSeparator}test${fileSeparator}java"
        val srcTestIndex = testDirectoryPath.indexOf(splitStr)
        return if (srcTestIndex != -1) {
            testDirectoryPath.substring(srcTestIndex + splitStr.length + 1).replace(fileSeparator, ".")
        } else {
            ""
        }
    }

    private fun ensureDirectoryExists(path: String, project: Project) {
        val directory = LocalFileSystem.getInstance().findFileByPath(path)
        if (directory == null || !directory.isValid) {
            WriteCommandAction.runWriteCommandAction(project) {
                try {
                    val dirFile = File(path)
                    if (!dirFile.exists()) {
                        dirFile.mkdirs()
                    }
                } catch (e: IOException) {
                    LogUtil.info("CREATE_TEST_DOCUMENT ensureDirectoryExists error", e)
                }
            }
        }
    }

    // 计算 MD5 哈希值的扩展函数
    private fun String.md5(): String {
        val thisWithoutNewline = this.replace("\n", "")
        val bytes = MessageDigest.getInstance("MD5").digest(thisWithoutNewline.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun updateConfig(){
        GlobalScope.launch {
            //update Config
            val queryConfigRequestBean = QueryConfigRequestBean()
            queryConfigRequestBean.ideVersion = CommonUtils.getIdeVersion()
            queryConfigRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
            queryConfigRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
            AppConstant.COMPLETION_CONFIG = tsingYanProdService.queryConfig(queryConfigRequestBean)
            AppConstant.CONFIG_STR_HASH = JSON.toJSONString(AppConstant.COMPLETION_CONFIG).md5()
            AppConstant.INLINE_CHAT_ENABLE = CommonUtils.isEnableInlineChat()
        }
    }

    /**
     * DiffView 关闭后的回调方法
     */
    private fun onDiffViewClosed(receiveMessage: MessageModel, project: Project, editor: Editor) {
        LogUtil.info("onDiffViewClosed")
        editorManagerService.mockEditModifiedEnd(editor)
    }

    /**
     * 用户点击应用差异时的回调方法
     */
    private fun onDiffViewAccepted(receiveMessage: MessageModel, project: Project, editor: Editor) {
        try {
            // 应用代码变更的逻辑
            val receiveMessageObject = JSONObject.parseObject(receiveMessage.message)
            val code = receiveMessageObject.getString("code")?.trimEnd()

            if (!code.isNullOrEmpty()) {
                ApplicationManager.getApplication().invokeLater {
                    val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
                    if (selectedEditor is TextEditor) {
                        val editor = selectedEditor.editor
                        val document = editor.document
                        val selection = editor.selectionModel

                        WriteCommandAction.runWriteCommandAction(project) {
                            document.replaceString(selection.selectionStart, selection.selectionEnd, code)
                        }
                        editorManagerService.mockEditModifiedEnd(editor)
                        LogUtil.info("onDiffViewAccepted")
                    }
                }
            }
        } catch (e: Exception) {
            LogUtil.info("onDiffViewAccepted failed", e)
        }
    }

    /**
     * 用户取消差异时的回调方法
     */
    private fun onDiffViewCancelled(receiveMessage: MessageModel, project: Project, editor: Editor) {
        LogUtil.info("onDiffViewCancelled")
        editorManagerService.mockEditModifiedEnd(editor)
    }
}

