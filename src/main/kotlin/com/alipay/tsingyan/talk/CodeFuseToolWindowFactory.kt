package com.alipay.tsingyan.talk

import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory


/**
 * codefuse对话框
 * <AUTHOR>
 * 创建时间 2023-05-23
 */
class CodeFuseToolWindowFactory : ToolWindowFactory, DumbAware {
    var historyPageAction: HistoryPageAction? = null
    var refreshAction:RefreshAction? = null
    var goHelpToolWindowAction:GoHelpDocToolWindowAction? = null
    var openSettingAction:OpenSettingAction? = null
    var feedBackAction:FeedBackAction? = null
    var theme:ThemeEnum = ThemeEnum.DARK
    var moreActionsButton : AnAction? = null


    /**
     * toolwindow初始化
     * 在header区域新建两个按钮
     */
    override fun init(toolWindow: ToolWindow) {
        theme = UIThemeListener.getTheme()
        historyPageAction = HistoryPageAction(theme)
        openSettingAction = OpenSettingAction(theme)
        feedBackAction = FeedBackAction(theme)
        goHelpToolWindowAction = GoHelpDocToolWindowAction(theme)

        refreshAction = RefreshAction(theme)

        // 创建三点菜单
        val moreActionsGroup = createMoreActionsGroup()
        moreActionsButton = MoreAction(theme, moreActionsGroup)


        val key = Key.findKeyByName("DontHideToolbarInHeader")
        LogUtil.debug("CodeFuseToolWindowFactory key is $key")
        key?.let { toolWindow.component.putClientProperty(key, true) }
        // 添加刷新按钮和三点菜单到标题栏
        toolWindow.setTitleActions(listOf(historyPageAction, openSettingAction, feedBackAction, goHelpToolWindowAction, moreActionsButton))
    }


    override fun isApplicable(project: Project): Boolean {
        return true;
    }

    /**
     * 创建包含多个操作的ActionGroup
     */
    private fun createMoreActionsGroup(): DefaultActionGroup {
        return DefaultActionGroup().apply {
            add(refreshAction!!)
        }
    }

    /**
     * Create the tool window content.
     *
     * @param project    current project
     * @param toolWindow current tool window
     */
    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val contentManager = toolWindow.contentManager
        val labelContent = contentManager.factory.createContent(CodeFuseDialogPanel(project).getDialogContent(), null, false)
        contentManager.addContent(labelContent)
    }


}