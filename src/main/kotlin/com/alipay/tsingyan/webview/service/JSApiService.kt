package com.alipay.tsingyan.webview.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.composer.FileActionInfoModel
import com.alipay.tsingyan.model.composer.FileStatus
import com.alipay.tsingyan.model.composer.FileTag
import com.alipay.tsingyan.model.composer.ProjectInfoModel
import com.alipay.tsingyan.model.enums.CommonResultEnum
import com.alipay.tsingyan.model.enums.TABKey
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.composer.ui.ComposerTextAttributes
import com.alipay.tsingyan.services.file.FilePathUtils
import com.alipay.tsingyan.services.jgit.model.SnapShotType
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.util.FileSearchVo
import com.alipay.tsingyan.utils.CommonUtils
import com.google.gson.Gson
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.OpenFileDescriptor
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import kotlinx.coroutines.asCoroutineDispatcher
import org.apache.http.util.TextUtils
import java.io.File
import java.util.concurrent.Executors
import javax.swing.SwingUtilities

/**
 *
 * author:jianzhi
 * date:2024-01-24 21:13:03
 *
 */
class JSApiService(project: Project) : Disposable {
    private val myProject: Project = project
    private val webViewService = project.service<WebViewService>()
    private val dispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()

    override fun dispose() {
        dispatcher.close()
    }

    private fun jobRun(runnable: java.lang.Runnable) {
        dispatcher.dispatch(dispatcher, runnable)
    }

    fun switchFileMsg(newFilePath: String, newFileOffSet: Int, oldFilePath: String, oldFileOffset: Int) {
        jobRun { sendSwitchMsg(newFilePath, newFileOffSet, oldFilePath, oldFileOffset) }
    }

    fun selectedCodeMsg(
        filePath: String?,
        fileName: String?,
        code: String?,
        language: String,
        startLine: Int,
        endLine: Int,
    ) {
        jobRun { sendSelectedCodeMsg(filePath, fileName, code, language, startLine, endLine) }
    }

    private fun sendSwitchMsg(newFilePath: String, newFileOffSet: Int, oldFilePath: String, oldFileOffset: Int) {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.SWITCH_FILE_MSG.name
        val jsonObject = JSONObject()
        jsonObject.put("newFilePath", convertPathAbsoluteToRelative(newFilePath))
        jsonObject.put("newFileOffSet", newFileOffSet)
        jsonObject.put("oldFilePath", convertPathAbsoluteToRelative(oldFilePath))
        jsonObject.put("oldFileOffset", oldFileOffset)
        messageModel.message = jsonObject.toString()
        LogUtil.info("sendSwitchMsg sendMsgToBrowser ${JSON.toJSONString(messageModel)}", false)
        val webView = myProject.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
        }
    }

    private fun convertPathAbsoluteToRelative(newFilePath: String): String {
        return if (newFilePath.isNotEmpty()) {
            FilePathUtils.getRelativePath(newFilePath, myProject.basePath!!)
        } else {
            ""
        }
    }

    private fun sendSelectedCodeMsg(
        filePath: String?,
        fileName: String?,
        code: String?,
        language: String,
        startLine: Int,
        endLine: Int,
    ) {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.SELECTED_CODE.name
        val jsonObject = JSONObject()
        jsonObject.put("filePath", filePath)
        jsonObject.put("fileName", fileName)
        if (TextUtils.isEmpty(code)) {
            jsonObject.put("code", "")
        } else {
            jsonObject.put("code", code)
        }
        jsonObject.put("language", language)
        if (startLine != 0) {
            jsonObject.put("startLine", startLine)
            jsonObject.put("endLine", endLine)
        }
        messageModel.message = jsonObject.toString()
//        LogUtil.info("sendSelectedCodeMsg sendMsgToBrowser ${JSON.toJSONString(messageModel)}", false)
        val webView = myProject.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
        }
    }

    fun navigateToFile(filePath: String, editorManagerService: EditorManagerService, type:String? = null) {
        var targetFile = LocalFileSystem.getInstance().findFileByPath(filePath)
        // 如果文件不存在，尝试使用相对路径查找
        if (targetFile == null || !targetFile.exists()) {
            val projectBaseDir = myProject.basePath ?: return
            val newFilePath = projectBaseDir + File.separator + filePath
            LogUtil.info("navigateToFile filePath: $newFilePath", false)
            targetFile = LocalFileSystem.getInstance().findFileByPath(newFilePath)
        }

        if (targetFile != null && targetFile.exists()) {
            // 创建一个 OpenFileDescriptor 对象
            val descriptor = OpenFileDescriptor(myProject, targetFile, -1)
            // 使用 FileEditorManager 来打开文件编辑器并执行跳转
            val openTextEditorList = FileEditorManager.getInstance(myProject).openEditor(descriptor, false)
            if (openTextEditorList.size > 0 && openTextEditorList[0] is TextEditor) {
                val editor: Editor = (openTextEditorList[0] as TextEditor).editor
                updateUI(editor)
                if (!TextUtils.isEmpty(type) && "test".contentEquals(type)){
                    editorManagerService.mockEditModifiedEnd(editor)
                }
            }
        }
    }

    private fun updateUI(editor: Editor) {
        // 判断一下是否有高亮的地方，如果有，跳转到对应位置
        val highlighter = editor.markupModel.allHighlighters
            .filter {
                it.textAttributes is ComposerTextAttributes
            }
            .minByOrNull {
                it.startOffset
            }
        if (null != highlighter) {
            invokeLater {
                editor.scrollingModel.scrollTo(
                    editor.offsetToLogicalPosition(highlighter.startOffset),
                    ScrollType.CENTER_UP
                )
            }
        } else {
            invokeLater {
                editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
            }
        }
    }

    fun sendFileSearchMsg(searchFile: List<FileSearchVo>) {
        jobRun {
            sendFileSearch(searchFile)
        }

    }

    private fun sendFileSearch(searchFile: List<FileSearchVo>) {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.SEARCH_FILES_TO_H5.name
        messageModel.message = JSONObject.toJSONString(searchFile)
        val webView = myProject.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
        }
    }


    fun sendRecentFile(traceId: String?, recentFiles: List<String>) {
        jobRun {
            doSendRecentFile(traceId, recentFiles)
        }
    }

    private fun doSendRecentFile(traceId: String?, recentFiles: List<String>) {
        val result = JSONObject()
        result.put("traceId", traceId)

        val jsonArray = JSONArray()
        for (file in recentFiles) {
            val fileObj = JSONObject()
            fileObj.put("filePath", file)
            jsonArray.add(fileObj)
        }

        result.put("fileList", jsonArray)
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.QUERY_RECENT_FILE_TO_H5.name
        messageModel.message = result.toJSONString()
        val webView = myProject.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
        }
    }

    fun sendInjectedContent(traceId: String?, files: List<FileActionInfoModel>) {
        jobRun {
            doSendInjectedContent(traceId, files)
        }
    }

    private fun doSendInjectedContent(traceId: String?, files: List<FileActionInfoModel>) {
        val result = JSONObject()
        result.put("traceId", traceId)
        result.put("fileList", files)
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.INJECTED_CONTENT_TO_H5.name
        messageModel.message = result.toJSONString()
        val webView = myProject.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
        }
    }

    fun sendComposerAction(traceId: String?, files: List<FileActionInfoModel>) {
        jobRun {
            doSendComposerAction(traceId, files)
        }
    }

    fun sendProjectInfo(traceId: String?, projectInfo: ProjectInfoModel) {
        jobRun {
            projectInfo.traceId = traceId
            val messageModel = MessageModel()
            messageModel.actionType = WebActionTypeEnum.TO_JS.name
            messageModel.target = WebTargetEnum.PROJECT_INFO_TO_H5.name
            messageModel.message = Gson().toJson(projectInfo)
            val webView = myProject.getService(WebViewService::class.java).getWebView()
            if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
                webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
            }
        }
    }


    /**
     * 切换对话框 Tab
     */
    fun sendSwitchTab(tabKey: TABKey, traceId: String? = null) {
        commonSend(
            mapOf(
                "tabKey" to tabKey.realName,
            ),
            WebTargetEnum.SWITCH_TAB,
            traceId
        )
    }

    /**
     * 增加文件标签
     */
    fun sendFileTagInfo(fileList: List<FileTag>, traceId: String? = null) {
        commonSend(
            mapOf(
                "traceId" to traceId,
                "fileList" to fileList
            ),
            WebTargetEnum.QUERY_FILE_TAG_TO_H5,
            traceId
        )
    }

    /**
     * 发送主题变更
     */
    fun sendThemeChanges(name: String) {
        LogUtil.info("检测到主题变更 $name")
        commonSend(
            "{\"theme\": \"$name\"}",
            WebTargetEnum.CHANGE_THEME
        )
    }

    /**
     * 发送文件状态
     */
    fun sendFileStatus(files: List<FileStatus>, traceId: String? = null) {
        LogUtil.info("发送文件状态")
        commonSend(
            mapOf(
                "fileList" to files,
                "traceId" to traceId,
            ),
            WebTargetEnum.QUERY_FILE_EXIST_TO_H5,
            traceId
        )
    }

    /**
     * 发送 FastApply 后的文件状态
     */
    fun sendApplyStatus(
        traceId: String,
        fileUrl: String,
        status: String,
        currAddLines: Int? = null,
        currDelLines: Int? = null,
        totalAddLines: Int? = null,
        totalDelLines: Int? = null,
    ) {

        LogUtil.info("发送单个文件应用状态")
        commonSend(
            mapOf(
                "traceId" to traceId,
                "fileUrl" to fileUrl,
                "status" to status,
                "currentDiff" to mapOf(
                    "removeLines" to currDelLines,
                    "addLines" to currAddLines,
                ),
                "totalDiff" to mapOf(
                    "removeLines" to totalDelLines,
                    "addLines" to totalAddLines,
                ),
            ),
            WebTargetEnum.APPLY_FILE_TO_H5,
            traceId
        )
    }

    /**
     * 发送快照信息
     */
    fun sendSnapResult(
        traceId: String,
        sessionId: String,
        commitId: String,
        type: SnapShotType,
    ) {
        commonSend(
            mapOf(
                "traceId" to traceId,
                "sessionId" to sessionId,
                "commitId" to commitId,
                "type" to type,
                "branch" to "main",
            ),
            WebTargetEnum.SNAPSHOT_RECORD_TO_H5,
            traceId
        )
    }

    /**
     * 发送通用异常信息
     *  extend 扩展信息
     */
    fun sendCommonResult(
        traceId: String,
        status: CommonResultEnum = CommonResultEnum.ERROR,
        desc: String? = null,
        extend: Any? = null,
    ) {
        commonSend(
            mapOf(
                "traceId" to traceId,
                "status" to status,
                "desc" to desc,
                "extend" to extend,
            ),
            WebTargetEnum.COMMON_RESULT,
            traceId
        )
    }

    /**
     * 获取选中的编辑器
     */
    fun sendSelectEditorFile(traceId: String, filePath: String?) {
        commonSend(
            mapOf(
                "traceId" to traceId,
                "filePath" to filePath,
            ),
            WebTargetEnum.GET_SELECT_EDITOR_FILE_TO_H5,
            traceId
        )
    }

    private fun doSendComposerAction(traceId: String?, files: List<FileActionInfoModel>) {
        val result = mapOf(
            "traceId" to traceId,
            "fileList" to files,
        )
        commonSend(result, WebTargetEnum.COMPOSER_EXECUTE_FROM_IDE, traceId)
    }


    /**
     * 通用发送逻辑
     */
    private fun commonSend(
        msgMap: Map<String, Any?>,
        type: WebTargetEnum,
        traceId: String? = null,
    ) {
        commonSend(Gson().toJson(msgMap), type, traceId)
    }

    /**
     * 通用发送逻辑
     */
    private fun commonSend(
        msg: String,
        type: WebTargetEnum,
        traceId: String? = null,
    ) {
        LogUtil.info("type：[$type] ,msg:[$msg]")
        jobRun {
            val messageModel = MessageModel()
            messageModel.actionType = WebActionTypeEnum.TO_JS.name
            messageModel.target = type.name
            messageModel.message = msg

            val webView = webViewService.getWebView()
            if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
                webView.sendMsgToBrowser(messageModel, messageModel.target!!, traceId)
            }
        }
    }
}