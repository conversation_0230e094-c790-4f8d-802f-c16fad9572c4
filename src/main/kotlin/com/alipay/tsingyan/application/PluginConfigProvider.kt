package com.alipay.tsingyan.application

import com.alipay.tsingyan.ui.TsingYanSettingComponent
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ui.UIUtils
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.options.ConfigurableProvider
import org.jetbrains.concurrency.runAsync
import javax.swing.JComponent

/**
 * 插件配置信息
 */
class PluginConfigProvider : ConfigurableProvider() {
    override fun createConfigurable(): Configurable = TsingYanConfig()
}

/**
 * 青燕配置
 */
class TsingYanConfig : Configurable {
    private val settingComponent = TsingYanSettingComponent()
    private val settingData = service<TsingYanSettingStore>()


    override fun createComponent(): JComponent? {
        return settingComponent.mainPanel
    }

    override fun isModified(): Boolean {
        return settingComponent.autoUpdateCb?.isSelected != settingData.state.autoUpdatePluginSwitch
                || settingComponent.codegenSwitchCb?.isSelected != settingData.state.codegenSwitch ||
                settingComponent.enableLineMarkerCb?.isSelected != settingData.state.enableLineMarkerSwitch ||
                settingComponent.codegenSecureCb?.isSelected != settingData.state.codegenSecureSwitch ||
                AppConstant.commitLanguageType != settingData.state.COMMIT_MSG_LANGUAGE_SELECTOR ||
                AppConstant.commitMsgLength != settingData.state.COMMIT_MSG_LEHGTH_SELECTOR  ||
                AppConstant.annotationLanguageType != settingData.state.ANNOTATION_LANGUAGE_SELECTOR ||
                AppConstant.aiDialogLanguageType != settingData.state.AI_DIALOG_LANGUAGE_SELECTOR||
                settingComponent.floatingBarShowCb?.isSelected != settingData.state.enableFloatingBarShowCb ||
                settingComponent.codeEditCb?.isSelected != settingData.state.codeEditSwitch ||
                settingComponent.autoFix?.isSelected != settingData.state.enableAutoFix ||
                settingComponent.repoQACheckbox?.isSelected != settingData.state.enableRepoQASearch ||
                settingComponent.explainCodeCheckbox?.isSelected != settingData.state.enableExplainCodeSearch ||
                settingComponent.aParAgentCheckbox?.isSelected != settingData.state.enableAParAgentSearch
    }

    override fun apply() {
        settingData.state.codegenSecureSwitch = settingComponent.codegenSecureCb?.isSelected ?: true
        settingData.state.autoUpdatePluginSwitch = settingComponent.autoUpdateCb?.isSelected ?: true
        settingData.state.enableLineMarkerSwitch = settingComponent.enableLineMarkerCb?.isSelected ?: true
        settingData.state.enableFloatingBarShowCb = settingComponent.floatingBarShowCb?.isSelected ?: true
        settingData.state.enableLineMarkerSwitch = AppConstant.ENABLE_LINE_MARKER
        settingData.state.COMMIT_MSG_LANGUAGE_SELECTOR = AppConstant.commitLanguageType
        settingData.state.COMMIT_MSG_LEHGTH_SELECTOR = AppConstant.commitMsgLength
        settingData.state.ANNOTATION_LANGUAGE_SELECTOR = AppConstant.annotationLanguageType
        settingData.state.AI_DIALOG_LANGUAGE_SELECTOR = AppConstant.aiDialogLanguageType

        if (settingData.state.codegenSwitch != settingComponent.codegenSwitchCb?.isSelected){
            settingData.state.codegenSwitch = settingComponent.codegenSwitchCb?.isSelected ?: true
            ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.settingChange, "{\"codegenSwitchCb\":${settingData.state.codegenSwitch}}")
        }
        settingData.state.codeEditSwitch = settingComponent.codeEditCb?.isSelected ?: true
        if (settingComponent.autoFix?.isSelected != settingData.state.enableAutoFix){
            settingData.state.enableAutoFix = (settingComponent.autoFix?.isSelected ?: false)
            UIUtils.doUpdateCefDetect(settingData.state.enableAutoFix)
            runAsync {
                // 不能直接放在当前线程内执行，否者右下角的通知会消失
                NotificationUtils.showAutoFix()
            }
        }
        
        // 保存搜索增强选项
        settingData.state.enableRepoQASearch = settingComponent.repoQACheckbox?.isSelected ?: true
        settingData.state.enableExplainCodeSearch = settingComponent.explainCodeCheckbox?.isSelected ?: false
        settingData.state.enableAParAgentSearch = settingComponent.aParAgentCheckbox?.isSelected ?: false
        
        // 调用搜索增强设置保存方法
        settingComponent.saveSearchEnhancementSettings()
    }

    override fun getDisplayName(): String {
        return AppConstant.PLUGIN_NAME
    }

}