package com.alipay.tsingyan.application


import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.config.VersionControlsStore
import com.alipay.tsingyan.mcp.MCPCoreService
import com.alipay.tsingyan.model.completion.QueryConfigRequestBean
import com.alipay.tsingyan.model.enums.PermissionsEnum
import com.alipay.tsingyan.model.enums.UserStatusEnum
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.model.request.UserInfoRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.input.InputTrackingService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.ui.config.UserState
import com.alipay.tsingyan.util.SystemEnv
import com.alipay.tsingyan.utils.*
import com.intellij.concurrency.JobScheduler
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ApplicationNamesInfo
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * project级监听器
 */
internal class ProjectManagerListener : ProjectManagerListener {
    private val LOGGER: Logger = Logger.getInstance(ProjectManagerListener::class.java)

    /**
     * 青燕prod远程服务
     */
    var tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    var inputTrackingService: InputTrackingService = service<InputTrackingService>()

    var agentService: AgentService = service<AgentService>()

    /**
     * 本地存储用户信息服务
     */
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    val settingData = service<TsingYanSettingStore>()
    val tracerService = service<CodeFuseTracerService>()
    var versionControl = service<VersionControlsStore>()
    var mcpCoreService = service<MCPCoreService>()


    /**
     * project打开时触发
     */
    override fun projectOpened(project: Project) {
        LOGGER.info("codefuse init start projectOpened ....")
        GlobalScope.launch {
            // 下个版本删掉
            backupVersionData()
            initData(project)
            initConfigAndStartSchedule(project)
            try {
                if (CommonUtils.isSupportMcpFeature()) {
                    mcpCoreService.init()
                }
            } catch (e: Throwable) {
                LOGGER.info("mcpCoreService init error", e)
            }
        }
        disableFullLineCompletion()
    }

    override fun projectClosed(project: Project) {
        LOGGER.info("codefuse projectClosed ....")
        try {
            inputTrackingService.disableTracking(project)
        } catch (e: Throwable) {
            LOGGER.info("InputTrackingService disableTracking error", e)
        }
    }

    /**
     * 初始化配置文件并开启定时任务
     */
    fun initConfigAndStartSchedule(project: Project) {
        val queryConfigRequestBean = QueryConfigRequestBean()
        queryConfigRequestBean.ideVersion = CommonUtils.getIdeVersion()
        queryConfigRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        queryConfigRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        AppConstant.COMPLETION_CONFIG = tsingYanProdService.queryConfig(queryConfigRequestBean)
        AppConstant.CONFIG_STR_HASH = JSON.toJSONString(AppConstant.COMPLETION_CONFIG).md5()
        AppConstant.INLINE_CHAT_ENABLE = CommonUtils.isEnableInlineChat()
        AppConstant.IS_CLOUD_IDE_211 = false
        LogUtil.info("init config success ${JSONObject.toJSONString(AppConstant.COMPLETION_CONFIG)}")

        // 安全过滤提醒需要放到用户配置查询成功后，否则有几率拿不到用户白名单信息
        // 未开启安全过滤，允许提醒，安全过滤白名单用户 弹出开启安全提醒
        if (!settingData.state.codegenSecureSwitch &&!settingData.state.SECURE_NO_NOTICE_CLICKED
                &&CommonUtils.isEnableSecureWhiteList()){
            NotificationUtils.showSecureMessage(project)
        }
        agentService.initProject(project)
        var delayTime = AppConstant.COMPLETION_CONFIG.intervalTime.toLong()
        if (CommonUtils.isCloudIDE){
            delayTime = delayTime*2
        }

        JobScheduler
            .getScheduler()
            .scheduleWithFixedDelay(
                {
                    try {
                        AppConstant.COMPLETION_CONFIG = tsingYanProdService.queryConfig(queryConfigRequestBean)
                        val newHash = JSON.toJSONString(AppConstant.COMPLETION_CONFIG).md5()
                        if (newHash != AppConstant.CONFIG_STR_HASH) {
                            // 发现不相同，通知H5对话页
                            val webView = project.getService(WebViewService::class.java).getWebView()
                            val messageModel = MessageModel()
                            messageModel.actionType = WebActionTypeEnum.TO_JS.name
                            messageModel.target = WebTargetEnum.CONFIG_CHANGED.name
                            webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
                        }
                        AppConstant.CONFIG_STR_HASH = newHash

                        AppConstant.INLINE_CHAT_ENABLE = CommonUtils.isEnableInlineChat()
                    } catch (e: Throwable) {
                        LOGGER.info("tsingyan schedule error", e)
                    }
                },
                delayTime,
                delayTime,
                TimeUnit.MILLISECONDS
            )

//        try {
//            project.service<PromptCacheService>().updateCodeSnipCache()
//        } catch (e: Throwable) {
//            LOGGER.info("initConfigAndStartSchedule init error", e)
//        }
    }

    /**
     * 初始化时更新pubkey和用户信息
     */
    fun initData(project: Project) {
        val userInfoRequestBean = UserInfoRequestBean()
        userInfoRequestBean.ideVersion = CommonUtils.getIdeVersion()
        userInfoRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        //一、获取缓存的token
        //缓存的一个userState包含三部分
        //userState包含三部分，
        //1、publicKey公钥
        //2、userInfoStr用户对象String
        //3、queueNum排队人数
        var userState = localUserStore.state
        //假如没有缓存的，直接初始化为一个空的。
        if (userState == null) {
            userState = UserState()
        }
        userInfoRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        //如果是cloudIDE环境,需要从配置文件里读取到工号,然后把工号当作userToken,并且insertUserFlag置为true.让服务端看到即使是没见过的token,会进行插入操作
        if (CommonUtils.isCloudIDE) {
            val parseRunTimeInfo = CommonUtils.parseRunTimeInfo()
            if (parseRunTimeInfo != null && parseRunTimeInfo.ownerUserId != null) {
                userInfoRequestBean.userToken = parseRunTimeInfo.ownerUserId
                userInfoRequestBean.insertUserFlag = true
            }
        }

        //二、获取公钥，如果没有token就是null
//        val publicKeyModel = queryPubKey(userInfoRequestBean)
        //三、获取用户信息，如果没有token就是null,会返回一个token
        val userInfo = tsingYanProdService.queryUserInfo(userInfoRequestBean) ?: return
//        userState.publicKey = publicKeyModel!!.publicKey
        localUserStore.setUserInfoModel(userState, userInfo.userInfoModel)
        userState.queueNum = userInfo.queueNum

        if (userInfo.userInfoModel?.userStatus == null || userInfo.userInfoModel.userStatus == UserStatusEnum.NONE_USER.name) {
            NotificationUtils.showBeginLoginMessage(project)
            ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.loginMessage, "loginOut")
        } else if (userInfo.userInfoModel.permissionsGpu == PermissionsEnum.NONE.name) {
            NotificationUtils.showNeedPermissionMsg(project)
        }

        if (userInfo.userInfoModel?.userStatus == UserStatusEnum.LOGGED_IN.name){
            // 登录态是正常，需要发消息给LoginOutWindowAction
            ApplicationManager.getApplication().messageBus.syncPublisher(EventBusTopic.MyTopic).onMessage(EventBusType.loginMessage, "login")
        }
        localUserStore.setUserState(userState)
        LOGGER.info("用户信息" + JSONObject.toJSONString(userState.userInfoStr))
        LOGGER.info("codegenSwitch " + settingData.state.codegenSwitch)
        if (!settingData.state.codegenSwitch) {
            tracerService.submitInitData()
        }

        //https://yuque.antfin-inc.com/iikq97/ukuoxm/ayvqr0hwci5enpv8
        // 仅新用户首次安装插件时弹出提醒，以后更新、升级都不再提示
        val state = settingData.state
        LogUtil.info("initData state.NOTIFY_V012_HAS_CLICKED " + state.NOTIFY_V012_HAS_CLICKED, false)
        if (state != null && !state.NOTIFY_V012_HAS_CLICKED) {
            //弹出0122版本的提醒
            NotificationUtils.showVersionMessage(project)
            state.NOTIFY_V012_HAS_CLICKED = true
        }
        //固定变量初始化
        try {
            ProjectCache.saveGitData(project, CommonUtils.getProjectGitRepository(project))
            AppConstant.IDEA_FULL_NAME = ApplicationNamesInfo.getInstance().fullProductName
            AppConstant.IDEA_VERSION = CommonUtils.getIdeVersion()
            AppConstant.osArchEnum = CommonUtils.detectOsArch()
            AppConstant.osTypeEnum = CommonUtils.detectOsType()
            val repo = CommonUtils.getProjectGitRepository(project)
            LogUtil.info("init data repoStr ${repo}, ideaName ${AppConstant.IDEA_FULL_NAME}")
            LogUtil.info("init data branch ${CommonUtils.getBranch(project)}")
        } catch (e: Throwable){
            LOGGER.info("tsingyan init error", e)
        }
    }


//    /**
//     * 由于公钥很重要,所以需要重试三次
//     */
//    fun queryPubKey(userInfoRequestBean: UserInfoRequestBean): PublicKeyModel? {
//        try {
//            for (i in 0..3) {
//                val productToken = tsingYanProdService.queryPubKey(userInfoRequestBean)
//                if (productToken?.publicKey != null) {
//                    return productToken
//                }
//                Thread.sleep(2000)
//            }
//            return null
//        } catch (e: Throwable) {
//            LOGGER.info("tsingyan queryPubKey error", e)
//            return null
//        }
//    }

    fun backupVersionData(){
        val hasBackup:Boolean = settingData.state.HAS_BACKUP_VERSION_STATE
        if (hasBackup){
            return
        }
        //备份逻辑
        val hasClick:Boolean = versionControl.state?.NOTIFY_V012_HAS_CLICKED == true
        if (hasClick){
            settingData.state.NOTIFY_V012_HAS_CLICKED = true
        }
        //设置标记，已经备份过了
        settingData.state.HAS_BACKUP_VERSION_STATE = true
    }

    private fun disableFullLineCompletion() {
        try {
            val hasSet:Boolean = settingData.state.HAS_SET_FLCC_ENBALE
            if (!hasSet && CommonUtils.is241Version()) {
                LogUtil.info("disableFullLineCompletion " + ApplicationInfo.getInstance().apiVersion)
                val pluginId = PluginId.findId("org.jetbrains.completion.full.line") ?: return
                val fullLinePlugin = PluginManager.getInstance().findEnabledPlugin(pluginId) ?: return
                LogUtil.info("disableConflictPlugin pluginClassLoader $fullLinePlugin")
                SystemEnv.setEnv("flcc_enable", "false")
                SystemEnv.setEnv("flcc_evaluating", "true")
                LogUtil.info("disableConflictPlugin flcc_enable ${System.getenv("flcc_enable")}")
                LogUtil.info("disableConflictPlugin flcc_enable ${System.getenv("flcc_evaluating")}")
            }
        } catch (exception: Throwable) {
            LOGGER.info("disableConflictPlugin error ", exception)
            tracerService.submitException(exception)
        } finally {
            settingData.state.HAS_SET_FLCC_ENBALE = true
        }
    }


    // 计算 MD5 哈希值的扩展函数
    private fun String.md5(): String {
        val thisWithoutNewline = this.replace("\n", "")
        val bytes = MessageDigest.getInstance("MD5").digest(thisWithoutNewline.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    fun isCloudIDE211(): Boolean {
        return CommonUtils.isCloudIDE && CommonUtils.is211Version()
    }

}
