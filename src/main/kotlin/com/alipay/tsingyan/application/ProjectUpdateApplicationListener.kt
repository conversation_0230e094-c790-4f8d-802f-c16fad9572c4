package com.alipay.tsingyan.application

import cn.hutool.core.codec.Base64
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.TypeReference
import com.alipay.tsingyan.config.PluginUpdateModel
import com.alipay.tsingyan.config.UpdateStatus
import com.alipay.tsingyan.model.completion.QueryConfigRequestBean
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.VERSION_UPDATE_STATUS
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.TaskExecutor
import com.alipay.tsingyan.utils.http.HttpClient
import com.alipay.tsingyan.view.jcef.socket.JWebSocketService
import com.google.gson.JsonArray
import com.google.gson.JsonParser
import com.intellij.ide.AppLifecycleListener
import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.ide.startup.StartupActionScriptManager
import com.intellij.notification.NotificationListener
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.PathManager
import com.intellij.openapi.application.ex.ApplicationManagerEx
import com.intellij.openapi.components.NamedComponent
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.project.ProjectManager
import com.jetbrains.rd.util.getThrowableText
import org.apache.commons.io.FileUtils
import org.apache.http.util.TextUtils
import java.io.File
import java.net.URL
import java.util.*
import kotlin.concurrent.thread


/**
 * 插件自动升级
 */
class ProjectUpdateApplicationListener : AppLifecycleListener, NamedComponent {
    private val LOGGER: Logger = Logger.getInstance(ProjectUpdateApplicationListener::class.java)


    val IDE_ADDRESS = "http://ide.alipay.net"

    val PLUGIN_CHANNEL_ADDRESS = "$IDE_ADDRESS/plugin/channel?plugin="

    /**
     * 线上包channel名字
     */
    val CHANNEL_RELEASE = "release"

    /**
     * 插件配置数据
     */
    var setting: TsingYanSettingStore = service<TsingYanSettingStore>()
    /**
     * 本地存储用户信息服务
     */
    var localUserStore: LocalUserStore = service<LocalUserStore>()

    /**
     * 青燕prod远程服务
     */
    var tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()

    /**
     * 此处不能直接使用AppConstant.DATA_URL（还未赋值）
     */
    private val tsingyanFile =
        File(System.getProperty("user.home") + File.separator + "." + AppConstant.PLUGIN_NAME.lowercase() + File.separator + "data" + File.separator + "CodeFuse.zip")

    val tracerService = service<CodeFuseTracerService>()



    init {
        FileUtils.deleteQuietly(tsingyanFile)
        val file =
            File(System.getProperty("user.home") + File.separator + "." + AppConstant.PLUGIN_NAME.lowercase() + File.separator + "data")
        if(!file.exists()){
            file.mkdirs()
        }
    }


    /**
     * 启动时检查是否需要自动更新
     */
    override fun appStarted() {
        LogUtil.info("ProjectUpdateApplicationListener appStarted .....", false)
        updateClientID()
        val updateJob = Runnable {
            try {
                if (CommonUtils.isCloudIDE){
                    return@Runnable
                }

                //1、判断插件版本是否需要更新
                val currentPluginVersion = CommonUtils.getPluginVersion("")
                if (currentPluginVersion.trim().isEmpty()) {
                    return@Runnable
                }
                var marketInfo = getLastReleasePluginInfo()
                //加个兜底逻辑
                LogUtil.info("ProjectUpdateApplicationListener appStarted getLastReleasePluginInfo .....marketInfo $marketInfo", false)
                if (marketInfo == null){
                    marketInfo = getLastReleasePluginInfo2()
                    LogUtil.info("ProjectUpdateApplicationListener appStarted getLastReleasePluginInfo2 .....marketInfo $marketInfo", false)
                }
                if (marketInfo == null){
                    LogUtil.info("ProjectUpdateApplicationListener appStarted marketInfo == null", false)
                    return@Runnable
                }
                val marketVersion = Version(marketInfo.latestVersion)
                val currentVersion = Version(currentPluginVersion)
                AppConstant.MARKET_INFO = marketInfo
                LogUtil.info("ProjectUpdateApplicationListener appStarted .....1 ${marketInfo.latestVersion} ${currentPluginVersion} ", false)
                val shouldUpdate = marketVersion.compareTo(currentVersion) > 0
                AppConstant.NEED_UPDATE = shouldUpdate
                LogUtil.info("ProjectUpdateApplicationListener appStarted .....2 ${shouldUpdate}", false)
                if (!shouldUpdate) {
                    return@Runnable
                }

                //2、从配置接口获取pluginStatus的值
                val pluginUpdateModel = queryPluginUpdateModel() ?: return@Runnable
                LogUtil.info("ProjectUpdateApplicationListener appStarted .....3 " + JSON.toJSONString(pluginUpdateModel), false)
                doUpdatePlugin(marketInfo, pluginUpdateModel)

                LogUtil.info("ProjectUpdateApplicationListener appStarted .....4 end", false)

            } catch (e:Throwable){
                LOGGER.info("ProjectUpdateApplicationListener appStarted .....error", e)
                submitUpdateError(e)
            }
        }

        TaskExecutor.submitTask(updateJob)

        //启动应用级的项目
        thread(true, name = "SocketServer") {
            service<JWebSocketService>().start()
        }
    }

    fun queryPluginUpdateModel(): PluginUpdateModel?{
        val queryConfigRequestBean = QueryConfigRequestBean()
        queryConfigRequestBean.ideVersion = CommonUtils.getIdeVersion()
        queryConfigRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        queryConfigRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        val configModel  = tsingYanProdService.queryConfig(queryConfigRequestBean)
        return configModel.pluginUpdateModel
    }

    /**
     * 获取最近一个release包信息
     * 启动阶段没有预热，可能会超时，所以重试3次
     */
    fun getLastReleasePluginInfo(pluginId: String = AppConstant.PLUGIN_ID): PluginChannelInfo? {
        val url = "${PLUGIN_CHANNEL_ADDRESS}$pluginId&build=${ApplicationInfo.getInstance().build}"
        for (index in 0 until 3) {
            try {
                val resultStr = HttpClient.get(url).syncExecute(AppConstant.COMPLETION_CONFIG.timeOut)
                if (resultStr == null) {
                    Thread.sleep(1000)
                    continue
                }
                val pluginChannelInfoList =
                    JSONObject.parseObject(resultStr, object : TypeReference<PluginChannelResponse>() {}).data
                if (pluginChannelInfoList == null || pluginChannelInfoList.size == 0) {
                    Thread.sleep(1000)
                    return continue
                }
                return pluginChannelInfoList.firstOrNull { it.channelName.equals(CHANNEL_RELEASE, true) }
            } catch (ex: Throwable) {
                Thread.sleep(1000)
                LogUtil.info("ProjectUpdateApplicationListener getLastReleasePluginInfo .....error", ex)
                continue

            }
        }
        return null
    }

    fun getLastReleasePluginInfo2(pluginId: String = AppConstant.PLUGIN_ID): PluginChannelInfo? {
        val url = "${PLUGIN_CHANNEL_ADDRESS}$pluginId&build=${ApplicationInfo.getInstance().build}"
        for (index in 0 until 3) {
            try {
                val resultStr = HttpClient.get(url).syncExecute(AppConstant.COMPLETION_CONFIG.timeOut)
                if (resultStr == null) {
                    Thread.sleep(1000)
                    continue
                }

                val jsonObject = JsonParser.parseString(resultStr).asJsonObject
                if (jsonObject.has("data")) {
                    val jsonArray: JsonArray = jsonObject.getAsJsonArray("data")
                    if (jsonArray.size() > 0) {
                        // 创建PluginChannelInfo对象
                        val pluginChannelInfo = PluginChannelInfo()
                        val dataJSON = jsonArray.get(0).asJsonObject
                        pluginChannelInfo.channelName = dataJSON["channelName"].asString
                        pluginChannelInfo.channelDescription = dataJSON["channelDescription"].asString
                        pluginChannelInfo.latestVersion = dataJSON["latestVersion"].asString
                        pluginChannelInfo.downloadURL = dataJSON["downloadURL"].asString
                        pluginChannelInfo.releaseTime = Date(dataJSON["releaseTime"].asLong)
                        return pluginChannelInfo
                    }
                }
            } catch (ex: Throwable) {
                Thread.sleep(1000)
                LogUtil.info("ProjectUpdateApplicationListener getLastReleasePluginInfo2 .....error", ex)
                continue
            }
        }
        return null
    }

    /**
     * 开始更新
     */
    private fun doUpdatePlugin(channel: PluginChannelInfo, pluginUpdateModel: PluginUpdateModel) {
        when (pluginUpdateModel.updateStatus) {
            UpdateStatus.FORCE -> {
                if (TextUtils.isEmpty(pluginUpdateModel.notifyMsg)){
                    pluginUpdateModel.notifyMsg = AppConstant.UPDATE_SUCCESS_NOTIFY
                }
                installAndShowMessage(channel, pluginUpdateModel.notifyMsg)
            }
            UpdateStatus.AUTO -> {
                if (setting.state.autoUpdatePluginSwitch){
                    installAndShowMessage(channel, AppConstant.UPDATE_SUCCESS_NOTIFY)
                } else {
                    NotificationUtils.notifyMessage("CodeFuse插件 更新通知",
                        "CodeFuse有新版本，请 <a href=\"update\">现在升级</a> <br/>或 <a href=\"later\">稍后</a>",
                        ProjectManager.getInstance().defaultProject,
                        NotificationListener { notification, event ->
                            notification.expire()
                            if (event.description.contains("update")) {
                                installAndShowMessage(channel, AppConstant.UPDATE_SUCCESS_NOTIFY)
                            }
                        })
                }
            }
            UpdateStatus.NONE -> {}
        }
    }

    /**
     * 安装并且显示用户通知
     */
     fun installAndShowMessage(channel: PluginChannelInfo, notifyMessage: String?) {
        VERSION_UPDATE_STATUS = 1
        ApplicationManager.getApplication().executeOnPooledThread {
            val isSuccess = installFromPluginChannel(channel, tsingyanFile)
            if (isSuccess) {
                NotificationUtils.notifyMessage("CodeFuse插件 自动更新通知",
                    "$notifyMessage, <a href=\"restart\">重启IDEA</a> 生效<br/>或 <a href=\"later\">稍后</a> 重启自动生效",
                    ProjectManager.getInstance().defaultProject,
                    NotificationListener { notification, event ->
                        notification.expire()
                        if (event.description.contains("restart")) {
                            ApplicationManagerEx.getApplicationEx().restart(true)
                        }
                    })
            }
        }
    }


    private fun installFromPluginChannel(channel: PluginChannelInfo, tsingyanFile: File): Boolean {
        try {
            getPluginInfo()?.let { pluginInfo ->
                    //旧版本存在，删除旧版本，并下载新版本
                    if (tsingyanFile.exists()){
                        FileUtils.deleteQuietly(tsingyanFile)
                    }

                    FileUtils.copyURLToFile(URL(channel.downloadURL), tsingyanFile)

                    pluginInfo.installFile?.let { StartupActionScriptManager.DeleteCommand(it.toPath()) }?.let {
                        StartupActionScriptManager.addActionCommand(
                            it
                        )
                    }
                    StartupActionScriptManager.addActionCommand(
                        StartupActionScriptManager.UnzipCommand(
                            tsingyanFile.toPath(),
                            File(PathManager.getPluginsPath()).toPath()
                        )
                    )
                    StartupActionScriptManager.addActionCommand(StartupActionScriptManager.DeleteCommand(tsingyanFile.toPath()))
                }

                LOGGER.info("ProjectUpdateApplicationListener CodeFuse Plugin Installed:  version: ${channel.latestVersion}, location: ${PathManager.getPluginsPath()}")
                return true
        } catch (e: Exception) {
            LOGGER.info("ProjectUpdateApplicationListener CodeFuse Prepare for install plugin fail", e)
        }
        return false
    }

    @Synchronized
    fun getPluginInfo(pluginId: String = AppConstant.PLUGIN_ID): PluginInfo? {
        PluginManagerCore.getPlugin(PluginId.getId(pluginId))?.let { pluginDescriptor ->
            val pluginInfo = PluginInfo()
            pluginInfo.installFile = pluginDescriptor.path
            val versionAndChannel = pluginDescriptor.version.split("-", limit = 2)
            pluginInfo.version = versionAndChannel[0]
            return pluginInfo
        }
        return null
    }

    class PluginInfo(
        var version: String = "",
        var installFile: File? = null,
    )

    /**
     * 描述 plugin channel 相应的版本信息等
     */
    class PluginChannelInfo(
        var channelId: Int = 0,
        var channelName: String = "",
        var channelDescription: String = "",
        var latestVersion: String = "",
        var downloadURL: String = "",
        var releaseTime: Date? = null,
        var docLink: String? = ""
    )

    /**
     * ide.alipay.net返回值
     */
    class PluginChannelResponse(
        var result: Boolean = false,
        var data: List<PluginChannelInfo> = listOf()
    )

    private fun submitUpdateError(e:Throwable){
        val jsonObject = JSONObject()
        jsonObject["errorMsg"] = e.getThrowableText()
        jsonObject["errorType"] = "UPDATE_ERROR"
        tracerService.submitTypeAndData(TraceTypeEnum.UPDATE_ERROR, jsonObject.toJSONString())
    }

    /**
     * 更新客户端ID
     * 考虑物理机和容器环境，不使用
     */
    private fun updateClientID(){
        try {
            val settingStore = service<TsingYanSettingStore>()
            val state = settingStore.state
            if (StrUtil.isBlank(state.CLIENT_ID)){
                state.CLIENT_ID = Base64.encode(DateUtil.now())
                settingStore.loadState(state)
            }
        } catch (e: Exception) {
            LOGGER.error(e)
        } catch (t : Throwable){
            LOGGER.error(t)
        }
    }

    class Version(private val version: String) : Comparable<Version> {
        override fun compareTo(other: Version): Int {
            val thisParts = this.version.split(".")
            val otherParts = other.version.split(".")
            val maxIndex = Math.max(thisParts.size, otherParts.size)
            for (i in 0 until maxIndex) {
                val thisPart = if (i < thisParts.size) thisParts[i].toInt() else 0
                val otherPart = if (i < otherParts.size) otherParts[i].toInt() else 0
                if (thisPart < otherPart) return -1
                if (thisPart > otherPart) return 1
            }
            return 0 // 如果全部相等，则返回0
        }

        override fun toString(): String {
            return version
        }
    }
}