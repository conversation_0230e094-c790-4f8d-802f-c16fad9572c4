package com.alipay.tsingyan.application

import com.intellij.util.messages.Topic

interface EventBusListener {
    fun onMessage(msgType: EventBusType, data: String?)
}

class EventBusTopic {
    companion object {
        val MyTopic = Topic.create("codefuse.eventbus", EventBusListener::class.java)
    }
}

enum class EventBusType {
    settingChange, // idea的设置修改
    settingChangeReceive, //H5修改设置被接受
    loginMessage // 登录消息
}
