package com.alipay.tsingyan.floating.service

import com.alipay.tsingyan.inline2.compontent.FloatingToolBar
import com.alipay.tsingyan.inline2.theme.ColorManager
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.talk.UIThemeListener
import com.alipay.tsingyan.util.CodeFuseToolBarHintUtils
import com.alipay.tsingyan.utils.AppConstant
import com.intellij.codeInsight.hint.HintManager
import com.intellij.codeInsight.hint.HintManagerImpl
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.util.Key
import java.util.concurrent.atomic.AtomicReference


@Service(Service.Level.PROJECT)
class FloatingToolBarService : Disposable {

    private val toolBarAlarm = CancellableAlarm(this)

    private val floatingToolBarHint: Key<FloatingToolBar> = Key.create("Ant.floatingToolBarHint")

    var theme = UIThemeListener.getTheme()
    var themeColor = ColorManager.getColor(theme)
    var lastUpdateTime = System.currentTimeMillis()

    fun blockHint(editor: Editor) {
        toolBarAlarm.cancelAllRequests()
        if (floatingToolBarHint[editor] != null && floatingToolBarHint[editor].hint != null) {
            floatingToolBarHint[editor].hint!!.hide()
        }
        AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW = AtomicReference(false)
    }

    fun floatingToolBarShowOrHide(event: SelectionEvent) {
        toolBarAlarm.cancelAllRequests()
        val editor = event.editor
        // 避免频繁获取主题
        if (System.currentTimeMillis() - lastUpdateTime > 1000){
            theme = UIThemeListener.getTheme()
            themeColor = ColorManager.getColor(theme)
            lastUpdateTime = System.currentTimeMillis()
        }
        if (floatingToolBarHint[editor] == null) {
            floatingToolBarHint[editor] = FloatingToolBar(editor, themeColor)
        }
        if (editor.selectionModel.hasSelection()&&AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW.get()) {
            toolBarAlarm.cancelAllAndAddRequest({
                showHint(editor, floatingToolBarHint[editor])
            }, 50)
        }


    }

    private fun showHint(editor: Editor, floatingToolBar: FloatingToolBar) {
        try {
            ApplicationManager.getApplication().invokeLater({
                val hint = floatingToolBar.hint
                if (hint!!.isVisible) {
                    hint.hide()
                }
                if (!AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW.get()) {
                    toolBarAlarm.cancelAllRequests()
                    hint.hide()
                    return@invokeLater
                }
                //设置提示框消失条件
                val flags = HintManager.HIDE_BY_ESCAPE or HintManager.HIDE_BY_LOOKUP_ITEM_CHANGE or HintManager.HIDE_BY_TEXT_CHANGE or HintManager.HIDE_BY_SCROLLING
                //设置提示框展示位置
                try {
                    val position = CodeFuseToolBarHintUtils.getHintPosition(hint, editor)
                    HintManagerImpl.getInstanceImpl().showEditorHint(hint, editor, position, flags, 0, true)
                }catch (e: Exception){
                    LogUtil.info("FloatingToolBarService getHintPosition error:", e)
                }

            }, ModalityState.any())
        } catch (e: Exception) {
            LogUtil.info("FloatingToolBarService showHint error:", e)
        }
    }

    override fun dispose() {
    }

    fun openHint() {
        AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW = AtomicReference(true)
    }


}