package com.alipay.tsingyan.model.request

import com.alipay.tsingyan.utils.CommonUtils

/**
 * 抽象请求类
 */
abstract class AbstractRequestBean {
    /**
     * 用户token
     */
    var userToken: String? = null

    /**
     * 插件版本
     */
    var pluginVersion: String? = null

    /**
     * ide版本
     */
    var ideVersion: String? = null


    /**
     * 产品类型
     */
    var productType: String = CommonUtils.getProductType()
}