package com.alipay.tsingyan.services.input

import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project

interface InputTrackingService: Disposable {
    fun enableTracking(project: Project)
    fun disableTracking(project: Project)
    fun isTrackingEnabled(): Boolean
    fun getActiveListenerCount(): Int
    fun flushCachedInputChangeData(editor: Editor)
}