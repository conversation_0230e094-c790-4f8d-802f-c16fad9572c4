package com.alipay.tsingyan.services.input

import com.alipay.tsingyan.services.input.model.InputChangeData
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.agent.bean.CodeEditsDiffHistoricalRecordRequestBean
import com.alipay.tsingyan.agent.bean.CodeEditsInitModelRequestBean
import com.alipay.tsingyan.agent.bean.DiffHistoricalContentChange
import com.alipay.tsingyan.agent.bean.DiffHistoricalRange
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.CommonUtils.isCodeFile
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.event.EditorFactoryEvent
import com.intellij.openapi.editor.event.EditorFactoryListener
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import java.util.concurrent.ConcurrentHashMap

/**
 * 编辑器初始化状态枚举
 */
private enum class EditorInitStatus {
    UNINITIALIZED,  // 未初始化
    INITIALIZED,    // 已成功初始化
    FAILED          // 初始化失败
}

/**
 * 输入追踪服务
 * 负责管理所有编辑器的输入监听器
 *
 * <AUTHOR>
 */
class InputTrackingServiceImpl : InputTrackingService {

    private val logger = Logger.getInstance(InputTrackingServiceImpl::class.java)

    private val requestAlarm = CancellableAlarm(this)

    // Agent服务实例 - 延迟初始化避免循环依赖
    private var _agentService: AgentService? = null
    private fun getAgentService(): AgentService {
        if (_agentService == null) {
            _agentService = service<AgentService>()
        }
        return _agentService!!
    }

    // 存储项目级别的数据，包含文档版本和编辑器监听器
    private val projectData = ConcurrentHashMap<Project, ProjectTrackingData>()

    // 是否启用输入追踪
    private var isTrackingEnabled = false

    // 项目追踪数据
    private data class ProjectTrackingData(
        val documentVersions: ConcurrentHashMap<String, Long> = ConcurrentHashMap(),
        val editorListeners: ConcurrentHashMap<Editor, InputChangeListener> = ConcurrentHashMap(),
        // 缓存每个编辑器的输入变化数据
        val editorInputChangeData: ConcurrentHashMap<Editor, InputChangeData> = ConcurrentHashMap(),
        // 跟踪编辑器的初始化状态
        val editorInitStatus: ConcurrentHashMap<Editor, EditorInitStatus> = ConcurrentHashMap()
    )

    override fun dispose() {
        logger.info("InputTrackingService disposed")
    }

    init {
        // 注册编辑器工厂监听器
        ApplicationManager.getApplication().invokeLater {
            EditorFactory.getInstance().addEditorFactoryListener(object : EditorFactoryListener {
                override fun editorCreated(event: EditorFactoryEvent) {
                    if (isTrackingEnabled && CommonUtils.isCodeEditsEnable()){
                        // 获取编辑器所属的项目
                        val project = event.editor.project
                        if (project != null) {
                            // 只监听代码文件，排除日志等非代码文件
                            if (isCodeFile(event.editor)) {
                                val file = FileDocumentManager.getInstance().getFile(event.editor.document)
                                LogUtil.info("InputTrackingService editorCreated for code file: ${file?.name}")
                                addListenerToEditor(event.editor, project)
                            } else {
                                val file = FileDocumentManager.getInstance().getFile(event.editor.document)
                                LogUtil.debug("InputTrackingService ignoring non-code file: ${file?.name}")
                            }
                        }
                    }
                }

                override fun editorReleased(event: EditorFactoryEvent) {
                    if (isTrackingEnabled && CommonUtils.isCodeEditsEnable()) {
                        val file = FileDocumentManager.getInstance().getFile(event.editor.document)
                        LogUtil.debug("InputTrackingService editorReleased: ${file?.name}")
                        removeListenerFromEditor(event.editor)
                    }
                }
            }, this)
        }
    }

    /**
     * 启用输入追踪
     */
    override fun enableTracking(project: Project) {
        try {
            isTrackingEnabled = true
            // 为指定项目启用追踪
            projectData.getOrPut(project) { ProjectTrackingData() }
            // 为该项目的所有编辑器添加监听器（只处理代码文件）
            EditorFactory.getInstance().allEditors.forEach { editor ->
                if (editor.project == project && isCodeFile(editor)) {
                    val file = FileDocumentManager.getInstance().getFile(editor.document)
                    LogUtil.info("InputTrackingService adding listener to existing code file: ${file?.name}")
                    addListenerToEditor(editor, project)
                }
            }

            logger.info("Input tracking enabled for project: ${project.name}")
        } catch (e: Exception) {
            logger.error("Failed to enable input tracking", e)
        }
    }

    /**
     * 禁用输入追踪
     */
    override fun disableTracking(project: Project) {
        try {
            // 获取项目追踪数据并移除该项目的所有监听器
            val trackingData = projectData.remove(project)
            trackingData?.editorListeners?.forEach { (editor, listener) ->
                editor.document.removeDocumentListener(listener)
                val file = FileDocumentManager.getInstance().getFile(editor.document)
                logger.info("Removed input listener from editor for file: ${file?.name}")
            }
            // 清理所有缓存数据
            trackingData?.editorInputChangeData?.clear()
            trackingData?.editorInitStatus?.clear()

            logger.info("Input tracking disabled for project: ${project.name}")
        } catch (e: Exception) {
            logger.error("Failed to disable input tracking", e)
        }
    }

    /**
     * 为编辑器添加监听器
     */
    private fun addListenerToEditor(editor: Editor, project: Project) {
        // 确保项目数据存在
        val trackingData = projectData.getOrPut(project) { ProjectTrackingData() }

        if (trackingData.editorListeners.containsKey(editor)) {
            return
        }

        val listener = InputChangeListener(project, editor) { inputChangeData ->
            // 将输入变化数据缓存到editorInputChangeData中
            try {
                trackingData.editorInputChangeData[editor] = inputChangeData
                val file = FileDocumentManager.getInstance().getFile(editor.document)
                logger.debug("Cached input change data for file: ${file?.name}, cache size: ${trackingData.editorInputChangeData.size}")

                val agentService = getAgentService()
                if (agentService.isAgentFinishInit() && !agentService.isAgentClosed() && agentService.isHeatBeating()) {
                    val runnable = Runnable {
                        try {
                            // 检查缓存中是否有数据，如果有才调用
                            val cachedInputChangeData = trackingData.editorInputChangeData[editor]
                            if (cachedInputChangeData != null) {
                                logger.debug("Processing cached input change data for file: ${file?.name}")
                                callCodeEditsDiffHistoricalRecord(editor, cachedInputChangeData, trackingData)
                                // 调用完成后清空缓存
                                trackingData.editorInputChangeData.remove(editor)
                                logger.debug("Removed cached input change data for file: ${file?.name}, remaining cache size: ${trackingData.editorInputChangeData.size}")
                            } else {
                                logger.debug("No cached input change data found for file: ${file?.name}")
                            }
                        } catch (e: Exception) {
                            logger.warn("Failed to call codeEditsDiffHistoricalRecord", e)
                        }
                    }
                    requestAlarm.cancelAllAndAddRequest(runnable, 3 * 1000)
                }
            } catch (e: Exception) {
                logger.warn("Error accessing AgentService", e)
            }
        }

        editor.document.addDocumentListener(listener)
        trackingData.editorListeners[editor] = listener

        // 获取文件信息用于日志
        val file = FileDocumentManager.getInstance().getFile(editor.document)
        logger.info("Added input listener to editor for file: ${file?.name} in project: ${project.name}")
    }

    /**
     * 从编辑器移除监听器
     */
    private fun removeListenerFromEditor(editor: Editor) {
        // 查找包含该编辑器的项目
        val project = editor.project
        if (project != null && !project.isDisposed) {
            val trackingData = projectData[project]
            val listener = trackingData?.editorListeners?.remove(editor)
            if (listener != null) {
                editor.document.removeDocumentListener(listener)

                val file = FileDocumentManager.getInstance().getFile(editor.document)
                logger.info("Removed input listener from editor for file: ${file?.name} in project: ${project.name}")

                // 清理版本号缓存、输入变化数据缓存和初始化状态
                file?.let { virtualFile ->
                    trackingData.documentVersions.remove(virtualFile.path)
                }
                trackingData.editorInputChangeData.remove(editor)
                trackingData.editorInitStatus.remove(editor)
            }
        }
    }


    /**
     * 检查追踪状态
     */
    override fun isTrackingEnabled(): Boolean = isTrackingEnabled

    /**
     * 获取当前监听的编辑器数量
     */
    override fun getActiveListenerCount(): Int {
        return projectData.values.sumOf { it.editorListeners.size }
    }


    /**
     * 获取指定编辑器的初始化状态
     */
    fun getEditorInitStatus(editor: Editor): String {
        val project = editor.project ?: return "Unknown"
        val status = projectData[project]?.editorInitStatus?.get(editor) ?: EditorInitStatus.UNINITIALIZED
        return when (status) {
            EditorInitStatus.UNINITIALIZED -> "Uninitialized"
            EditorInitStatus.INITIALIZED -> "Initialized"
            EditorInitStatus.FAILED -> "Failed"
        }
    }

    /**
     * 重置指定编辑器的初始化状态，允许重新尝试初始化
     * 这在某些情况下可能有用，比如Agent服务重启后
     */
    fun resetEditorInitStatus(editor: Editor) {
        val project = editor.project ?: return
        val trackingData = projectData[project] ?: return

        trackingData.editorInitStatus.remove(editor)

        val file = FileDocumentManager.getInstance().getFile(editor.document)
        logger.info("Reset initialization status for editor: ${file?.name}")
    }

    /**
     * 主动调用将缓存editorInputChangeData发送给本地核心服务
     * 先调用发送函数，再清空缓存，确保数据不丢失
     */
    override fun flushCachedInputChangeData(editor: Editor) {
        val project = editor.project ?: return
        val trackingData = projectData[project] ?: return

        val cachedInputChangeData = trackingData.editorInputChangeData[editor]
        if (cachedInputChangeData != null) {
            try {
                val file = FileDocumentManager.getInstance().getFile(editor.document)
                logger.debug("Manually flushing cached input change data for file: ${file?.name}")

                // 先调用发送函数（内部会自动进行初始化检查）
                callCodeEditsDiffHistoricalRecord(editor, cachedInputChangeData, trackingData)

                // 调用成功后再清空缓存
                trackingData.editorInputChangeData.remove(editor)
                logger.debug("Successfully flushed and removed cached input change data for file: ${file?.name}")
            } catch (e: Exception) {
                logger.warn("Failed to flush cached input change data, keeping in cache for retry", e)
                // 调用失败时不清空缓存，保留数据以便重试
            }
        } else {
            logger.debug("No cached input change data found for editor")
        }
    }

    /**
     * 调用Agent服务初始化编辑轨迹模型
     * 返回初始化是否成功
     */
    private fun performCodeEditsInitModel(editor: Editor, trackingData: ProjectTrackingData): Boolean {
        try {
            val file = FileDocumentManager.getInstance().getFile(editor.document)
            if (file == null) {
                logger.debug("No file associated with editor, skipping codeEditsInitModel call")
                return false
            }

            val fileUri = file.path
            val fileContent = editor.document.text

            // 使用当前时间戳作为初始版本号
            val modifyTime = editor.document.modificationStamp
            trackingData.documentVersions[fileUri] = modifyTime

            // 检测行尾字符
            val eol = detectLineEnding(fileContent)

            // 创建初始化请求Bean
            val request = CodeEditsInitModelRequestBean().apply {
                uri = fileUri
                text = fileContent
                version = modifyTime
                this.eol = eol
            }

            // 调用Agent服务
            val result = getAgentService().codeEditsInitModel(request)

            if (result) {
                // 初始化成功，标记该编辑器为已初始化
                trackingData.editorInitStatus[editor] = EditorInitStatus.INITIALIZED
                logger.info("codeEditsInitModel success for file: ${file.name}, version: $modifyTime")
                return true
            } else {
                // 初始化失败，不设置状态（保持UNINITIALIZED，让上层设置为FAILED）
                logger.warn("codeEditsInitModel failed for file: ${file.name}, version: $modifyTime")
                return false
            }

        } catch (e: Exception) {
            // 异常情况下也不设置状态（保持UNINITIALIZED，让上层设置为FAILED）
            val file = FileDocumentManager.getInstance().getFile(editor.document)
            LogUtil.info("Error calling codeEditsInitModel for file: ${file?.name}", e)
            return false
        }
    }

    /**
     * 调用Agent服务记录编辑轨迹
     * 在第一次调用时会自动进行初始化，如果初始化失败则后续不再尝试
     */
    private fun callCodeEditsDiffHistoricalRecord(
        editor: Editor,
        inputChangeData: InputChangeData,
        trackingData: ProjectTrackingData
    ) {
        try {
            val file = FileDocumentManager.getInstance().getFile(editor.document)
            if (file == null) {
                logger.debug("No file associated with editor, skipping codeEditsDiffHistoricalRecord call")
                return
            }

            // 获取编辑器的初始化状态，默认为未初始化
            val initStatus = trackingData.editorInitStatus[editor] ?: EditorInitStatus.UNINITIALIZED

            when (initStatus) {
                EditorInitStatus.FAILED -> {
                    logger.debug("Editor for file: ${file.name} has failed initialization before, skipping codeEditsDiffHistoricalRecord call.")
                    return
                }

                EditorInitStatus.UNINITIALIZED -> {
                    //logger.info("Editor for file: ${file.name} not initialized yet, calling codeEditsInitModel first.")

                    // 调用初始化方法
                    val initSuccess = performCodeEditsInitModel(editor, trackingData)
                    if (!initSuccess) {
                        // 初始化失败，标记该编辑器并打印日志
                        trackingData.editorInitStatus[editor] = EditorInitStatus.FAILED
                        LogUtil.info("CodeEditsInitModel failed for file: ${file.name}, this editor will not call codeEditsDiffHistoricalRecord in the future.", false)
                        return
                    } else {
                        LogUtil.info("CodeEditsInitModel succeeded for file: ${file.name}, proceeding with codeEditsDiffHistoricalRecord.", false)
                    }
                }

                EditorInitStatus.INITIALIZED -> {
                    // 已初始化，直接继续执行
                }
            }

            val fileUri = file.path
            val fileContent = editor.document.text

            val modifyTime = editor.document.modificationStamp
            // 使用当前时间戳作为版本号
            trackingData.documentVersions[fileUri] = modifyTime

            // 检测行尾字符
            val eol = detectLineEnding(fileContent)

            // 转换InputChangeData为DiffHistoricalContentChange
            val contentChange = DiffHistoricalContentChange().apply {
                range = DiffHistoricalRange().apply {
                    start_line_number = inputChangeData.range.start_line_number
                    start_column = inputChangeData.range.start_column
                    end_line_number = inputChangeData.range.end_line_number
                    end_column = inputChangeData.range.end_column
                }
                range_offset = inputChangeData.range_offset
                range_length = inputChangeData.range_length
                text = inputChangeData.text
            }

            // 创建请求Bean
            val request = CodeEditsDiffHistoricalRecordRequestBean().apply {
                uri = fileUri
                text = fileContent
                version = modifyTime
                this.eol = eol
                content_changes = listOf(contentChange)
            }

            // 调用Agent服务
            val result = getAgentService().codeEditsDiffHistoricalRecord(request)
            LogUtil.info("codeEditsDiffHistoricalRecord result: $result for file: ${file.name}, version: $modifyTime", false)

        } catch (e: Exception) {
            val file = FileDocumentManager.getInstance().getFile(editor.document)
            LogUtil.info("Error calling codeEditsDiffHistoricalRecord for file: ${file?.name}", e)
        }
    }

    /**
     * 检测文件内容的行尾字符
     */
    private fun detectLineEnding(content: String): String {
        return when {
            content.contains("\r\n") -> "\r\n"
            content.contains("\n") -> "\n"
            content.contains("\r") -> "\r"
            else -> "\n" // 默认使用LF
        }
    }
} 