package com.alipay.tsingyan.services.ask.action

import cn.hutool.core.io.FileUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.TABKey
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_OPEN_WINDOW
import com.alipay.tsingyan.utils.ChatMessageUtil
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ui.UIUtils
import com.intellij.openapi.actionSystem.ActionPlaces
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.CustomShortcutSet
import com.intellij.openapi.actionSystem.KeyboardShortcut
import com.intellij.openapi.actionSystem.OverridingAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import java.awt.event.InputEvent
import java.awt.event.KeyEvent
import java.awt.event.MouseEvent
import javax.swing.KeyStroke


/**
 *
 * 在会话中提问的action
 */
class CodefuseAskAction : AnAction(), OverridingAction {

    val tracerService = service<CodeFuseTracerService>()
    private val editorManagerService = service<EditorManagerService>()

    override fun actionPerformed(e: AnActionEvent) {
        if (!editorManagerService.checkLogin()) {
            e.project?.let { NotificationUtils.showBeginLoginMessage(it) }
            return
        }
        val inputEvent = e.inputEvent ?: return
        var enterType = ClickType.RIGHT.toString()
        if(inputEvent is MouseEvent){
            enterType = ClickType.RIGHT.toString();
        }else if(inputEvent is KeyEvent){
            enterType = ClickType.SHORT_CUT.toString();
        }
        LogUtil.info("enterType "+enterType, false)
        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        val project = e.project

        if (editor != null && project != null && !project.isDisposed){
            LogUtil.info("right click... $TEXT_STATIC_OPEN_WINDOW")
            val selectionModel = editor.selectionModel
            var selectedText = selectionModel.selectedText ?: ""
            //如果是快捷键触发， 没有选中代码也打开codefuse。 如果是右键触发，没有选中代码就拦截
//            if ((selectedText.isEmpty() || selectedText.isBlank()) && enterType == ClickType.RIGHT.toString()) {
//                editor.project?.let { NotificationUtils.notifyMessage("CodeFuse", "未选中代码", it, null) }
//                return
//            }
            
            // 根据是否支持MCP功能来决定不同的行为
            if (CommonUtils.isSupportMcpFeature()) {
                // 支持MCP功能时，切换到A Par Agent标签页
                UIUtils.switchToolWindowTab(project, TABKey.AI_PARTNER)
                // 使用通用函数发送CHAT_DATA_V2消息
                val clickType = if (enterType == ClickType.RIGHT.toString()) ClickType.RIGHT else ClickType.SHORT_CUT
                ChatMessageUtil.sendChatDataV2MessageWithCode(
                    project, 
                    selectedText, 
                    AppConstant.POSE_AS_SESSION, 
                    clickType, 
                    CommonUtils.getSelectedCodeInfo()
                )
//                tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_ASK, null)
            } else {
                // 不支持MCP功能时，保持原有逻辑
                UIUtils.switchToolWindowTab(project)
                val webView = e.project?.getService(WebViewService::class.java)?.getWebView()
                if (webView != null){
                    val messageModel = MessageModel()
                    messageModel.actionType = WebActionTypeEnum.TO_JS.name
                    messageModel.target = WebTargetEnum.CHAT_DATA.name
                    val jsonObject = JSONObject()
                    jsonObject.put("question", selectedText)
                    jsonObject.put("intention", AppConstant.POSE_AS_SESSION)
                    jsonObject.put("enterType", enterType)
                    jsonObject.put("recentFilesInfo", e.project?.getService(RecentFilesService::class.java)?.getRecentFiles(project))
                    messageModel.message = jsonObject.toString()
                    LogUtil.info("OPEN_WINDOW from rightclick sendMsgToBrowser ${JSON.toJSONString(messageModel)}", false)
                    webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
//                tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_ASK, null)
                }
            }
        }


    }

    override fun update(e: AnActionEvent) {
        super.update(e)
        if (e.project == null) {
            e.presentation.isEnabled = false
            return
        }

        if (!editorManagerService.checkLogin()) {
            e.project?.let { e.presentation.isEnabled = false }
            return
        }

        e.presentation.isEnabled = true
        
        // 根据是否支持MCP功能来更新文案和快捷键
        if (CommonUtils.isSupportMcpFeature()) {
            // 在FloatingToolBar中始终显示"Chat"，在菜单中显示"去提问"
            if (e.place == ActionPlaces.EDITOR_TOOLBAR) {
                if (FileUtil.isWindows()){
                    e.presentation.text = "Chat(shift+ctrl+I)"
                } else {
                    e.presentation.text = "Chat(⇧+⌘+I)"
                }
            } else {
                e.presentation.text = "去提问"
            }
            // 设置快捷键为 shift + cmd + i
            val keyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_I, InputEvent.SHIFT_DOWN_MASK or InputEvent.META_DOWN_MASK)
            val shortcut = KeyboardShortcut(keyStroke, null)
            shortcutSet = CustomShortcutSet(shortcut)
        } else {
            // 在FloatingToolBar中始终显示"Chat"，在菜单中显示"在会话中提问"
            if (e.place == ActionPlaces.EDITOR_TOOLBAR) {
                if (FileUtil.isWindows()){
                    e.presentation.text = "Chat(ctrl+Y)"
                } else {
                    e.presentation.text = "Chat(⌘+Y)"
                }
            } else {
                e.presentation.text = "在会话中提问"
            }
            // 保持原有快捷键 cmd + y
            val keyStroke = KeyStroke.getKeyStroke(KeyEvent.VK_Y, InputEvent.META_DOWN_MASK)
            val shortcut = KeyboardShortcut(keyStroke, null)
            shortcutSet = CustomShortcutSet(shortcut)
        }
    }


}