package com.alipay.tsingyan.services.ask.action

import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import java.util.*

/**
 *
 * 解决快捷键冲突的问题
 */
class CodefuseAskActionPromoter : ActionPromoter {
    val editorManagerService = service<EditorManagerService>()

    override fun promote(actions: MutableList<out AnAction>, context: DataContext): MutableList<AnAction>? {
        if (actions == null) {
            return super.promote(actions, context)
        }

        if (context == null) {
            return super.promote(actions, context)
        }

        if (this.isValidEditor(CommonDataKeys.EDITOR.getData(context) as Editor?)) {
            return null
        } else if (actions.stream().noneMatch { action: AnAction? -> action is CodefuseAskAction }) {
            return null
        } else {
            val result = ArrayList(actions)
            result.sortWith { a, b ->
                val aOk = isCodefuseAskAction(a)
                val bOk = isCodefuseAskAction(b)
                if (aOk) {
                    return@sortWith -1
                }
                if (bOk) {
                    return@sortWith 1
                }
                0
            }

            LogUtil.info("promote actions: $result", false)
            return result
        }
    }

    private fun isValidEditor(editor: Editor?): Boolean {
        return editor == null || !editorManagerService.isAvailable(editor)
    }

    private fun isCodefuseAskAction(action: AnAction): Boolean {
        return action is CodefuseAskAction
    }


}