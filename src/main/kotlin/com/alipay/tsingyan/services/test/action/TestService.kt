package com.alipay.tsingyan.services.test.action

import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.completion.IntentionType
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.CodeFuseDialogWebBrowser
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import org.apache.http.util.TextUtils

class TestService: Disposable {
    private val LOGGER: Logger = Logger.getInstance(TestService::class.java)

    private val TestCodeAlarm = CancellableAlarm(this)
    private var agentServiceImpl: AgentService = service<AgentService>()

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
    }

    fun generateUnitTest(project: Project?, selectedText: String, fileContent: String, language: String, clickType: ClickType, intentionType: IntentionType,
                         startOffSet: Int,
                         endOffSet: Int,
                         startLine: Int,
                         endLine: Int,
                         isTestFile: Boolean) {
        if (project == null){
            return
        }

        //已经初始化过了，直接使用即可
        //将选中的代码传给webview
        val webView = project.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            openToolWindow(project)
            generateTestCode(project, selectedText, fileContent, language, webView, clickType, intentionType, startOffSet, endOffSet, startLine, endLine, isTestFile)
            return
        }

        //没有初始化过，需要等待初始化成功
        TestCodeAlarm.cancelAllAndAddRequest({
            ApplicationManager.getApplication().invokeLater {
                openToolWindow(project)
            }

            //等待页面加载完成后再进行操作
            var count = 10
            while (true){
                if (--count <= 0){
                    return@cancelAllAndAddRequest
                }
                if (webView.getHasLoadedFinished()){
                    Thread.sleep(3500)
                    LogUtil.info("analysisyToWebView invokeLater $count")
                    ApplicationManager.getApplication().invokeLater {
                        generateTestCode(project, selectedText, fileContent, language, webView, clickType, intentionType, startOffSet, endOffSet, startLine, endLine, isTestFile)
                    }
                    return@cancelAllAndAddRequest
                }

                LogUtil.info("analysisyToWebView wait Count $count")
                Thread.sleep(500)
            }

        }, 0)
    }

    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }

    private fun generateTestCode(project: Project, str:String, fileContent:String, language: String, webView: CodeFuseDialogWebBrowser, clickType: ClickType, intentionType: IntentionType,
                                 startOffSet: Int,
                                 endOffSet: Int,
                                 startLine: Int,
                                 endLine: Int,
                                 isTestFile: Boolean) {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.CHAT_DATA.name
        val jsonOject = JSONObject()
        if (intentionType == IntentionType.UNIT_TEST){
            jsonOject.put("question", "为以下代码写单测：\n"+"```\n$str\n```")
        } else {
            jsonOject.put("question", "为以下代码生成测试用例：\n"+"```\n$str\n```")
        }
        jsonOject.put("intention", AppConstant.CHAT_INTENTION_TEST)
        jsonOject.put("fileContent", fileContent)
        jsonOject.put("language", language)
        jsonOject.put("repo", ProjectCache.getGitData(project))
        jsonOject.put("enterType", clickType.toString())
        jsonOject.put("intentionType", intentionType.toString())
        // isTestFile测试文件还是非测试文件,用来区分是否走补充更多测试的逻辑
        jsonOject.put("isTestFile", isTestFile)
        jsonOject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
        // 使用 ProjectUtil 获取项目目录
        val projectUrl: String? = project.basePath
        val relatedContent = agentServiceImpl.getRelatedContent(AppConstant.FILE_URL, projectUrl, language, fileContent, str, intentionType, startOffSet, endOffSet, startLine, endLine, isTestFile)
        if (!TextUtils.isEmpty(relatedContent)) {
            jsonOject.put("relatedContent", relatedContent)
        } else  {
            jsonOject.put("relatedContent", "")
        }
        //isTestFile需要替换“补充更多测试用例的prompt”
        if (isTestFile && (intentionType == IntentionType.UNIT_TEST || intentionType == IntentionType.AUTO)) {
            jsonOject.put("question", "为以下代码测试类补充更多场景的测试用例，补充用例需要标明测试场景：\n"+"```\n$str\n```")
        }
        messageModel.message = jsonOject.toString()
        LogUtil.debug("generateTestCode sendMsgToBrowser ${messageModel.message}")
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }


}
