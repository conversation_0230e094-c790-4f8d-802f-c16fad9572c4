package com.alipay.tsingyan.services.test.action

import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.completion.IntentionType
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.COMPLETION_CONFIG
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.OverridingAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor

/**
 * 生成测试 的Action
 * author: junlong.njl
 * 2023-06-30 16:03:52
 */
class TestAction : AnAction(), Disposable,OverridingAction {
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    private val editorManagerService = service<EditorManagerService>()
    val tracerService = service<CodeFuseTracerService>()

    override fun actionPerformed(e: AnActionEvent) {
        if (!editorManagerService.checkLogin()) {
            e.project?.let { NotificationUtils.showBeginLoginMessage(it) }
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        val project = e.project
        if (editor != null && project != null && !project.isDisposed) {
            val selectionModel = editor.selectionModel
            var selectedText = selectionModel.selectedText ?: ""
            val startOffset = selectionModel.selectionStart
            val endOffset = selectionModel.selectionEnd
            val document = editor.document
            val startLine = document.getLineNumber(startOffset)
            val endLine = document.getLineNumber(endOffset)
            if (selectedText.isEmpty() || selectedText.isBlank()) {
                editor.project?.let { NotificationUtils.notifyMessage("CodeFuse", "未选中代码", it, null) }
                return
            }
            val fileContent = editor.document.text
            val language = AntEditorUtil.getLanguage(editor)
            val isTestFile = CommonUtils.isInTestBundle(editor)
            if (COMPLETION_CONFIG.intentionType == IntentionType.UNIT_TEST.name) {
                e.project?.service<TestService>()?.generateUnitTest(
                    e.project,
                    selectedText,
                    fileContent,
                    language,
                    ClickType.RIGHT,
                    IntentionType.UNIT_TEST,
                    startOffset,
                    endOffset,
                    startLine,
                    endLine,
                    isTestFile
                )
            } else {
                e.project?.service<TestService>()?.generateUnitTest(
                    e.project,
                    selectedText,
                    fileContent,
                    language,
                    ClickType.RIGHT,
                    IntentionType.AUTO,
                    startOffset,
                    endOffset,
                    startLine,
                    endLine,
                    isTestFile
                )
            }
//            tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_TEST,null)
        }
    }


    override fun update(e: AnActionEvent) {
        super.update(e)

        if (e.project == null) {
            e.presentation.isEnabled = false
            return
        }

        if (!editorManagerService.checkLogin()) {
            e.project?.let { e.presentation.isEnabled = false }
            return
        }

        //FloatingToolBar显示的文字不需要覆盖
        if (e.presentation.text != AppConstant.FLOATING_TOOL_BAR_TEST_ACTION_TEXT) {
            val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
            val isTestFile = CommonUtils.isInTestBundle(editor)
            if (isTestFile) {
                e.presentation.text = AppConstant.MORE_UNIT_TEST
            } else {
                e.presentation.text = if (COMPLETION_CONFIG.intentionType == IntentionType.UNIT_TEST.name) AppConstant.TEXT_UNIT_TEST else AppConstant.TEXT_AUTO
            }
            val project = e.project
            if (editor != null && project != null && !project.isDisposed) {
                val selectionModel = editor.selectionModel
                val selectedText = selectionModel.selectedText ?: ""
                if (selectedText.isEmpty() || selectedText.isBlank()) {
                    e.presentation.isEnabled = false
                    return
                }
            }
            e.presentation.isEnabled = true
        }

    }

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
    }

}