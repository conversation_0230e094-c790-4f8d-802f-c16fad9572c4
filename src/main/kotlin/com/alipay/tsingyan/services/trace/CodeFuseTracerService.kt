package com.alipay.tsingyan.services.trace

import com.alipay.tsingyan.model.UserTypeModel
import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.model.enums.FloatingToolBarActionTypeEnum
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.intellij.openapi.project.Project

interface CodeFuseTracerService {
    fun submitInitData()
    fun submitRuntimeEvent(completionRtModel: CompletionRtModel, traceType: String? = null)
    fun submitRuntimeLineCodeEvent(completionRtModel: CompletionRtModel)
    fun submitException(exception: Throwable)
    fun submitTypeAndData(project: Project, traceType: TraceTypeEnum, data: Any?)
    fun submitTypeAndData(traceType: TraceTypeEnum, data: Any?)
    fun submitTypeStrAndData(project: Project, traceType: String, data: Any?)
    fun submitUserCount(userTypeModel: UserTypeModel)

    fun submitFloatingToolBarClickEvent(project: Project, actionTypeEnum: FloatingToolBarActionTypeEnum)

}