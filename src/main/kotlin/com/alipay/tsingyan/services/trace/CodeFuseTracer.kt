package com.alipay.tsingyan.services.trace

import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.TraceModel
import com.alipay.tsingyan.model.TraceModelTypeStr
import com.alipay.tsingyan.model.UserTypeModel
import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.model.enums.FloatingToolBarActionTypeEnum
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import java.lang.Exception
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

class CodeFuseTracer : CodeFuseTracerService, Disposable {
    private val LOGGER: Logger = Logger.getInstance(CodeFuseTracer::class.java)

    var localUserStore: LocalUserStore = service<LocalUserStore>()

    val executorService: ScheduledExecutorService = Executors.newScheduledThreadPool(3)

    /**
     * 青燕prod远程服务
     */
    var tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()

    override fun submitInitData() {
        val callable = Callable {
            val traceModel = TraceModel<String>()
            traceModel.type = TraceTypeEnum.CLOSE_CODEGEN_SWITCH
            traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
            traceModel.ideVersion = CommonUtils.getIdeVersion()
            traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
            tsingYanProdService.submitTrace(traceModel)
            "success"
        }

        submitTrace(callable)
    }

    override fun submitRuntimeEvent(completionRtModel: CompletionRtModel, traceType: String?){
        val callable = Callable {
            if (completionRtModel != null){
                val traceModel = TraceModel<CompletionRtModel>();
                traceModel.type = TraceTypeEnum.COMPLETION_RT
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken

                completionRtModel.userToken = traceModel.userToken
                completionRtModel.pluginVersion = traceModel.pluginVersion
                completionRtModel.ideVersion = traceModel.ideVersion

                traceModel.data = completionRtModel
                tsingYanProdService.submitTrace(traceModel, traceType)
            }
            "success"
        }

        submitTrace(callable)
    }

    override fun submitRuntimeLineCodeEvent(completionRtModel: CompletionRtModel) {
        val callable = Callable {
            if (completionRtModel != null){
                val traceModel = TraceModel<CompletionRtModel>();
                traceModel.type = TraceTypeEnum.COMPLETION_RT_CODE
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken

                completionRtModel.userToken = traceModel.userToken
                completionRtModel.pluginVersion = traceModel.pluginVersion
                completionRtModel.ideVersion = traceModel.ideVersion

                traceModel.data = completionRtModel
                tsingYanProdService.submitTrace(traceModel)
            }
            "success"
        }

        submitTrace(callable)
    }

    override fun submitException(exception: Throwable){
        val callable = Callable {
            val traceModel = TraceModel<String>()
            traceModel.type = TraceTypeEnum.ERROR_MSG
            traceModel.data = JSONObject.toJSONString(exception)
            traceModel.ideVersion = CommonUtils.getIdeVersion()
            traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
            traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
            tsingYanProdService.submitTrace(traceModel)
            "success"
        }

        submitTrace(callable)
    }

    override fun submitTypeAndData(project: Project, traceType: TraceTypeEnum, data: Any?) {
        val callable = Callable {
            try {
                val traceModel = TraceModel<String>()
                traceModel.type = traceType
                var parseObject = JSONObject()
                if (data != null){
                    parseObject = JSONObject.parseObject(data.toString())
                }
                parseObject.put("repo", ProjectCache.getGitData(project))
                parseObject.put("fileUrl", AppConstant.FILE_URL)
                traceModel.data = parseObject.toString()
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
                tsingYanProdService.submitTrace(traceModel)
            } catch (e: Exception) {
                LOGGER.error("submitTypeAndData error", e)
            }
            "success"
        }

        submitTrace(callable)
    }

    override fun submitTypeAndData(traceType: TraceTypeEnum, data: Any?) {
        val callable = Callable {
            try {
                val traceModel = TraceModel<String>()
                traceModel.type = traceType
                var parseObject = JSONObject()
                if (data != null){
                    parseObject = JSONObject.parseObject(data.toString())
                }
                parseObject.put("fileUrl", AppConstant.FILE_URL)
                traceModel.data = parseObject.toString()
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
                tsingYanProdService.submitTrace(traceModel)
            } catch (e: Exception) {
                LOGGER.error("submitTypeAndData error", e)
            }
            "success"
        }

        submitTrace(callable)
    }

    override fun submitTypeStrAndData(project: Project, traceType: String, data: Any?) {
        val callable = Callable {
            try {
                val traceModel = TraceModelTypeStr<String>()
                traceModel.type = traceType
                var parseObject = JSONObject()
                if (data != null){
                    parseObject = JSONObject.parseObject(data.toString())
                }
                parseObject.put("repo", ProjectCache.getGitData(project))
                parseObject.put("fileUrl", AppConstant.FILE_URL)
                traceModel.data = parseObject.toString()
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
                tsingYanProdService.submitTraceTypeStr(traceModel)
            } catch (e: Exception) {
                LOGGER.error("submitTypeAndData error", e)
            }
            "success"
        }

        submitTrace(callable)
    }

    override fun submitUserCount(userTypeModel: UserTypeModel) {
        val callable = Callable {
            try {
                val traceModel = TraceModel<String>()
                traceModel.type = TraceTypeEnum.COUN_CODES
                traceModel.data = JSONObject.toJSONString(userTypeModel)
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
                tsingYanProdService.submitTrace(traceModel)
            } catch (e: Exception) {
                LOGGER.error("submitTypeAndData error", e)
            }
            "success"
        }

        submitTrace(callable)
    }

    /**
     * 上报悬浮窗点击事件
     */
    override fun submitFloatingToolBarClickEvent(project: Project, actionTypeEnum: FloatingToolBarActionTypeEnum) {
        val callable = Callable {
            try {
                val traceModel = TraceModel<String>()
                traceModel.type = actionTypeEnum.traceType
                var parseObject = JSONObject()
                parseObject.put("actionType", actionTypeEnum.actionType)
                traceModel.data = parseObject.toString()
                traceModel.ideVersion = CommonUtils.getIdeVersion()
                traceModel.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                traceModel.userToken = localUserStore.getUserInfoModel()?.userToken
                tsingYanProdService.submitTrace(traceModel)
            } catch (e: Exception) {
                LOGGER.error("submitTypeAndData error", e)
            }
            "success"
        }

        submitTrace(callable)
    }

    private fun submitTrace(callable: Callable<String>) {
        executorService.schedule(callable, 1000, TimeUnit.MILLISECONDS)
    }

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
        executorService.shutdown()
    }
}