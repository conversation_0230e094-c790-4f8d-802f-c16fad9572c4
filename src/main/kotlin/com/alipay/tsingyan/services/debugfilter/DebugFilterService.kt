package com.alipay.tsingyan.services.debugfilter

import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.CodeFuseDialogWebBrowser
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager

/**
 * 生成java exception的修复建议
 *
 * <AUTHOR>
 *
 * @date 2024-03-19 14:59:17
 */
class DebugFilterService:Disposable {

    private val debugFilterAlarm = CancellableAlarm(this)

    fun generateFixPrompt(project: Project?, exceptionTrace: String, code:String, fileContent: String, language: String){
        if (project == null){
            return
        }

        //已经初始化过了，直接使用即可
        //将选中的代码传给webview
        val webView = project.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            openToolWindow(project)
            generateFixExceptionPrompt(project, exceptionTrace, code, fileContent, language, webView)
            return
        }

        //没有初始化过，需要等待初始化成功
        debugFilterAlarm.cancelAllAndAddRequest({
            ApplicationManager.getApplication().invokeLater {
                openToolWindow(project)
            }

            //等待页面加载完成后再进行操作
            var count = 10
            while (true){
                if (--count <= 0){
                    return@cancelAllAndAddRequest
                }
                if (webView.getHasLoadedFinished()){
                    Thread.sleep(3500)
                    LogUtil.info("analysisyToWebView invokeLater $count")
                    ApplicationManager.getApplication().invokeLater {
                        generateFixExceptionPrompt(project, exceptionTrace, code, fileContent, language, webView)
                    }
                    return@cancelAllAndAddRequest
                }

                LogUtil.info("analysisyToWebView wait Count $count")
                Thread.sleep(500)
            }

        }, 0)
    }

    private fun generateFixExceptionPrompt(project: Project, exceptionTrace: String, code:String, fileContent: String, language: String, webView: CodeFuseDialogWebBrowser) {
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.CHAT_DATA.name
        val jsonOject = JSONObject()
        jsonOject.put("intention", AppConstant.FIX_EXCEPTION_PROMPT)
        jsonOject.put("trace", exceptionTrace)
        jsonOject.put("code", code)
        jsonOject.put("fileContent", fileContent)
        jsonOject.put("language", language)
        jsonOject.put("repo", ProjectCache.getGitData(project))
        jsonOject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
        messageModel.message = jsonOject.toString()
        LogUtil.info("generateFixPrompt sendMsgToBrowser ${messageModel.message}", false)
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }

    override fun dispose() {
    }

    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }
}