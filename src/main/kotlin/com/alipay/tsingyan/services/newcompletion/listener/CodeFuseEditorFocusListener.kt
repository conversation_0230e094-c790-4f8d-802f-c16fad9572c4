package com.alipay.tsingyan.services.newcompletion.listener

import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.prompt.service.PromptCacheService
import com.alipay.tsingyan.services.composer.ComposerService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.webview.service.JSApiService
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiManager
import org.apache.http.util.TextUtils

/**
 * 监听切换文件，文件切换取消补全
 *
 * author: jianzhi
 * 2023年07月26日11:15:26
 */
class CodeFuseEditorFocusListener(project: Project) : FileEditorManagerListener {
    val LOG: Logger = Logger.getInstance(CodeFuseEditorFocusListener::class.java)

    private val project = project
    private val editorManager = service<EditorManagerService>()
    private val editorManagerService = service<EditorManagerService>()
    var agentService: AgentService = service<AgentService>()

    override fun fileClosed(fileEditorManager: FileEditorManager, virtualFile: VirtualFile) {
        LogUtil.info("cfl fileClosed 1 文件关闭 " + virtualFile.path, false)
        val service = project.service<InlineChatService>()
        service.closeFile(virtualFile)

        project.service<ComposerService>().closeFile(virtualFile)
    }

    override fun selectionChanged(event: FileEditorManagerEvent) {
        val oldFile = event.oldFile
        if (oldFile != null && oldFile.isValid) {
            val psiFile = PsiManager.getInstance(project).findFile(oldFile)
            if (psiFile != null && psiFile.isValid) {
                val oldEditor = event.oldEditor
                if (oldEditor is TextEditor) {
                    val editor = oldEditor.editor
                    LOG.debug("cfl selectionChanged1  文件切换")
                    editorManager.disposeInlays(editor, InlayDisposeContextEnum.UserAction)
                }
            }
        }

        // 新增页面切换的消息回调
        try {
            var projectBasePath = project.basePath ?: ""
            var oldFileName = ""
            var oldFileCount = 0
            var oldFileOffSet = 0
            var oldFilePath = ""
            var oldFileContent = ""

            var newFileName = ""
            var newFileCount = 0
            var newFileOffSet = 0
            var newFilePath = ""

            val oldFileTemp = event.oldFile
            if (oldFileTemp != null && oldFileTemp.isValid) {
                val oldPisFile = PsiManager.getInstance(project).findFile(oldFileTemp)
//                LOG.info("cfl selectionChanged2  文件切换：" + oldPisFile!!.text)
                if (oldPisFile != null && oldPisFile.isValid) {
                    val oldEditor = event.oldEditor
                    if (oldEditor is TextEditor) {
                        val document: Document = oldEditor.editor.document
                        val charactersCount: Int = document.textLength

                        oldFileName = oldPisFile.name
                        oldFileCount = charactersCount
                        oldFileOffSet = oldEditor.editor.caretModel.offset
                        oldFilePath = oldFileTemp.path
                        oldFileContent = document.text
                    }
                }
            }

            val newFileTemp = event.newFile
            if (newFileTemp != null && newFileTemp.isValid) {
                val newPisFile = PsiManager.getInstance(project).findFile(newFileTemp)
                if (newPisFile != null && newPisFile.isValid) {
                    val newEditor = event.newEditor
                    if (newEditor is TextEditor) {
                        val document: Document = newEditor.editor.document
                        val charactersCount: Int = document.textLength

                        newFileName = newPisFile.name
                        newFileCount = charactersCount
                        newFileOffSet = newEditor.editor.caretModel.offset
                        newFilePath = newFileTemp.path
                        AppConstant.FILE_URL = FileDocumentManager.getInstance().getFile(newEditor.editor.document)?.path.toString()
                    }
                }
            }
            editorManager.onChangePage(oldFileName, oldFileCount, newFileName, newFileCount)
            if (agentService.isAgentFinishInit()){
                agentService.switchPageDataRecord(oldFilePath, projectBasePath, oldFileContent)
            }

            if (editorManagerService.checkLogin()) {
//                if (!TextUtils.isEmpty(oldFileName)) {
//                    project.service<PromptCacheService>().updateCodeSnipCache()
//                }
                //发送切换文件的消息给H5
                project.service<JSApiService>().switchFileMsg(newFilePath, newFileOffSet, oldFilePath, oldFileOffSet)
            }
        } catch (e: Throwable){
            LOG.info("cfl selectionChanged2  文件异常 ", e)
        }
    }
}