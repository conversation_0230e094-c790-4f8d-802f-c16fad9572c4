package com.alipay.tsingyan.services.newcompletion

import com.alipay.tsingyan.services.composer.ComposerService
import com.alipay.tsingyan.utils.CommonUtils
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile


class EditorBeforeListener(private val project: Project) : FileEditorManagerListener.Before {

    private val logger = Logger.getInstance(this::class.java)

    /**
     * 在文件关闭前做的操作
     */
    override fun beforeFileClosed(source: FileEditorManager, file: VirtualFile) {
        super.beforeFileClosed(source, file)
        if (CommonUtils.isEnableComposer()) {
            try {
                project.service<ComposerService>().beforeClose(file)
            } catch (e: Exception) {
                logger.error(e)
            } catch (t: Throwable) {
                logger.error(t)
            }
        }
    }
}