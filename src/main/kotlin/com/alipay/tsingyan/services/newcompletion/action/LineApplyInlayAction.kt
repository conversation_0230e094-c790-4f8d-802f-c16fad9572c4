package com.alipay.tsingyan.services.newcompletion.action

import com.alipay.tsingyan.services.completion.edit.model.ApplyInlayType
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.intellij.codeInsight.lookup.LookupManager
import com.intellij.codeInsight.template.TemplateManager
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorActionHandler
import com.intellij.openapi.project.DumbAware

/**
 * Handler for applying inlay completions to the next Line
 * author: jianzhi
 * date: 2024年06月24日
 */
class LineApplyInlayAction : EditorAction(ApplyLineNewHandler()), DumbAware {
    init {
        setInjectedContext(true)
    }


    class ApplyLineNewHandler() : EditorActionHandler() {
        private val LOGGER: Logger = Logger.getInstance(ApplyLineNewHandler::class.java)
        val editorManagerService = service<EditorManagerService>()

        override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext): Boolean {
            LOGGER.info("doExecute LineApplyInlayAction isEnabledForCaret = ${isSupported(editor)}")
            return isSupported(editor)
        }

        override fun executeInCommand(editor: Editor, dataContext: DataContext): Boolean {
            LOGGER.info("doExecute executeInCommand = ")
            return false
        }

        override fun doExecute(editor: Editor, caret: Caret?, dataContext: DataContext) {
            LOGGER.info("doExecute LineApplyInlayAction applyInlayType = ")
            editorManagerService.applyCompletion(editor, ApplyInlayType.NEXT_LINE)
        }

        fun isSupported(editor: Editor): Boolean {
            try {
                val project = editor.project
                return project != null && editor.caretModel.caretCount == 1 && (LookupManager.getActiveLookup(editor) == null)
                        && editorManagerService.hasCompletionInlays(editor) && TemplateManager.getInstance(project)
                    .getActiveTemplate(editor) == null
            } catch (e: Exception) {
                LOGGER.info(e)
            }
            return false
        }
    }
}