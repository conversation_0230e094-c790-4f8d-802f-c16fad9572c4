package com.alipay.tsingyan.services.commit

import ProgressTask
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.commit.AddCommitMsgRequestBean
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.*
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.UpdateInBackground
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diff.impl.patch.IdeaTextPatchBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.vcs.FilePath
import com.intellij.openapi.vcs.VcsDataKeys
import com.intellij.openapi.vcs.VcsException
import com.intellij.openapi.vcs.changes.Change
import com.intellij.openapi.vcs.changes.ContentRevision
import com.intellij.openapi.vcs.changes.CurrentContentRevision
import com.intellij.openapi.vcs.ui.CommitMessage
import com.intellij.vcs.commit.AbstractCommitWorkflowHandler
import org.apache.commons.lang3.StringUtils
import java.io.IOException
import java.lang.reflect.InvocationTargetException
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.util.*

/**
 * author: jianzhi
 * date: 2024-08-22 17:19:09
 *
 * 提交代码时，自动生成 commit message
 */
class CodefuseCommitMessageGenerationAction : AnAction(), UpdateInBackground {
    var progressTask: ProgressTask? = null
    @Volatile
    var isProcess = false
    val tracerService = service<CodeFuseTracerService>()
    private val editorManagerService = service<EditorManagerService>()
    private val tsingYanProdService = service<TsingYanProdService>()
    private val settingData = service<TsingYanSettingStore>()


    override fun actionPerformed(anActionEvent: AnActionEvent) {
        val project = anActionEvent.project ?: return

        //登录态判断
        if (!editorManagerService.checkLogin()) {
            anActionEvent.project?.let { NotificationUtils.showBeginLoginMessage(it) }
            return
        }

        // 停止状态点击无效
        if (isProcess) {
            return
        }

        try {
            isProcess = true
            //设置icon
            anActionEvent.presentation.icon = IconConstant.COMMIT_STOP
            // 非停止状态可以触发
            progressTask = ProgressTask(anActionEvent.project!!) { indicator ->
                //1、获取变更
                val diffList: MutableList<GitFileBean?> = getDiff(anActionEvent)
                if (diffList.isEmpty()) {
                    showMessage(project, "未选择要提交的变更，或所选文件不符合条件")
                    ApplicationManager.getApplication().invokeLater {
                        anActionEvent.presentation.icon = IconConstant.COMMIT_START
                        isProcess = false
                    }
                    return@ProgressTask
                }
                //变更文件上限20个
                if(diffList.size > 20){
                    showMessage(project, "提交消息生成失败，文件超出上限.")
                    ApplicationManager.getApplication().invokeLater {
                        anActionEvent.presentation.icon = IconConstant.COMMIT_START
                        isProcess = false
                    }
                    return@ProgressTask
                }
                val addCommitMsgRequestBean = AddCommitMsgRequestBean()
                addCommitMsgRequestBean.diffList = diffList;
                addCommitMsgRequestBean.optionLanguage = settingData.state.COMMIT_MSG_LANGUAGE_SELECTOR
                addCommitMsgRequestBean.charLimit = AppConstant.commitMsgLength.toInt()
                addCommitMsgRequestBean.ideVersion = CommonUtils.getIdeVersion()
                addCommitMsgRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
                addCommitMsgRequestBean.userToken = CommonUtils.localUserStore.getUserInfoModel()?.userToken

                //网络请求
                LogUtil.info("addCommitMsgRequestBean " + JSON.toJSONString(addCommitMsgRequestBean), false)
                var addCommitMsgResultModel = tsingYanProdService.addCommitMsg(addCommitMsgRequestBean)
                LogUtil.info("addCommitMsgResultModel " + JSON.toJSONString(addCommitMsgResultModel), false)

                //3、成功设置Commit Message，失败显示提示
                if (addCommitMsgResultModel != null){
                    val commitMsgStr = addCommitMsgResultModel.result
                    ApplicationManager.getApplication().invokeLater {
                        setCommitText(anActionEvent, commitMsgStr)
                        anActionEvent.presentation.icon = IconConstant.COMMIT_START
                        isProcess = false
                    }
                    submitTrace(project, true)
                } else {
                    showMessage(project, "Generate Commit Message failed.")
                    isProcess = false
                    submitTrace(project, false)
                }

            }
            progressTask?.start("Git变更信息生成中")
        } catch (e: Throwable){
            LogUtil.error("CodeFuse git error", e)
            showMessage(project, "Git变更信息生成失败")
            isProcess = false
        }
    }

    private fun showMessage(project: Project, msg:String){
        ApplicationManager.getApplication().invokeLater {
            NotificationUtils.notifyMessage(
                "CodeFuse通知",
                msg,
                project,
                null
            )
        }
    }

    private fun submitTrace(project: Project, rst: Boolean){
        var data: JSONObject = JSONObject();
        data.put("commitMsg", rst)
        tracerService.submitTypeAndData(project, TraceTypeEnum.COMMIT_MESSAGE_CLICK, data)
    }

    private fun setCommitText(anActionEvent: AnActionEvent, commitMessageStr: String){
        val commitMessage = VcsDataKeys.COMMIT_MESSAGE_CONTROL.getData(anActionEvent.getDataContext()) as CommitMessage
        try {
            commitMessage.setText(commitMessageStr)
        } catch (e: InterruptedException) {
            throw RuntimeException(e)
        } catch (e2: InvocationTargetException) {
            throw RuntimeException(e2)
        }
    }


    private fun getDiff(anActionEvent: AnActionEvent): MutableList<GitFileBean?> {
        val workflowHandler: Any? = anActionEvent.dataContext.getData(VcsDataKeys.COMMIT_WORKFLOW_HANDLER)
        if (workflowHandler == null) {
            return mutableListOf()
        } else {
            val changeList: MutableList<Any?> = mutableListOf()
            if (workflowHandler is AbstractCommitWorkflowHandler<*, *>) {
                // 在UI线程中操作
                ApplicationManager.getApplication().invokeAndWait {
                    val includedChanges: List<Change?> = workflowHandler.ui.getIncludedChanges()
                    if (includedChanges.isNotEmpty()) {
                        changeList.addAll(includedChanges)
                    }

                    val filePaths =
                        (anActionEvent.dataContext.getData(VcsDataKeys.COMMIT_WORKFLOW_HANDLER) as AbstractCommitWorkflowHandler<*, *>?)!!.ui.getIncludedUnversionedFiles()
                    if (filePaths.isNotEmpty()) {
                        val filePathIterator: Iterator<*> = filePaths.iterator()

                        while (filePathIterator.hasNext()) {
                            val filePath = filePathIterator.next() as FilePath
                            val change = Change(null as ContentRevision?, CurrentContentRevision(filePath))
                            changeList.add(change)
                        }
                    }
                }

                val gitFileList: MutableList<GitFileBean?> = mutableListOf()
                val changeListIterator: Iterator<*> = changeList.iterator()

                while (changeListIterator.hasNext()) {
                    val change = changeListIterator.next() as Change

                    try {
                        val isBinary =
                            if (change.afterRevision != null) change.afterRevision!!.file.fileType.isBinary else change.beforeRevision!!
                                .file.fileType.isBinary
                        if (!isBinary) {
                            val patches = IdeaTextPatchBuilder.buildPatch(
                                anActionEvent.project, Arrays.asList(change), Path.of(
                                    anActionEvent.project!!.basePath
                                ), false, true
                            )
                            if (patches.isNotEmpty()) {
                                val gitFileBean = GitFileBean()
                                // 使用 git diff 命令获取具体的改动内容
                                val filePath = if (change.afterRevision != null) change.afterRevision!!
                                    .file.path else (if (change.beforeRevision != null) change.beforeRevision!!.getFile().path else "")
                                if (!StringUtils.isBlank(filePath)) {
                                    gitFileBean.filePath = filePath
                                    val projectPath = anActionEvent.project!!.basePath
                                    when (change.type) {
                                        Change.Type.NEW -> {
                                            gitFileBean.status = GitFileStatus.ADDED
                                            // 直接读取新文件的内容
                                            if (Files.exists(Paths.get(filePath))) {
                                                val content = Files.readAllBytes(Paths.get(filePath))
                                                gitFileBean.content = String(content)
                                            }
                                        }

                                        Change.Type.DELETED -> {
                                            gitFileBean.status = GitFileStatus.DELETED
                                            // 从最新提交中读取文件内容
                                            val newFilePath = getRelativePath(projectPath!!, filePath)
                                            val diffCommand =
                                                listOf("git", "-C", projectPath, "show", "HEAD:$newFilePath")
                                            LogUtil.info("diffCommand deleted: $diffCommand", false)
                                            val processBuilder = ProcessBuilder(diffCommand)
                                            val process = processBuilder.start()
                                            val diffContent = process.inputStream.bufferedReader().readText()
                                            process.waitFor()
                                            gitFileBean.content = diffContent
                                        }

                                        else -> {
                                            gitFileBean.status = GitFileStatus.MODIFIED
                                            val newFilePath = getRelativePath(projectPath!!, filePath)
                                            // 使用 `git diff HEAD -- <file>` 获取修改文件的内容
                                            val diffCommand =
                                                listOf("git", "-C", projectPath, "diff", "HEAD", "--", newFilePath)
                                            LogUtil.info("diffCommand deleted: $diffCommand", false)
                                            val processBuilder = ProcessBuilder(diffCommand)
                                            val process = processBuilder.start()
                                            val diffContent = process.inputStream.bufferedReader().readText()
                                            process.waitFor()
                                            gitFileBean.content = diffContent
                                        }
                                    }
                                    gitFileList.add(gitFileBean)
                                }
                            }
                        }
                    } catch (exception: VcsException) {
                        LogUtil.debug("getDiff VcsException exception", exception)
                    } catch (exception: IOException) {
                        LogUtil.debug("getDiff IOException exception", exception)
                    }
                }

                return gitFileList
            } else {
                return mutableListOf()
            }
        }
    }

    class GitFileBean {
        var filePath: String? = null
        var content: String? = null
        var status: GitFileStatus? = GitFileStatus.MODIFIED
    }

    enum class GitFileStatus {
        ADDED,
        MODIFIED,
        DELETED
    }

    fun getRelativePath(projectPath: String, filePath: String): String {
        val project = Paths.get(projectPath)
        val file = Paths.get(filePath)
        return project.relativize(file).toString()
    }

}