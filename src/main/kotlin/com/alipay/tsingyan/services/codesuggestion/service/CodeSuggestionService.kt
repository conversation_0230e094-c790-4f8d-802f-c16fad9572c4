package com.alipay.tsingyan.services.codesuggestion.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.CodeFuseDialogWebBrowser
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager

/**
 * 代码解析的逻辑
 * author:jianzhi
 *
 * 2023年08月04日16:22:50
 */
class CodeSuggestionService: Disposable {
    private val LOGGER: Logger = Logger.getInstance(CodeSuggestionService::class.java)

    private val analysisCodeAlarm = CancellableAlarm(this)

    fun codeSuggestion(project: Project, selectCode: String, clickType: ClickType) {
        val webView = project.getService(WebViewService::class.java).getWebView()

        //已经初始化过了，直接使用即可
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            openToolWindow(project)
            codeSuggestion(project, selectCode, webView, clickType)
            return
        }

        //没有初始化过，需要等待初始化成功
        analysisCodeAlarm.cancelAllAndAddRequest({
            ApplicationManager.getApplication().invokeLater {
                openToolWindow(project)
            }

            //等待页面加载完成后再进行操作
            var count = 10
            while (true){
                if (--count <= 0){
                    return@cancelAllAndAddRequest
                }
                if (webView.getHasLoadedFinished()){
                    Thread.sleep(3500)
                    LogUtil.info("codeSuggestion invokeLater $count")
                    ApplicationManager.getApplication().invokeLater {
                        codeSuggestion(project, selectCode, webView, clickType)
                    }
                    return@cancelAllAndAddRequest
                }

                LOGGER.info("codeSuggestion wait Count $count")
                Thread.sleep(500)
            }

        }, 0)
    }


    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }


    private fun codeSuggestion(project: Project, selectedText: String, webView: CodeFuseDialogWebBrowser, clickType: ClickType){
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.CHAT_DATA.name
        val jsonObject = JSONObject()
        jsonObject.put("question", selectedText)
        jsonObject.put("intention", AppConstant.CHAT_INTENTION_SUGGESTION_V2)
        jsonObject.put("repo", ProjectCache.getGitData(project))
        jsonObject.put("enterType", clickType.toString())
        try {
            jsonObject.put("staticAnalysisInfo", JSON.toJSONString(
                StaticAnalysisInfo(FileEditorManager.getInstance(project).selectedTextEditor!!)))
        }catch (e: Throwable){
            LOGGER.debug("codeSuggestion staticAnalysisInfo error")
        }
        jsonObject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
        messageModel.message = jsonObject.toString()
        LOGGER.info("codeSuggestion sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
    }
}
