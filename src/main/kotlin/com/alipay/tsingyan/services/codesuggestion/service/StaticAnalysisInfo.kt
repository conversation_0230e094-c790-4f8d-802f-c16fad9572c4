package com.alipay.tsingyan.services.codesuggestion.service

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.roots.ProjectRootManager

class File(val file_path: String, val file_content: String)

// 1 based line number
class RangePair(val start_line: Int, val end_line: Int)
class Range(val file_path: String, val range_list: Array<RangePair>)
class Diff(val new_path: String, val diff: String)
class StaticAnalysisInfo {
    var files: Array<File>? = null
    var diffs: Array<Diff>? = null
    var ranges: Array<Range>? = null

    constructor(editor: Editor) {
        if (editor.document.text.length > 100 * 1024) {
            return
        }

        val activeVirtualFile = FileDocumentManager.getInstance().getFile(editor.document)!!
        val moduleBaseDir = ProjectRootManager.getInstance(editor.project!!).fileIndex.getContentRootForFile(activeVirtualFile)!!.path

        val filePath = FileDocumentManager.getInstance().getFile(editor.document)!!.path.substring(moduleBaseDir.length + 1)
        val rangePair = RangePair(
            editor.offsetToLogicalPosition(editor.selectionModel.selectionStart).line + 1,
            editor.offsetToLogicalPosition(editor.selectionModel.selectionEnd).line + 1,
        )
        this.files = arrayOf(File(filePath, editor.document.text))
        this.ranges = arrayOf(Range(filePath, arrayOf(rangePair)))
    }
}
