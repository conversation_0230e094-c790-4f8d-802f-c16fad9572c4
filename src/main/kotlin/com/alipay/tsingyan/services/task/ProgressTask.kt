import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProgressManager
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project

/**
 * 状态栏的后台任务
 * @author: jian<PERSON>
 * @date: 2024-08-19 16:26:36
 */
class ProgressTask(private val project: Project, private val taskRunnable: (ProgressIndicator) -> Unit) {

    private var task: Task.Backgroundable? = null
    private var indicator: ProgressIndicator? = null

    fun start(title: String) {
        task = object : Task.Backgroundable(project, title, true) {
            override fun run(indicator: ProgressIndicator) {
                indicator.isIndeterminate = false // 设置为非不确定状态
                <EMAIL> = indicator
                taskRunnable.invoke(indicator)
            }

            override fun onCancel() {
                LogUtil.info("Task was canceled", false)
            }

            override fun onSuccess() {
                LogUtil.info("Task completed successfully", false)
            }
        }

        ProgressManager.getInstance().run(task as Task.Backgroundable)
    }

    fun stop() {
        indicator?.let {
            if (!it.isCanceled) {
                it.cancel()
            }
        }
    }
}