package com.alipay.tsingyan.services.comment.service

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.model.comment.AddCommentRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.view.status.CodeFuseStatus
import com.alipay.tsingyan.view.status.CodeFuseStatusService
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.SelectionModel
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.codeStyle.CodeStyleManager
import javax.swing.SwingUtilities


/**
 * 添加注释的service
 * author jianzhi
 * 2023-06-29 10:51:40
 */
class AddCommentServiceImpl : AddCommentService, Disposable {
    private val LOGGER: Logger = Logger.getInstance(AddCommentServiceImpl::class.java)

    /**
     * 取消请求
     */
    private val addCommentAlarm = CancellableAlarm(this)
    private val tsingYanProdService = service<TsingYanProdService>()
    private val settingData = service<TsingYanSettingStore>()

    override fun requestComment(requestBean: AddCommentRequestBean, editor: Editor) {

        val virtualFile = VfsUtils.getVfsFileByEditor(editor)
        if (null != virtualFile){
            editor.project?.service<FileAITagService>()?.putTextToCodeTag(virtualFile)
        }

        //1、发起请求
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready)
        var originStartOff: Int
        var originEndOff: Int
        requestBean.intention = "CODE_GENERATE_COMMENT"
        requestBean.ideVersion = CommonUtils.getIdeVersion()
        requestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        requestBean.userToken = CommonUtils.localUserStore.getUserInfoModel()?.userToken
        editor.project?.let { requestBean.repo = ProjectCache.getGitData(it) }
        requestBean.fileUrl = AppConstant.FILE_URL
        requestBean.optionLanguage = settingData.state.ANNOTATION_LANGUAGE_SELECTOR
        ApplicationManager.getApplication().runReadAction(){
            requestBean.language = AntEditorUtil.getLanguage(editor)
            originStartOff = editor.selectionModel.selectionStart
            originEndOff = editor.selectionModel.selectionEnd
            requestBean.fileContent = editor.document.text
            addCommentAlarm.cancelAllAndAddRequest({
                try {
                    CodeFuseStatusService.notifyApplication(CodeFuseStatus.CompletionInProgress)
                    LogUtil.info("requestComment " + JSON.toJSONString(requestBean), false)
                    val commentResult = tsingYanProdService.addcomment(requestBean)
                    if (commentResult == null){
                        editor.project?.let { NotificationUtils.notifyMessage("CodeFuse", "生成注释失败", it, null) }
                    }
                    //2、去替换用户选中的内容
                    commentResult?.let {editor.project?.let { commentResult.content?.let { text -> replaceSelectedText(it, editor, text, originStartOff, originEndOff) } } }
                } catch (e: Throwable) {
                    LogUtil.info("requestComment error", e)
                } finally {
                    if (null != virtualFile){
                        editor.project?.service<FileAITagService>()?.removeTextToCodeTag(virtualFile)
                    }
                    CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready)
                }
            }, 20)
        }
    }

    override fun dispose() {
    }

    private fun getCurrentDocument(editor: Editor): Document? {
        return editor.document
    }

    private fun replaceText(document: Document, startOffset: Int, endOffset: Int, newText: String) {
        document.deleteString(startOffset, endOffset)
        document.insertString(startOffset, newText)
    }

    private fun replaceSelectedText(project: Project, editor: Editor, newText: String, originStartOffset: Int, originEndOffset: Int) {
        val document = getCurrentDocument(editor) ?: return

        var startOffset = -1
        ApplicationManager.getApplication().runReadAction(){
            startOffset = editor.selectionModel.selectionStart
        }

        //当startOff光标移动过，那么就取消本次插入
        if (originStartOffset != startOffset){
            LogUtil.info("replaceSelectedText originStartOffset != startOffset", false)
            return
        }

        LogUtil.info("replaceSelectedText $startOffset", false)
        LogUtil.info(newText, false)

        WriteCommandAction.runWriteCommandAction(project) {
            if (startOffset >= 0){
                replaceText(document, originStartOffset, originEndOffset, newText)
                val codeStyleManager = CodeStyleManager.getInstance(project)
                val psiFile = PsiDocumentManager.getInstance(editor.project!!).getPsiFile(editor.document)
                if (psiFile != null) {
                    codeStyleManager.reformatText(psiFile, originStartOffset, originStartOffset + newText.length)
                }
//                updateUI(editor)
            }
        }
    }

    private fun getSelectedText(editor: Editor): String? {
        val selectionModel: SelectionModel = editor.selectionModel
        return selectionModel.selectedText
    }

    private fun updateUI(editor: Editor) {
        SwingUtilities.invokeLater {
            editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
        }
    }
}