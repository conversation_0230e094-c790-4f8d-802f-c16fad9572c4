package com.alipay.tsingyan.services.comment.action

import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.comment.AddCommentRequestBean
import com.alipay.tsingyan.services.comment.service.AddCommentService
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.NotificationUtils
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.actionSystem.UpdateInBackground

/**
 * 添加注释的Action
 * author jianzhi
 * 2023-06-29 10:51:40
 */
class AddCommentAction : AnAction(), UpdateInBackground {
    private val addCommentService = service<AddCommentService>()
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    private val editorManagerService = service<EditorManagerService>()
    val tracerService = service<CodeFuseTracerService>()


    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        if (!editorManagerService.checkLogin()) {
            project.let { NotificationUtils.showBeginLoginMessage(it) }
            return
        }

        if (project.service<FileAITagService>().isTextToCodeEnableWithWarning(e)){
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        if (editor != null && !project.isDisposed) {
            val selectionModel = editor.selectionModel
            val selectedText = selectionModel.selectedText ?: ""
            if (selectedText.isEmpty() || selectedText.isBlank()) {
                NotificationUtils.notifyMessage("CodeFuse", "未选中代码", project, null)
                return
            }

            val requestBean = AddCommentRequestBean()
            requestBean.question = selectedText
            requestBean.enterType = ClickType.RIGHT.name
            addCommentService.requestComment(requestBean, editor)
//            tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_COMMENT, null)
        }
    }

    override fun update(e: AnActionEvent) {
        super.update(e)

        if (e.project == null) {
            e.presentation.isEnabled = false
            return
        }

        if (!editorManagerService.checkLogin()) {
            e.project?.let { e.presentation.isVisible = false }
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        val project = e.project
        if (editor != null && project != null && !project.isDisposed) {
            val selectionModel = editor.selectionModel
            val selectedText = selectionModel.selectedText ?: ""
            if (selectedText.isEmpty() || selectedText.isBlank()) {
                e.presentation.isEnabled = false
                return
            }
        }

        e.presentation.isEnabled = true
    }

}
