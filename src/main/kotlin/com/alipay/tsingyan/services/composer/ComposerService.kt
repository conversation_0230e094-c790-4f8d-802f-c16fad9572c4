package com.alipay.tsingyan.services.composer

import cn.hutool.core.io.FileUtil
import com.alipay.tsingyan.inline2.bean.FastApplyBean
import com.alipay.tsingyan.model.composer.FileActionInfoModel
import com.alipay.tsingyan.model.composer.ProjectInfoModel
import com.alipay.tsingyan.model.composer.Tag
import com.alipay.tsingyan.model.enums.ComposerActionEnum
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.vfs.VirtualFile
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.apache.commons.lang3.StringUtils
import java.util.*

/**
 * 用于 Composer 相关服务
 */
class ComposerService(val project: Project) : Disposable {
    private val logger: Logger = Logger.getInstance(this::class.java)
    private val myProject: Project = project
    private val serviceMap: HashMap<String, ComposerCodeApplyService> = HashMap()
    private val streamQueueMap: HashMap<String, LinkedList<FastApplyBean>> = HashMap()

    /**
     * 流式 Apply 队列，
     * 业务场景，一个文件在一个问题中可能有多次 FastApply。做如下调整。
     * 1. 保证同一个文件，每次进行中的流式只有一个。
     * 2. 返回的流式输出内容加上本次Diff和全部Diff。
     * 3.
     */

    @Synchronized
    fun applyCodeStreamQueue(bean: FastApplyBean) {
        val queue = streamQueueMap.getOrDefault(bean.fileUrl, LinkedList<FastApplyBean>())
        if (queue.isEmpty()) {
            queue.add(bean)
            GlobalScope.launch {
                while (queue.isNotEmpty()) {
                    val popBean = queue.pop()
                    applyCodeStream(popBean)
                }
            }
        } else {
            queue.add(bean)
        }
    }

    /**
     * 流式 Apply 操作
     */
    suspend fun applyCodeStream(bean: FastApplyBean) {
        logger.info("Composer Stream apply start")
        val service = serviceMap
            .getOrPut(bean.fileUrl!!) { ComposerCodeApplyService() }
        service.applyCodeStream(bean, project)
        delay(100)
        while (service.isProcessing()) {
            delay(100)
        }
    }

    // 用于关闭文件后的还原操作
    fun closeFile(virtualFile: VirtualFile) {
        logger.info("Composer closeFile")
        val filePath = VfsUtils.getRelativeFilePath(virtualFile, myProject)
        if (StringUtils.isBlank(filePath) || !serviceMap.containsKey(filePath)) return

        serviceMap[filePath]?.closeFile()
        serviceMap.remove(filePath)
        project.service<FileAITagService>().removeAllTag(virtualFile)
    }

    fun beforeClose(virtualFile: VirtualFile) {
        val filePath = VfsUtils.getRelativeFilePath(virtualFile, myProject)
        if (StringUtils.isBlank(filePath) || !serviceMap.containsKey(filePath)) return
        val codeApplyService = serviceMap.get(filePath) ?: return

        if (!codeApplyService.isAutoClose() && project.service<FileAITagService>().matchTag(virtualFile, Tag.ACTION_TO_CODE)) {
            val file = virtualFile.toNioPath().toFile()
            val rejectCode = codeApplyService.getRejectCode()
            // 如果存在未应用的文件，则提醒用户
            invokeLater {
                val result = Messages
                    .showOkCancelDialog(
                        myProject,
                        "当前文件有代码变更未处理，是否采纳变更",
                        "CodeFuse",
                        "采纳",
                        "拒绝",
                        CodeFuseIcons.CodeFuseIcon
                    )
                if (result == Messages.CANCEL) {
                    if (FileUtil.exist(file)) {
                        FileUtil.writeUtf8String(rejectCode, file)
                    }
                }
            }
        }
    }

    fun acceptOrReject(fileList: List<FileActionInfoModel>): List<FileActionInfoModel> {
        val list = ArrayList<FileActionInfoModel>()
        if (fileList.isEmpty()) return list
        fileList.forEach {
            if (StringUtils.isBlank(it.filePath)) return@forEach
            val fileInfoModel = FileActionInfoModel()
            fileInfoModel.filePath = it.filePath
            val codeApplyService = serviceMap.get(it.filePath)
            if (null == codeApplyService) {
                fileInfoModel.action = ComposerActionEnum.none
            } else {
                val action = it.action
                if (ComposerActionEnum.accept == action) {
                    fileInfoModel.action = ComposerActionEnum.accept
                    invokeLater {
                        codeApplyService.accept(false)
                    }
                } else if (ComposerActionEnum.reject == action) {
                    invokeLater {
                        codeApplyService.reject(false)
                    }
                    fileInfoModel.action = ComposerActionEnum.reject
                }
            }
            list.add(fileInfoModel)
        }
        return list
    }

    /**
     * 获取项目的信息
     */
    fun getProjectInfo(): ProjectInfoModel {
        val result = ProjectInfoModel()
        //获取项目的仓库分支信息
        result.repo = CommonUtils.getProjectGitRepository(project)
        result.projectUrl = VfsUtils.getProjectBaseDir(project)
        result.branch = CommonUtils.getBranch(project)
        result.commitID = CommonUtils.getLatestCommitId(project)
        VfsUtils.getEditorSelectedFile(project)?.let { virtualFile ->
            result.fileUrl = VfsUtils.getRelativeFilePath(virtualFile, project)
            result.fileContent = VfsUtils.getContent(virtualFile)
        }
        result.recentFilesInfo = project.service<RecentFilesService>().getRecentFiles(project)
        return result
    }

    /**
     * 刷新 Diff 逻辑
     */
    fun refreshEditorDiffView(path: String) {
        serviceMap[path]?.refreshEditorDiffView()
    }

    fun markAutoCloseTag(relativeFilePath: String) {
        serviceMap.get(relativeFilePath)?.markAutoCloseTag()
    }

    /**
     * 取消操作
     */
    fun cancelApply(fileList: List<String>) {
        // 理论上，当前编辑器正在变更的文件就是正在流式的文件
        runInEdt {
            val vfsFile = FileEditorManager.getInstance(project).selectedEditor?.file ?: return@runInEdt
            NotificationUtils.showApplyCancel(project, vfsFile)
        }
        fileList.forEach {
            serviceMap[it]?.doCancel()
        }
    }

    override fun dispose() {
        serviceMap.clear()
    }
}