package com.alipay.tsingyan.services.composer

import cn.hutool.core.io.FileUtil
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.jgit.JGitService
import com.alipay.tsingyan.utils.json.JsonUtil
import org.eclipse.jgit.api.ResetCommand
import org.eclipse.jgit.lib.Constants
import org.eclipse.jgit.revwalk.RevCommit

/**
 * 快照服务
 * 1、考虑到即不改变用户原有的代码，又要管理 AI 变更的代码，还要在回退的时候，不删除用户的代码，现在做如下操作
 */
class SnapShotService(
    private val repoPath: String,
    private val repoId: String,
    private val projectName: String,
) {
    private val gitService = JGitService(repoPath, repoId, projectName)
    private var firstCommitId = ""

    fun init(msg: Messgae) {
        clean()
        gitService.initRepo()
        try {
            // 这里初始化Commit失败，也没什么问题
            gitService.doAddAndCommit(msg.statusList.map { it.filePath }, JsonUtil.toJSONString(msg))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun clean() {
        gitService.del()
    }

    fun gc() {
        gitService.gc()
    }

    /**
     * 保存快照
     * 实际操作为 git commit
     */
    fun doSnap(msg: Messgae, reAdd: Boolean = true): String {
        LogUtil.info("执行快照保存 msg:[${JsonUtil.toJSONString(msg)}]")
        val fileList = msg.statusList.map { it.filePath }
        val revCommit = gitService.doAddAndCommit(fileList, JsonUtil.toJSONString(msg), reAdd)
        if (firstCommitId.isBlank()) {
            firstCommitId = revCommit!!.name
        }
        return revCommit!!.name
    }

    /**
     * 添加文件到缓存
     */
    fun addFile(pathList: List<String>) {
        gitService.doAdd(pathList)
    }

    /**
     * 快照回溯，逻辑如下
     * 1. 获取当前分支与目标分支之间的 Diff 信息
     * 2. 获取当前分支与目标分支之间所有 Commit 的描述信息,解析出文件类型（AI 新增 和 OLD）
     * 3. 根据 Diff 信息和文件类型，变更对应文件内容，
     *
     * 注意：这里不会变更提交的历史线
     */
    fun doHistoryReview(
        commitID: String,
        delCall: ((path: String) -> Unit)? = null,
        modifyCall: ((path: String) -> Unit)? = null,
        beforeDelCall: ((path: String) -> Unit)? = null,
    ) {
        val headCommit = gitService.resolveCommitID(Constants.HEAD)
        // 如果是回退到最新代码，则直接重置到最新代码
        if (headCommit.name.equals(commitID)) {
            gitService.resetCommit(headCommit)
            JsonUtil.parseObject(headCommit.fullMessage, Messgae::class.java).statusList.map {
                modifyCall?.invoke(it.filePath)
            }
            return
        }

        // 解析出时间的新老文件类型
        val allCommit = mutableListOf<RevCommit>()

        // TODO 这里的代码看起来很挫，但没有想到更优雅的写法
        for (revCommit in gitService.getLog()) {
            // 如果是回退到初始化节点，则要将初始化节点也添加到文件列表内
            if (firstCommitId == commitID) {
                allCommit.add(revCommit)
                if (revCommit.name.equals(commitID)) {
                    break
                }
            } else {
                if (revCommit.name.equals(commitID)) {
                    break
                } else {
                    allCommit.add(revCommit)
                }
            }
        }

        // 文件列表区分
        val orderMap = allCommit.withIndex().associate { (index, item) -> item.name to index }
        val realMap = getFirstMap(allCommit)
        val gitDiffFileList = gitService.getDiffWithHead(commitID)

        /**
         * 遍历Diff列表，根据存储的文件状态，
         * 如果是项目老文件，使用老代码，如果是AI文件，则直接删除
         */
        for (fileDiff in gitDiffFileList) {
            val filePath = fileDiff.filePath

            val fileStatus = realMap[filePath]
            if (null != fileStatus) {
                val fileAbsolutePath = gitService.repoPath + FileUtil.FILE_SEPARATOR + filePath
                if (FileType.OLD == fileStatus.type) {
                    val content = if (orderMap.getOrDefault(fileStatus.commitID, 0) == allCommit.size) {
                        fileDiff.newCode
                    } else {
                        gitService.getCommitContent(fileStatus.commitID!!, fileStatus.filePath)
                    }
                    // 将文件内容写入编辑器中
                    FileUtil.writeUtf8String(content, fileAbsolutePath)
                    modifyCall?.invoke(fileAbsolutePath)
                } else {
                    beforeDelCall?.invoke(filePath)
                    val path = FileUtil.file(fileAbsolutePath).parentFile.path
                    FileUtil.del(fileAbsolutePath)
                    delCall?.invoke(path)
                }
            }
        }
    }


    /**
     * git soft reset
     */
    fun doResetSnap(commitID: String) {
        gitService.resetCommit(commitID, ResetCommand.ResetType.SOFT)
    }

    /**
     * 根据所有的日志信息，获取项目实际的文件列表信息
     */
    private fun getFirstMap(commitList: MutableList<RevCommit>): Map<String, FileStatus> {
        val fileStatusList = commitList.map {
            val messgae = JsonUtil.parseObject(it.fullMessage, Messgae::class.java)
            messgae.statusList.forEach { status -> status.commitID = it.name }
            messgae
        }.flatMap { it.statusList }

        // 根据文件路径分组，然后再根据文件时间戳排序
        return fileStatusList
            .groupBy { it.filePath }
            .mapValues { (_, grorp) -> grorp.minByOrNull { it.time }!! }
    }
}


/**
 * 变更的文件信息
 */
data class Messgae(
    var statusList: List<FileStatus> = emptyList(),
    var extend: String? = null,
)

/**
 * 记录的文件状态
 */
data class FileStatus(
    val filePath: String,
    val type: FileType = FileType.OLD,
    val time: Long = System.currentTimeMillis(),
    var commitID: String? = null,
)

enum class FileType {
    AI,
    OLD,
}

// 历史回溯时，记录变更的文件，以刷新编辑器
data class ReviewLog(
    val filePath: String,
    val type: ReviewType = ReviewType.MODIFY,
)

enum class ReviewType {
    ADD,
    MODIFY,
    DEL,
}

