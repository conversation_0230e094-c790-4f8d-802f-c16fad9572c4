package com.alipay.tsingyan.services.composer


import com.alipay.tsingyan.services.composer.ui.MyEditorTopToolbar
import com.alipay.tsingyan.utils.CommonUtils
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.EditorNotifications

class ComposerEditorProvider : EditorNotifications.Provider<MyEditorTopToolbar>() {
    override fun getKey(): Key<MyEditorTopToolbar?> = Key.create<MyEditorTopToolbar?>("Code Apply Top Toolbar")

    override fun createNotificationPanel(
        file: VirtualFile,
        fileEditor: FileEditor,
        project: Project,
    ): MyEditorTopToolbar? {
        removeOldPanel(project, fileEditor)
        return if (true == fileEditor.getUserData(ComposerKey.SHOW)) {
            val myEditorTopToolbar = MyEditorTopToolbar().apply {
                addBtn("查看变更") {
                    fileEditor.getUserData(ComposerKey.DIFF)?.invoke()
                }
                addBtn("拒绝") {
                    fileEditor.getUserData(ComposerKey.CANCEL)?.invoke()
                }
                addBtn("采纳", true) {
                    fileEditor.getUserData(ComposerKey.APPLY)?.invoke()
                }
            }
            // 判断一下当前UI是否要禁用
            val enableStatus = (true != fileEditor.getUserData(ComposerKey.DISABLE))
            myEditorTopToolbar.updateStatus(enableStatus)
            myEditorTopToolbar
        } else {
            null
        }
    }


    /**
     * 获取顶部的ToolBar，如果有旧的，移除掉
     */
    private fun removeOldPanel(project: Project, fileEditor: FileEditor) {
        if (CommonUtils.getBigVersion().toString().startsWith("21")) {
            val fileEditorManager = FileEditorManager.getInstance(project)
            if (fileEditorManager is FileEditorManagerImpl) {
                runInEdt {
                    val topComponents = fileEditorManager.getTopComponents(fileEditor)
                    for (component in topComponents) {
                        if (component is MyEditorTopToolbar) {
                            fileEditorManager.removeTopComponent(fileEditor, component)
                        }
                    }
                }
            }
        }
    }
}




