package com.alipay.tsingyan.services.composer.flow

import cn.hutool.core.io.FileUtil
import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.mcp.server.base.ParamDesc
import com.alipay.tsingyan.mcp.server.model.NLExtend
import com.alipay.tsingyan.model.log.UploadLogRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import org.jetbrains.concurrency.runAsync
import kotlin.math.min
import kotlin.math.abs
import kotlin.math.log

/**
 * 字符串替换工具的辅助类，移植自 Python 版本的 common.py
 * 保持与原版逻辑完全一致
 */
object StrReplaceUtil {

    private val logger = LogUtil

    // 常量
    const val MAX_RESPONSE_LEN = 50000
    const val TRUNCATED_MESSAGE = "<response clipped><NOTE>To save on context only part of this file has been shown to you.</NOTE>"
    const val SNIPPET_CONTEXT_LINES = 4

    /**
     * 直接生成文件
     */
    fun fullReplace(
        project: Project,
        path: String,
        newContent: String,
        appendNewLine: Boolean,
    ): NLExtend<String> {
        val resultExtend = NLExtend<String>()

        // 判断文件路径是否匹配
        val basePath = VfsUtils.getPath(path, project)
//        val localVfsFile = VfsUtils.getLocalVfsFile(basePath)
//        if (null != localVfsFile) {
//            resultExtend.nlResult = "File already exists: $basePath"
//            return resultExtend
//        }

        try {
            FileUtil.touch(basePath)
        } catch (e: Exception) {
            resultExtend.nlResult = "Cannot resolve path: $basePath"
            LogUtil.info("fullReplace error,", e)
            return resultExtend
        }


        val newCode = if (appendNewLine) {
            newContent + FileUtil.getLineSeparator()
        } else {
            newContent
        }

        resultExtend.success(
            nlResult = "File saved. Saved file $basePath",
            data = newCode
        )
        return resultExtend
    }

    /**
     * 成功工具响应
     */
    fun successToolResponse(message: String): Map<String, Any> {
        return mapOf<String, Any>("type" to "success", "message" to message)
    }

    /**
     * 错误工具响应
     */
    fun errorToolResponse(message: String): Map<String, Any> {
        return mapOf<String, Any>("type" to "error", "message" to message)
    }

    /**
     * 移除每行的尾随空白
     */
    fun removeTrailingWhitespace(text: String): String {
        val lineEnding = if (text.contains("\r\n")) "\r\n" else "\n"
        val lines = text.split(lineEnding)
        val trimmedLines = lines.map { it.trimEnd() }
        return trimmedLines.joinToString(lineEnding)
    }

    /**
     * 截断内容如果太长
     */
    fun maybeTruncate(content: String, truncateAfter: Int = MAX_RESPONSE_LEN): String {
        return if (content.length <= truncateAfter) {
            content
        } else {
            content.take(truncateAfter) + TRUNCATED_MESSAGE
        }
    }

    /**
     * 标准化行结束符为 '\n'
     * 🔧 文本处理的第一步：将所有行结束符统一为 \n
     * 确保在不同操作系统上处理行为一致
     */
    fun normalizeLineEndings(text: String): String {
        return text.replace("\r\n", "\n")
    }

    /**
     * 检测文本中使用的行结束符类型
     * 🔧 关键作用：保存原始行结束符信息，供后续恢复使用
     * - Windows: \r\n
     * - Unix/Linux/Mac: \n
     */
    fun detectLineEnding(text: String): String {
        return if (text.contains("\r\n")) "\r\n" else "\n"
    }

    /**
     * 为编辑准备文本：标准化行结束符并移除行尾空白
     * 🔧 返回两个字段：
     * - content: 标准化后的内容（在 single_str_replace 中使用）
     * - originalLineEnding: 原始行结束符（在 prepare_tool_response 中使用）
     */
    fun prepareTextForEditing(text: String): Map<String, String> {
        val originalLineEnding = detectLineEnding(text)
        val content = normalizeLineEndings(removeTrailingWhitespace(text))
        return mapOf("content" to content, "originalLineEnding" to originalLineEnding)
    }

    /**
     * 恢复原始行结束符格式
     * 🔧 这是 originalLineEnding 真正被使用的地方！
     * 在文件写入前调用，确保文件格式的跨平台兼容性
     */
    fun restoreLineEndings(text: String, lineEnding: String): String {
        return if (lineEnding == "\n") {
            text
        } else {
            text.replace("\n", lineEnding)
        }
    }

    /**
     * 检测代码的缩进风格
     * 🔧 智能缩进修正的基础：
     * - 统计使用 Tab 和空格缩进的行数
     * - 确定主要的缩进风格和大小
     * - 用于当直接匹配失败时的缩进修正
     */
    fun detectIndentation(content: String): Map<String, Any> {
        val lines = content.split("\n")
        var spaceIndents = 0  // 使用空格缩进的行数
        var tabIndents = 0    // 使用Tab缩进的行数
        var spaceSize = 0     // 空格缩进的大小

        for (line in lines) {
            if (line.trim().isEmpty()) continue  // 跳过空行

            // 检测行首的空格和Tab缩进
            val leadingSpaces = Regex("^( +)").find(line)
            val leadingTabs = Regex("^(\t+)").find(line)

            when {
                leadingSpaces != null -> {
                    spaceIndents++
                    if (spaceSize == 0) {  // 记录第一次遇到的空格缩进大小
                        spaceSize = leadingSpaces.groupValues[1].length
                    }
                }
                leadingTabs != null -> {
                    tabIndents++
                }
            }
        }

        // 根据统计结果确定主要缩进风格
        return if (tabIndents > spaceIndents) {
            mapOf<String, Any>("type" to "tab", "size" to 1)
        } else {
            mapOf<String, Any>("type" to "space", "size" to (spaceSize.takeIf { it > 0 } ?: 2))
        }
    }

    /**
     * 移除一个缩进级别
     * 🔧 智能缩进修正的核心：
     * 当用户提供的代码片段缩进与实际文件中缩进不一致时，
     * 移除一级缩进后重新尝试匹配
     */
    fun removeOneIndentLevel(text: String, indentation: Map<String, Any>): String {
        val lines = text.split("\n")
        val pattern = when (indentation["type"]) {
            "tab" -> Regex("^\t")  // 移除行首的一个Tab
            else -> {
                // 移除行首的1到size个空格
                val size = indentation["size"] as Int
                Regex("^ {1,$size}")
            }
        }

        return lines.map { pattern.replace(it, "") }.joinToString("\n")
    }

    /**
     * 检查文本的所有非空行是否都有指定类型的缩进
     * 🔧 缩进修正的前置条件：
     * 确保文本确实是有缩进的，避免误判和不必要的处理
     */
    fun allLinesHaveIndent(text: String, indentation: Map<String, Any>): Boolean {
        val lines = text.split("\n")

        for (line in lines) {
            if (line.trim().isEmpty()) continue  // 跳过空行

            when (indentation["type"]) {
                "tab" -> {
                    if (!Regex("^\t").containsMatchIn(line)) {
                        return false
                    }
                }
                else -> {
                    if (!Regex("^ +").containsMatchIn(line)) {
                        return false
                    }
                }
            }
        }

        return true
    }

    /**
     * 在内容中查找所有匹配的字符串位置 - 核心匹配算法
     * 🔧 支持两种匹配模式：
     * 1. 单行匹配：支持部分行匹配（子字符串匹配）
     * 2. 多行匹配：精确的跨行字符串匹配
     *
     * 这是 str_replace_editor 支持部分行替换的关键！
     */
    fun findMatches(content: String, strToFind: String): List<Map<String, Int>> {
        val contentLines = content.split("\n")
        val strLines = strToFind.split("\n")
        val matches = mutableListOf<Map<String, Int>>()

        // 边界情况检查
        if (strToFind.trim().isEmpty() || strLines.size > contentLines.size) {
            return matches
        }

        // 🔧 情况1：单行匹配（支持部分行匹配）
        if (strLines.size == 1) {
            for ((index, line) in contentLines.withIndex()) {
                if (strToFind in line) {  // 🔧 关键：使用 in 操作符，支持部分匹配！
                    matches.add(mapOf<String, Int>(
                        "startLine" to index,
                        "endLine" to index
                    ))
                }
            }
            return matches
        }

        // 🔧 情况2：多行匹配（精确匹配）
        val contentText = content
        val searchText = strToFind
        var startIndex = 0

        // 循环查找所有匹配位置
        while (true) {
            val foundIndex = contentText.indexOf(searchText, startIndex)
            if (foundIndex == -1) break

            // 🔧 通过统计换行符数量计算行号
            val textBeforeMatch = contentText.substring(0, foundIndex)
            val textUpToEndOfMatch = contentText.substring(0, foundIndex + searchText.length)

            val startLine = textBeforeMatch.count { it == '\n' }
            val endLine = textUpToEndOfMatch.count { it == '\n' }

            matches.add(mapOf<String, Int>(
                "startLine" to startLine,
                "endLine" to endLine
            ))

            startIndex = foundIndex + 1
        }

        return matches
    }

    // ================================
    // 新增：按行匹配和模糊匹配功能
    // ================================

    /**
     * 模糊匹配阈值
     */
    private const val FUZZY_MATCH_THRESHOLD = 0.9

    /**
     * 自定义trim函数，移除所有空白字符（空格、Tab、换行等）
     * 🔧 比标准trim更严格，确保空白字符处理一致性
     */
    fun customTrim(str: String?): String {
        if (str == null) return ""

        var start = 0
        var end = str.length - 1

        // 找到开头第一个非空白字符
        while (start < str.length && isWhitespace(str[start])) {
            start++
        }

        // 找到结尾最后一个非空白字符
        while (end >= 0 && isWhitespace(str[end])) {
            end--
        }

        // 如果全是空白字符，返回空字符串
        return if (start > end) "" else str.substring(start, end + 1)
    }

    /**
     * 判断字符是否为空白字符
     * 🔧 包含空格、Tab、换行符、回车符、换页符
     */
    private fun isWhitespace(ch: Char): Boolean {
        return ch == ' ' || ch == '\t' || ch == '\n' || ch == '\r' || ch == '\u000C'
    }

    /**
     * 计算两个字符串的Levenshtein距离相似度
     * 🔧 用于模糊匹配，返回0.0-1.0之间的相似度值
     */
    fun calculateSimilarity(s1: String, s2: String): Double {
        val maxLength = maxOf(s1.length, s2.length)
        return if (maxLength > 0) {
            val distance = levenshteinDistance(s1, s2)
            (maxLength - distance) / maxLength.toDouble()
        } else {
            1.0
        }
    }

    /**
     * 计算Levenshtein距离（编辑距离）
     * 🔧 衡量两个字符串之间的差异程度
     */
    fun levenshteinDistance(s1: String, s2: String): Int {
        val len1 = s1.length
        val len2 = s2.length

        // 创建距离矩阵
        val dp = Array(len1 + 1) { IntArray(len2 + 1) }

        // 初始化矩阵
        for (i in 0..len1) dp[i][0] = i
        for (j in 0..len2) dp[0][j] = j

        // 动态规划计算最小编辑距离
        for (i in 1..len1) {
            for (j in 1..len2) {
                val cost = if (s1[i-1] == s2[j-1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i-1][j] + 1,     // 删除
                    dp[i][j-1] + 1,     // 插入
                    dp[i-1][j-1] + cost // 替换
                )
            }
        }

        return dp[len1][len2]
    }

    /**
     * 模糊匹配判断两个字符串是否相似
     * 🔧 使用Levenshtein距离和阈值判断
     */
    fun isFuzzyMatch(s1: String, s2: String, threshold: Double = FUZZY_MATCH_THRESHOLD): Boolean {
        val s1Trimmed = customTrim(s1)
        val s2Trimmed = customTrim(s2)
        val similarity = calculateSimilarity(s1Trimmed, s2Trimmed)
        return similarity >= threshold
    }

    /**
     * 去除字符串开头和结尾的空白行
     * 🔧 预处理搜索内容，提高匹配成功率
     * 🔧 使用Kotlin标准库的isBlank()方法，避免与customTrim逻辑重复
     */
    fun removeLeadingAndTrailingEmptyLines(str: String): String {
        val lines = str.split("\n")
        var start = 0
        var end = lines.size - 1

        // 去除开头的空行（使用标准库方法判断空白行）
        while (start <= end && lines[start].isBlank()) {
            start++
        }

        // 去除结尾的空行（使用标准库方法判断空白行）
        while (end >= start && lines[end].isBlank()) {
            end--
        }

        // 重新组合字符串
        return if (start > end) "" else lines.subList(start, end + 1).joinToString("\n")
    }

    /**
     * 按行匹配查找替换位置 - 新的容错匹配算法
     * 🔧 核心改进：
     * 1. 按行逐步匹配，支持跳过空行
     * 2. 使用模糊匹配处理细微差异（如空格、缩进不一致）
     * 3. 更强的容错能力，减少因格式差异导致的匹配失败
     *
     * @param content 文件内容
     * @param searchContent 要查找的内容
     * @return 匹配结果，包含startLine和endLine，如果未找到返回null
     */
    fun findMatchesByLine(content: String, searchContent: String): Map<String, Int>? {
        // 🔧 第一步：预处理搜索内容，去除前后空白行
        val processedSearchContent = removeLeadingAndTrailingEmptyLines(searchContent)
        if (processedSearchContent.isEmpty()) {
            return null
        }

        val searchLines = processedSearchContent.split("\n")
        val contentLines = content.split("\n")

        var searchIndex = 0      // 当前匹配的搜索内容行索引
        var contentIndex = 0     // 当前检查的文件内容行索引
        var matchStartLine = -1  // 匹配开始的行号

        // 🔧 第二步：逐行匹配算法
        while (searchIndex < searchLines.size && contentIndex < contentLines.size) {
            val trimmedSearchLine = customTrim(searchLines[searchIndex])
            val trimmedContentLine = customTrim(contentLines[contentIndex])

            // 🔧 跳过搜索内容中的空行
            if (trimmedSearchLine.isEmpty()) {
                searchIndex++
                continue
            }

            // 🔧 跳过文件内容中的空行
            if (trimmedContentLine.isEmpty()) {
                contentIndex++
                continue
            }

            // 🔧 第三步：行匹配判断（精确匹配 + 模糊匹配）
            val isMatch = trimmedSearchLine == trimmedContentLine ||
                    isFuzzyMatch(trimmedSearchLine, trimmedContentLine)

            if (isMatch) {
                // 🔧 匹配成功：记录开始行号，继续匹配下一行
                if (matchStartLine == -1) {
                    matchStartLine = contentIndex
                }
                searchIndex++
                contentIndex++
            } else {
                // 🔧 匹配失败：重置搜索状态，从下一行重新开始
                searchIndex = 0
                matchStartLine = -1
                contentIndex++
            }
        }

        // 🔧 第四步：返回匹配结果
        return if (searchIndex == searchLines.size && matchStartLine != -1) {
            // 所有搜索行都匹配成功
            mapOf(
                "startLine" to matchStartLine,
                "endLine" to (contentIndex - 1)
            )
        } else {
            // 匹配失败
            null
        }
    }

    /**
     * 使用按行匹配方式执行内容替换
     * 🔧 相比原来的精确字符串匹配，这种方式有更强的容错能力
     *
     * @param content 原始内容
     * @param searchContent 要查找的内容
     * @param newContent 新内容
     * @return 替换后的内容，如果匹配失败返回原内容
     */
    fun lineSearchAndReplace(content: String, searchContent: String, newContent: String): String? {
        val matchResult = findMatchesByLine(content, searchContent)
            ?: return null // 🔧 匹配失败，返回null表示需要使用其他方式

        val startLine = matchResult["startLine"]!!
        val endLine = matchResult["endLine"]!!

        // 🔧 执行替换：将匹配的行范围替换为新内容
        val contentLines = content.split("\n").toMutableList()

        // 移除匹配的行范围
        for (i in endLine downTo startLine) {
            contentLines.removeAt(i)
        }

        // 在原位置插入新内容
        val newLines = newContent.split("\n")
        contentLines.addAll(startLine, newLines)

        return contentLines.joinToString("\n")
    }

    /**
     * 创建文本片段
     */
    fun createSnippet(
        content: String,
        replacementStartLine: Int,
        replacementNumLines: Int,
        snippetContextLines: Int
    ): Map<String, Any> {
        val startLine = (replacementStartLine - snippetContextLines).coerceAtLeast(0)
        val endLine = replacementStartLine + replacementNumLines - 1 + snippetContextLines

        val normalizedContent = content.replace("\r\n", "\n")
        val lines = normalizedContent.split("\n")
        val snippet = lines.subList(startLine, min(endLine + 1, lines.size)).joinToString("\n")

        return mapOf<String, Any>("snippet" to snippet, "startLine" to startLine)
    }

    /**
     * 创建带行号的格式化片段
     */
    fun createSnippetStr(
        content: String,
        replacementStartLine: Int,
        replacementNumLines: Int,
        snippetContextLines: Int
    ): String {
        val result = createSnippet(content, replacementStartLine, replacementNumLines, snippetContextLines)
        val snippet = result["snippet"] as String
        val startLine = result["startLine"] as Int

        return snippet.split("\n").mapIndexed { i, line ->
            "${(i + startLine + 1).toString().padStart(6)}\t$line"
        }.joinToString("\n")
    }


    /**
     * 查找重叠的条目
     */
    fun findOverlappingEntry(
        currentEntry: Map<String, Any>,
        strReplaceEntries: List<Map<String, Any>>
    ): Map<String, Any>? {
        val startLine = currentEntry["old_str_start_line_number"] as? Int ?: return null
        val endLine = currentEntry["old_str_end_line_number"] as? Int ?: return null

        for (entry in strReplaceEntries) {
            if (currentEntry["index"] == entry["index"] ||
                entry["old_str_start_line_number"] == null ||
                entry["old_str_end_line_number"] == null) {
                continue
            }

            val rangeStartLine = entry["old_str_start_line_number"] as? Int ?: continue
            val rangeEndLine = entry["old_str_end_line_number"] as? Int ?: continue

            // Check for overlap
            if ((startLine <= rangeStartLine && rangeStartLine <= endLine) ||
                (startLine <= rangeEndLine && rangeEndLine <= endLine) ||
                (rangeStartLine <= startLine && startLine <= rangeEndLine)) {
                return entry
            }
        }

        return null
    }

    /**
     * 生成重叠条目的错误消息
     */
    fun genOverlappingEntryErrorMessage(
        currentEntry: Map<String, Any>,
        overlappingEntry: Map<String, Any>
    ): String {
        val curStart = (currentEntry["old_str_start_line_number"] as? Int ?: 0) + 1
        val curEnd = (currentEntry["old_str_end_line_number"] as? Int ?: 0) + 1
        val overlapStart = (overlappingEntry["old_str_start_line_number"] as? Int ?: 0) + 1
        val overlapEnd = (overlappingEntry["old_str_end_line_number"] as? Int ?: 0) + 1

        return """old_str line numbers range overlaps with another entry.
This entry range: [$curStart-$curEnd]
Overlapping entry index: ${overlappingEntry["index"]}
Overlapping entry range: [$overlapStart-$overlapEnd]"""
    }

    /**
     * 执行单个字符串替换操作 - 核心替换逻辑
     * 🔧 重要说明：
     * - 此函数不使用 originalLineEnding 字段！
     * - 所有处理都使用标准化的 \n 格式
     * - originalLineEnding 只在外层的文件写入操作中使用
     */
    fun singleStrReplace(
        path: String,
        content: String,
        oldStr: String,
        newStr: String,
        lineNumberErrorTolerance: Double = 0.2,
        project: Project? = null,
        sessionUid: String? = null,
        questionUid: String? = null
    ): Map<String, Any> {
        // 🔧 文本预处理：标准化行结束符并移除行尾空白
        // 注意：这里只使用 content 字段，originalLineEnding 字段被忽略！
        val oldStrInfo = prepareTextForEditing(oldStr)
        val processedOldStr = oldStrInfo["content"]!!

        val newStrInfo = prepareTextForEditing(newStr)
        val processedNewStr = newStrInfo["content"]!!

        // Helper functions to create result objects
        fun makeSuccessEditResult(
            newContent: String,
            newStr: String,
            newStrStartLineNumber: Int,
            newStrEndLineNumber: Int,
            numLinesDiff: Int,
            genMessageFunc: (Map<String, Any>) -> String
        ): Map<String, Any> {
            return mapOf<String, Any>(
                "isError" to false,
                "oldStr" to processedOldStr,
                "newContent" to newContent,
                "newStr" to newStr,
                "newStrStartLineNumber" to newStrStartLineNumber,
                "newStrEndLineNumber" to newStrEndLineNumber,
                "numLinesDiff" to numLinesDiff,
                "genMessageFunc" to genMessageFunc
            )
        }

        fun makeErrorEditResult(genMessageFunc: (Map<String, Any>) -> String): Map<String, Any> {
            return mapOf<String, Any>(
                "isError" to true,
                "oldStr" to processedOldStr,
                "numLinesDiff" to 0,
                "genMessageFunc" to genMessageFunc
            )
        }

        var newContent: String? = null
        var newStrStartLine = 0
        var newStrEndLine = 0
        var currentOldStr = processedOldStr
        var currentNewStr = processedNewStr
        var useLineBasedReplacement = false

        // 🔧 特殊情况处理：空字符串替换
        // 空的 old_str 只在文件为空时允许（相当于插入操作）
        if (currentOldStr.trim().isEmpty()) {
            if (content.trim().isEmpty()) {
                // 空文件，直接插入 new_str
                newContent = currentNewStr
                newStrStartLine = 0
                newStrEndLine = currentNewStr.split("\n").size - 1
            } else {
                // 非空文件不允许空的 old_str
                return makeErrorEditResult { _ ->
                    """No replacement was performed, old_str is empty which is only allowed when the file is empty or contains only whitespace. The file $path is not empty.

IMPORTANT: Before retrying, please check the current content of the file to ensure you have the latest version."""
                }
            }
        } else {
            // 🔧 核心匹配逻辑：两层匹配策略，先精准后容错
            // 第一层：精确字符串匹配（优先使用，性能更好）
            // 第二层：按行模糊匹配（容错匹配，处理格式差异）

            var matches: List<Map<String, Int>> = emptyList()
            var lineReplacementResult: String? = null

            // 🔧 第一层：尝试精确字符串匹配
            matches = findMatches(content, currentOldStr)
            val logData = mapOf(
                "codeContent" to content,
                "oldStr" to currentOldStr,
                "matcheSize" to matches.size
            )
            logger.debug("Exact match result: ${JSON.toJSONString(logData)}")

            if (matches.isEmpty()) {
                // 🔧 精确匹配失败，上传日志
                runAsync {
                    uploadLogOnMatchFailure(sessionUid, questionUid, content, currentOldStr, path, "EXACT_MATCH_FAILED")
                }

                // 🔧 第二层：尝试按行模糊匹配
                lineReplacementResult = lineSearchAndReplace(content, currentOldStr, currentNewStr)
                if (lineReplacementResult != null) {
                    // 按行匹配成功，记录使用该方式
                    useLineBasedReplacement = true
                    logger.info("Line-by-line fuzzy matching successful")
                } else {
                    // 🔧 所有匹配方式都失败，上传日志并返回错误
                    runAsync {
                        uploadLogOnMatchFailure(sessionUid, questionUid, content, currentOldStr, path, "FUZZY_MATCH_FAILED")
                    }

                    return makeErrorEditResult { _ ->
                        """No replacement was performed, oldStr did not appear in $path.
Attempted matching strategies:
1. Exact string matching
2. Line-by-line fuzzy matching (supports spacing/indentation differences)

Please ensure oldStr is consistent with the file content including whitespace and indentation.

IMPORTANT: Before retrying, please check the current content of the file to ensure you have the latest version.
                        """.trimIndent()
                    }
                }
            } else {
                // 精确匹配成功
                logger.info("Exact matching successful")
            }

            // 🔧 处理匹配成功的结果
            if (useLineBasedReplacement) {
                // 🔧 使用按行模糊匹配的结果
                newContent = lineReplacementResult!!

                // 计算新内容的行号范围（用于生成提示信息）
                val matchResult = findMatchesByLine(content, currentOldStr)!!
                newStrStartLine = matchResult["startLine"]!!
                newStrEndLine = newStrStartLine + currentNewStr.split("\n").size - 1

                logger.info("Line-by-line fuzzy matching replacement completed, startLine: $newStrStartLine, endLine: $newStrEndLine")
            } else {
                // 🔧 使用精确匹配的结果
                if (matches.size == 1) {
                    // 单个匹配，直接替换
                    val match = matches[0]
                    newContent = content.replace(currentOldStr, currentNewStr)
                    newStrStartLine = match["startLine"]!!
                    newStrEndLine = match["startLine"]!! + currentNewStr.split("\n").size - 1

                    logger.info("Exact matching replacement completed, startLine: $newStrStartLine, endLine: $newStrEndLine")
                } else {
                    // 🔧 多个精确匹配，上传歧义匹配日志并返回错误
                    runAsync {
                        uploadLogOnMatchFailure(sessionUid, questionUid, content, currentOldStr, path, "AMBIGUOUS_MATCH")
                    }

                    return makeErrorEditResult { _ ->
                        """Ambiguous match: multiple occurrences of "$currentOldStr" detected.
                            
Please include AT LEAST 3-5 lines extra lines of surrounding context (above and below) so the string can be uniquely identified.

IMPORTANT: Before retrying, please check the current content of the file to ensure you have the latest version.
                        """
                    }
                }
            }
        }

        // 🔧 计算行数差异（用于后续行号调整）
        val oldStrLines = currentOldStr.split("\n").size
        val newStrLines = currentNewStr.split("\n").size
        val numLinesDiff = newStrLines - oldStrLines

        // 🔧 生成成功结果
        // 注意：返回的 newContent 使用标准化的 \n 格式，不恢复原始行结束符
        // 原始行结束符的恢复在外层的 prepare_tool_response 中进行
        val genSuccessMessage: (Map<String, Any>) -> String = { result ->
            val snippetStr = createSnippetStr(
                result["newContent"] as String,
                result["newStrStartLineNumber"] as Int,
                (result["newStrEndLineNumber"] as Int) - (result["newStrStartLineNumber"] as Int) + 1,
                SNIPPET_CONTEXT_LINES
            )
            val matchMethod = if (useLineBasedReplacement) "line-by-line fuzzy matching" else "exact string matching"
            """Replacement successful using $matchMethod.
Edited section after IDE auto-formatting was applied:
$snippetStr"""
        }

        return makeSuccessEditResult(
            newContent!!,
            currentNewStr,
            newStrStartLine,
            newStrEndLine,
            numLinesDiff,
            genSuccessMessage
        )
    }

    /**
     * 匹配失败时上传日志
     * @param matchType 匹配类型：EXACT_MATCH_FAILED（精确匹配失败）或 FUZZY_MATCH_FAILED（模糊匹配失败）
     */
    private fun uploadLogOnMatchFailure(
        sessionUid: String?,
        questionUid: String?,
        content: String,
        oldStr: String,
        path: String,
        matchType: String
    ) {
        try {

            val logData = mapOf(
                "sessionUid" to sessionUid,
                "questionUid" to questionUid,
                "codeContent" to content,
                "oldStr" to oldStr,
                "filePath" to path,
                "errorType" to matchType, // 使用具体的匹配类型
                "timestamp" to System.currentTimeMillis(),
                "matchStrategy" to when(matchType) {
                    "EXACT_MATCH_FAILED" -> "Exact string matching failed"
                    "FUZZY_MATCH_FAILED" -> "Both exact matching and line-by-line fuzzy matching failed"
                    "AMBIGUOUS_MATCH" -> "Exact matching found multiple results, ambiguous"
                    else -> "Unknown match type failed"
                }
            )

            logger.info("String matching failed (${matchType}), preparing to upload log: ${JSON.toJSONString(logData)}")

            val uploadLogRequestBean = UploadLogRequestBean(
                files = null, // No actual files to upload, only JSON data
                errorMsg = JSON.toJSONString(logData),
                sourceType = "PLUGIN_TOOL"
            )

            val tsingYanProdService = service<TsingYanProdService>()
            val result = tsingYanProdService.uploadLog(uploadLogRequestBean)

            if (result != null) {
                logger.info("Log upload successful, result: ${JSON.toJSONString(result)}")
            } else {
                logger.info("Log upload failed")
            }
        } catch (e: Exception) {
            logger.error("Exception occurred while uploading match failure log", e)
        }
    }
}


data class StrReplaceInfo(
    @ParamDesc(desc = "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.")
    val path: String,

    @ParamDesc(desc = "The text to replace (must be unique within the file, and must match the file contents exactly, including all whitespace and indentation)")
    val old_str: String,

    @ParamDesc(desc = "The string to replace `old_str` with. Can be an empty string to delete content.")
    val new_str: String,
)