package com.alipay.tsingyan.services.composer.flow

import com.alipay.tsingyan.inline2.bean.*
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow

object FlowUtil {

    private var mockDelayTime = 5L

    /**
     * 获取流式通信接口
     */
    fun getFastApplyCode(bean: FastApplyBean, project: Project): Flow<StreamResBean> {
        if (bean.isNewFile) {
            return getMockFlow(bean)
        }

        val localUserStore = service<LocalUserStore>()
        if (!CommonUtils.isCloudIDE) {
            bean.userToken = localUserStore.getUserInfoModel()?.userToken
        }

        bean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        bean.productType = CommonUtils.getProductType()
        bean.ideVersion = CommonUtils.getIdeVersion()
        val vfsFile = VfsUtils.getLocalVfsFile(bean.fileUrl!!, project)
        if (vfsFile != null) {
            bean.fileContent = VfsUtils.getContentByDocument(vfsFile)
        }
        bean.repo = ProjectCache.getGitData(project)
        val tsingYanProdService = service<TsingYanProdService>()
        return tsingYanProdService.queryFastApply(bean)
    }

    /**
     * 查询快速流式的结果
     * @see <a href>  https://yuque.antfin.com/iikq97/ukuoxm/tf5l2bkali10qkmu#t8P1N </a>
     * 传入的Bean必须具备如下字段：
     * fileUrl
     * question
     * draftCode
     */
    fun getSimpleFastApplyCode(
        project: Project,
        bean: FastApplyBean,
    ): Flow<StreamResBean> {
        if (null != bean.editPlus) {
            return handleAgentEditPlus(bean, bean.editPlus!!)
        } else {
            if (bean.isNewFile) {
                return getMockFlow(bean)
            }
            bean.userToken = service<LocalUserStore>().getUserInfoModel()?.userToken
            bean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
            bean.productType = CommonUtils.getProductType()
            bean.ideVersion = CommonUtils.getIdeVersion()

            val vfsFile = VfsUtils.getLocalVfsFile(bean.fileUrl!!, project)
            if (vfsFile != null) {
                bean.fileContent = VfsUtils.getContentByDocument(vfsFile)
            }

            bean.repo = ProjectCache.getGitData(project)
            val tsingYanProdService = service<TsingYanProdService>()
            return tsingYanProdService.querySimpleFastApply(bean)
        }
    }

    /**
     * 处理 CodeEdit
     */
    private fun handleAgentEditPlus(bean: FastApplyBean, editPlus: EditPlus): Flow<StreamResBean> {
        return when (editPlus) {
            is FullWriteEdit,
            is StrReplaceEdit,
                -> {
                // 根据内容进行替换
                getMockFlow(bean)
            }

            else -> {
                LogUtil.info("handleAgentEditPlus error,return default code")
                getMockFlow(bean)
            }
        }
    }


    /**
     * 某些场景，bean 的 draftCode 就是实际的代码，这样我们就不用请求 FastApply，直接给一个非常非常快的流式、或不Mock流
     */
    private fun getMockFlow(bean: FastApplyBean, needMock: Boolean = false): Flow<StreamResBean> {
        if (needMock) {
            val codeLines = bean.draftCode?.lines() ?: mutableListOf()
            return channelFlow<StreamResBean> {
                async {
                    send(StreamResBean().apply { errorMsg = AppConstant.STREAM_START })
                    codeLines.forEach { code ->
                        send(StreamResBean().apply {
                            content = code
                            traceId = bean.traceId
                        })
                        delay(mockDelayTime)
                    }
                    send(StreamResBean().apply { errorMsg = AppConstant.STREAM_END })
                }
            }
        } else {
            return channelFlow<StreamResBean> {
                async {
                    send(StreamResBean().apply { errorMsg = AppConstant.STREAM_START })
                    send(StreamResBean().apply {
                        content = bean.draftCode ?: ""
                        traceId = bean.traceId
                    })
                    send(StreamResBean().apply { errorMsg = AppConstant.STREAM_END })
                }
            }
        }
    }
}