package com.alipay.tsingyan.services.composer

import com.alipay.tsingyan.inline2.BaseIntent
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project

class CodeApplyIntent(val fileEditor: TextEditor) : BaseIntent(fileEditor.editor) {
    override fun isChatWindowShowing(): Boolean = false
    override fun isOptionWindowShowing(): Boolean = false

    override fun toString(): String {
        // 生成包含所有属性的字符串
        return "InlineChatIntent(startOffset=$startOffset, endOffset=$endOffset, startLine=$startLine, endLine=$endLine, lineStartOffset=$lineStartOffset, lineEndOffset=$lineEndOffset, newCodeStartOffSet=$newCodeStartOffSet, newCodeEndOffSet=$newCodeEndOffSet, clickType=$clickType, questionStr='$questionStr', isMarkup=$isMarkup, isCancel=$isCancel, isProcessing=$isProcessing)"
    }

    fun getEditor(): Editor {
        return fileEditor.editor
    }

    /**
     * 获取当前编辑器内的内容
     */
    fun getDocument(): Document {
        return fileEditor.editor.document
    }

    fun getProject(): Project {
        return fileEditor.editor.project!!
    }
}