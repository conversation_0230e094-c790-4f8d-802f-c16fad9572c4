package com.alipay.tsingyan.services.composer

import com.intellij.openapi.util.Key

/**
 *  Composer 的操作Key
 */
object ComposerKey {
    //展示判断
    val SHOW: Key<Boolean> = Key.create("Composer.Show")
    val APPLY: Key<(() -> Unit)> = Key.create("Composer.Apply")
    val CANCEL: Key<(() -> Unit)> = Key.create("Composer.Cancel")
    val DIFF: Key<(() -> Unit)> = Key.create("Composer.Diff")
    val DISABLE: Key<Boolean> = Key.create("Composer.ENABLE")
}