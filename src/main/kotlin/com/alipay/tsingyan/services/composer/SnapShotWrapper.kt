package com.alipay.tsingyan.services.composer

import cn.hutool.core.util.IdUtil
import com.alipay.tsingyan.model.enums.CommonResultEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.jgit.model.SnapShotClean
import com.alipay.tsingyan.services.jgit.model.SnapShotRecord
import com.alipay.tsingyan.services.jgit.model.SnapShotReset
import com.alipay.tsingyan.services.jgit.model.SnapShotType
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.webview.service.JSApiService
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFileManager

/**
 * 快照包装器
 */
@Service(Service.Level.PROJECT)
class SnapShotWrapper(val project: Project) : Disposable {
    private val logger: Logger = Logger.getInstance(this::class.java)
    private val baseDir = VfsUtils.getProjectBaseDir(project)

    // 存放所有的快照历史信息
    private var serviceMap = HashMap<String, Snapshot>(8)

    /**
     * 将指定文件添加到索引进行管理
     */
    fun composerDoAdd(sessionId: String, filePath: String, isNew: Boolean = false) {
        getTempFiles(sessionId).add(TempFile(filePath, isNew))
        getSnapShotService(sessionId).addFile(listOf(filePath))
    }

    /**
     * 新建快照，本质上是新建 Commit
     * 如果是 Composer 类型的，则直接 Commit，
     * 如果是 Apply 类型的，则根据根据对应文件进行提交
     */
    fun doSnapShot(bean: SnapShotRecord) {
        val snapShotService = getSnapShotService(bean.sessionId)
        runInEdt {
            FileDocumentManager.getInstance().saveAllDocuments()
            var commitID: String? = null
            if (bean.type == SnapShotType.COMPOSER) {
                // 转换文件列表到状态
                val fileStatusList =
                    getTempFiles(bean.sessionId).filter { !it.isNew }.map { FileStatus(filePath = it.filePath) }
                logger.info("Composer 时获取到的文件列表为：${fileStatusList}")
                commitID = snapShotService.doSnap(Messgae(fileStatusList, bean.toString()), false)
                getTempFiles(bean.sessionId).clear()
            } else if (bean.type == SnapShotType.APPLY) {
                val fileStatusList = bean.fileList.map { FileStatus(filePath = it, type = FileType.AI) }
                commitID = snapShotService.doSnap(Messgae(fileStatusList, bean.toString()))
            }

            if (null != commitID) {
                project.service<JSApiService>().sendSnapResult(bean.traceId, bean.sessionId, commitID, bean.type)
            } else {
                project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.ERROR)
            }
        }
    }

    /**
     * 即时快照创建，也不要影响对话业务的主流程
     */
    fun doSnapShotInAgentMode(sessionId: String, questionId: String): String {
        return try {
            snapShotInAgentMode(sessionId, questionId)
        } catch (e: Exception) {
            LogUtil.info("agent SnapShot error sessionId:[$sessionId] questionId:[$questionId] ,e:[${e.message}]", e)
            IdUtil.fastUUID()
        }
    }


    /**
     * Agent 快照模式处理逻辑如下：
     *
     * 在问下一个问题的时候，对上次问题的结果创建两个 Commit 节点
     *  Commit1 : AI变更文件之前的内容
     *  Commit2 ：问问题时当前的文件内容
     * ------
     * before  apply
     *  --  add oldCode to index
     *
     * next question
     *  --  do commit index -> c1
     *  --  add currCode to index && do commit -> c2
     * -----
     *
     *
     */
    private fun snapShotInAgentMode(sessionId: String, questionId: String) : String{
        val snapShotService = getSnapShotService(sessionId)
        var commitID = ""

        // 手动构建扩展信息对象
        val snapShotRecord = SnapShotRecord(
            IdUtil.fastUUID(),
            sessionId,
            SnapShotType.COMPOSER,
            questionId
        )

        ApplicationManager.getApplication().invokeAndWait {
            FileDocumentManager.getInstance().saveAllDocuments()
            val tempFileHashSet = getTempFiles(sessionId)
            // 先做 变更前文件提交，创建 CommitID 1，这个是要返回给前端的 CommitID
            val composerAllFile = tempFileHashSet.filter { !it.isNew }
            val composerFileList = composerAllFile.map { FileStatus(filePath = it.filePath) }
            LogUtil.info("AIAgent Composer 保存问问题前文件列表为：${composerFileList}")
            snapShotRecord.fileList = composerAllFile.map { it.filePath }
            val composerCommitID = snapShotService.doSnap(Messgae(composerFileList, snapShotRecord.toString()), false)
            // 开始记录当前真正变更的文件列表
            snapShotRecord.fileList = tempFileHashSet.map { it.filePath }
            snapShotRecord.type = SnapShotType.APPLY
            val applyFileList = tempFileHashSet.map { FileStatus(filePath = it.filePath, type = if (it.isNew) FileType.AI else FileType.OLD) }
            // do add to index
            getSnapShotService(sessionId).addFile(applyFileList.map { it.filePath })
            val applyCommitID = snapShotService.doSnap(Messgae(applyFileList, snapShotRecord.toString()))
            LogUtil.info("AIAgent Apply 保存问问题前文件列表为：${tempFileHashSet}")
            tempFileHashSet.clear()
            commitID = composerCommitID
        }
        return commitID
    }

    /**
     * 快照回退逻辑
     * reset 是否真的进行快照回退，默认只进行代码回滚。
     */
    fun resetSnapshotInAgentMode(sessionID: String, commitId: String, reset: Boolean = false): Boolean {
        val shotReset = SnapShotReset("111", sessionID, targetCommitId = commitId)
        try {
            if (reset) {
                resetSnapshot(shotReset)
            } else {
                historyReview(shotReset)
            }
        } catch (e: Exception) {
            return false
        }
        return true

//        ApplicationManager.getApplication().invokeAndWait {
//            VirtualFileManager.getInstance().syncRefresh()
//        }
    }

    /**
     * 将当前代码文件变更到指定的历史节点上，但是不进行 Git 回退操作
     */
    fun historyReview(bean: SnapShotReset) {
        val snapShotService = getSnapShotService(bean.sessionId)
        try {
            runInEdt {
                FileDocumentManager.getInstance().saveAllDocuments()
            }
            snapShotService.doHistoryReview(
                bean.targetCommitId,
                delCall = { path: String ->
                    VfsUtils.getLocalVfsFile(path, project)?.refresh(false, false)
                },
                modifyCall = { path: String ->
                    VfsUtils.getLocalVfsFile(path, project)?.let {
                        it.refresh(false, false)
                        project.service<ComposerService>()
                            .refreshEditorDiffView(VfsUtils.getRelativeFilePath(it, project))
                    }
                },
                beforeDelCall = { path: String ->
                    project.service<ComposerService>().markAutoCloseTag(path)
                },
            )
            project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.SUCCESS)
        } catch (e: Exception) {
            logger.info("快照历史回退异常", e)
            project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.ERROR)
        }
    }

    /**
     * 将历史回退到指定的提交上
     */
    fun resetSnapshot(bean: SnapShotReset) {
        val snapShotService = getSnapShotService(bean.sessionId)
        try {
            snapShotService.doResetSnap(bean.targetCommitId)
            VirtualFileManager.getInstance().asyncRefresh {
                project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.SUCCESS)
            }
        } catch (e: Exception) {
            logger.info("快照历史回退异常", e)
            project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.ERROR)
        }
    }

    /**
     * 清除所有快照
     */
    fun cleanSnap(bean: SnapShotClean) {
        if ("ALL".equals(bean.sessionId)) {
            serviceMap.values.forEach { it.service.clean() }
        } else {
            getSnapShotService(bean.sessionId).clean()
        }
        project.service<JSApiService>().sendCommonResult(bean.traceId, CommonResultEnum.SUCCESS)
    }

    /**
     * 关闭时，释放资源，
     * Git 仓库只做 GC 操作，不做别的操作
     */
    override fun dispose() {
        serviceMap.values.forEach {
            it.tempFiles.clear()
            it.service.gc()
        }
        serviceMap.clear()
    }

    /**
     * 获取指定的 Git 服务
     */
    private fun getSnapShotService(sessionID: String): SnapShotService {
        return getSnapshot(sessionID).service
    }

    /**
     * 获取暂存的文件列表
     */
    private fun getTempFiles(sessionID: String): HashSet<TempFile> {
        return getSnapshot(sessionID).tempFiles
    }

    /**
     * 获取对应会话的Wrapper
     * 主要为了保证初始化一次
     */
    private fun getSnapshot(sessionID: String): Snapshot {
        synchronized(serviceMap) {
            val wraper = serviceMap.get(sessionID)
            if (null == wraper) {
                val snapshot = Snapshot(SnapShotService(baseDir, sessionID, project.name))
                //使用 .idea/workspace.xml 路径下的创建初始化 Commit节点，避免没有Head。
                snapshot.service.init(Messgae(listOf(FileStatus(".idea/workspace.xml"))))
                Thread.sleep(1000)
                serviceMap.put(sessionID, snapshot)
            }
        }
        return serviceMap.get(sessionID)!!
    }

    data class Snapshot(
        val service: SnapShotService,
        val tempFiles: HashSet<TempFile> = hashSetOf<TempFile>(),
    )

    /**
     * 临时文件类型
     */
    data class TempFile(
        val filePath: String,
        val isNew: Boolean = false,
    )
}