package com.alipay.tsingyan.services.composer.ui

import com.intellij.ui.EditorNotificationPanel
import com.intellij.ui.JBColor
import com.intellij.util.ui.StartupUiUtil
import java.awt.BorderLayout
import java.awt.Color
import java.awt.Graphics
import javax.swing.*


class MyEditorTopToolbar() : JPanel() {
    private var rightButtonPanel = MyToolBarPanel()

    init {
        initializeUI()
    }

    /**
     * 添加按钮
     */
    fun addBtn(text: String, fill: Boolean = false, action: (() -> Unit)?) {
        rightButtonPanel.addBtn(text, fill, action)
    }

    /**
     * 变更按钮的状态
     */
    fun updateStatus(enable: Boolean) {
        rightButtonPanel.isEnabled = enable
        rightButtonPanel.components.forEach { component ->
            if (component is JButton) {
                component.isEnabled = enable
            }
        }
        this.updateUI()
    }

    private fun initializeUI() {
        layout = BorderLayout()
        background = getDefaultColor()
        border = BorderFactory.createCompoundBorder(
            BorderFactory.createMatteBorder(0, 0, 0, 0, Color.LIGHT_GRAY),
            BorderFactory.createEmptyBorder(5, 10, 5, 10)
        )
        // 添加右侧面板到工具栏
        add(rightButtonPanel, BorderLayout.EAST)
    }

    private fun getDefaultColor(): Color {
        return EditorNotificationPanel.getToolbarBackground()
    }


    // 顶部Panel
    class MyToolBarPanel : JPanel() {
        init {
            layout = BoxLayout(this, BoxLayout.X_AXIS)
            border = BorderFactory.createEmptyBorder(0, 0, 0, 10)
            isOpaque = false
        }

        fun addBtn(text: String, fill: Boolean = false, action: (() -> Unit)?) {
            val fillColor = JBColor.decode("#5c6cf7")

            val myCustomButton: JButton = object : JButton(text) {
                init {
                    isFocusPainted = false
                    isContentAreaFilled = false // 禁用默认背景填充
                    border = BorderFactory.createEmptyBorder(2, 4, 2, 4) // 内边距
                    if (fill) {
                        background = fillColor
                    }
                    addActionListener {
                        action?.invoke()
                    }
                }

                // 自定义绘制方法实现圆角
                override fun paintComponent(g: Graphics) {
                    val g2 = g.create()
                    g2.color = background
                    g2.fillRoundRect(0, 0, width, height, 15, 15)
                    super.paintComponent(g2)

                    // 绘制字体颜色
                    if (fill && !StartupUiUtil.isUnderDarcula()) {
                        g2.color = JBColor.WHITE
                        val fm = getFontMetrics(font)
                        val textX = (width - fm.stringWidth(getText())) / 2
                        val textY = (height - fm.height) / 2 + fm.ascent
                        g2.drawString(getText(), textX, textY)
                    }
                    g2.dispose()
                }


                // 绘制圆角边框
                override fun paintBorder(g: Graphics) {
                    val g2 = g.create()
                    if (StartupUiUtil.isUnderDarcula()) {
                        g2.color = JBColor.decode("#5f656b")
                    } else {
                        g2.color = JBColor.LIGHT_GRAY
                    }
                    g2.drawRoundRect(0, 0, width - 1, height - 1, 15, 15)
                    g2.dispose()
                }
            }

            add(Box.createHorizontalStrut(10)) // 按钮间距
            add(myCustomButton)
        }
    }
}

class CustomButton(
    btnText: String,
    val fillColor: Color = JBColor.decode("#5c6cf7"),
    val fill: Boolean = false,
    action: (() -> Unit)? = null,
) : JButton(btnText) {

    init {
        isFocusPainted = false
        isContentAreaFilled = false // 禁用默认背景填充
        border = BorderFactory.createEmptyBorder(3, 3, 3, 3) // 内边距
        if (fill) {
            background = fillColor
        }
        addActionListener {
            action?.invoke()
        }
    }

    // 自定义绘制方法实现圆角
    override fun paintComponent(g: Graphics) {
        val g2 = g.create()
        g2.color = background
        g2.fillRoundRect(0, 0, width, height, 15, 15)
        super.paintComponent(g2)

        // 绘制字体颜色
        if (fill && !StartupUiUtil.isUnderDarcula()) {
            g2.color = JBColor.WHITE
            val fm = getFontMetrics(font)
            val textX = (width - fm.stringWidth(getText())) / 2
            val textY = (height - fm.height) / 2 + fm.ascent
            g2.drawString(text, textX, textY)
        }
        g2.dispose()
    }

    // 绘制圆角边框
    override fun paintBorder(g: Graphics) {
        val g2 = g.create()
        if (StartupUiUtil.isUnderDarcula()) {
            g2.color = JBColor.decode("#5f656b")
        } else {
            g2.color = JBColor.LIGHT_GRAY
        }
        g2.drawRoundRect(0 + 1, 0 + 1, width - 1, height - 1, 15, 15)
        g2.dispose()
    }
}