package com.alipay.tsingyan.services.composer.flow.test1

import java.util.regex.Pattern
import java.util.logging.Logger
import java.util.logging.Level
import java.util.logging.FileHandler
import java.util.logging.ConsoleHandler
import java.util.logging.Formatter
import java.util.logging.LogRecord
import java.io.File

// Constants
const val MAX_RESPONSE_LEN = 50000
const val TRUNCATED_MESSAGE = "<response clipped><NOTE>To save on context only part of this file has been shown to you.</NOTE>"
const val SNIPPET_CONTEXT_LINES = 4

data class ToolResponse(
    val type: String,
    val message: String
)

data class TextInfo(
    val content: String,
    val originalLineEnding: String
)

data class ViewRange(
    val initLine: Int,
    val finalLine: Int,
    val message: String
)

data class Snippet(
    val snippet: String,
    val startLine: Int
)

data class IndentInfo(
    val type: String,
    val size: Int
)

class CommonUtils {
    companion object {
        /**
         * Initialize and configure a logger with colored console output and file rotation
         */
        fun setupLogger(name: String, logFile: String, level: Level = Level.INFO, handlers: List<String> = listOf("file", "console")): Logger {
            val logger = Logger.getLogger(name)
            logger.level = level
            
            // Set up file handler with log rotation
            if ("file" in handlers) {
                val fileHandler = FileHandler(logFile, 2000000, 5, true)
                fileHandler.formatter = object : Formatter() {
                    override fun format(record: LogRecord): String {
                        return "${java.time.LocalDateTime.now()} ${record.level.name.padEnd(8)} ${record.message}\n"
                    }
                }
                fileHandler.level = Level.FINE
                logger.addHandler(fileHandler)
            }
            
            // Set up console handler
            if ("console" in handlers) {
                val consoleHandler = ConsoleHandler()
                consoleHandler.formatter = object : Formatter() {
                    override fun format(record: LogRecord): String {
                        return "${java.time.LocalDateTime.now()} ${record.level.name.padEnd(8)} ${record.message}\n"
                    }
                }
                consoleHandler.level = Level.INFO
                logger.addHandler(consoleHandler)
            }
            
            return logger
        }
        
        /**
         * Create a success tool response
         */
        fun successToolResponse(message: String): ToolResponse {
            return ToolResponse("success", message)
        }
        
        /**
         * Create an error tool response
         */
        fun errorToolResponse(message: String): ToolResponse {
            return ToolResponse("error", message)
        }
        
        /**
         * Remove trailing whitespace from each line in the text
         */
        fun removeTrailingWhitespace(text: String): String {
            val lineEnding = if (text.contains("\r\n")) "\r\n" else "\n"
            val lines = text.split(lineEnding)
            val trimmedLines = lines.map { it.trimEnd() }
            return trimmedLines.joinToString(lineEnding)
        }
        
        /**
         * Format file content with line numbers for display
         */
        fun makeOutput(fileContent: String, fileDescriptor: String, initLine: Int = 1, totalLines: Int? = null): String {
            val truncatedContent = maybeTruncate(fileContent)
            val numberedContent = truncatedContent.split("\n").mapIndexed { i, line ->
                "${(i + initLine).toString().padStart(6)}\t$line"
            }.joinToString("\n")
            
            var output = "Here's the result of running `cat -n` on $fileDescriptor:\n$numberedContent\n"
            
            totalLines?.let {
                output += "Total lines in file: $it\n"
            }
            
            return output
        }
        
        /**
         * Truncate content if it's too long
         */
        fun maybeTruncate(content: String, truncateAfter: Int = MAX_RESPONSE_LEN): String {
            return if (content.length <= truncateAfter) {
                content
            } else {
                content.substring(0, truncateAfter) + TRUNCATED_MESSAGE
            }
        }
        
        /**
         * Normalize line endings to '\n'
         */
        fun normalizeLineEndings(text: String): String {
            return text.replace("\r\n", "\n")
        }
        
        /**
         * Detect line ending used in text
         */
        fun detectLineEnding(text: String): String {
            return if (text.contains("\r\n")) "\r\n" else "\n"
        }
        
        /**
         * Prepare text for editing by normalizing line endings and removing trailing whitespace
         */
        fun prepareTextForEditing(text: String): TextInfo {
            val originalLineEnding = detectLineEnding(text)
            val content = normalizeLineEndings(removeTrailingWhitespace(text))
            return TextInfo(content, originalLineEnding)
        }
        
        /**
         * Restore original line endings
         */
        fun restoreLineEndings(text: String, lineEnding: String): String {
            return if (lineEnding == "\n") {
                text
            } else {
                text.replace("\n", lineEnding)
            }
        }
        
        /**
         * Validate and adjust the view range to ensure it's within file bounds
         */
        fun validateViewRange(viewRange: List<Int>?, numLinesFile: Int, minViewSize: Int): ViewRange {
            var message = ""
            
            if (viewRange == null || viewRange.size != 2) {
                message = "Invalid view range provided. Showing entire file (lines 1-$numLinesFile)."
                return ViewRange(1, numLinesFile, message)
            }
            
            var initLine = viewRange[0]
            var finalLine = viewRange[1]
            
            if (initLine < 1) {
                message += "Start line $initLine is less than 1. Adjusted to 1.\n"
                initLine = 1
            } else if (initLine > numLinesFile) {
                message += "Start line $initLine exceeds file length ($numLinesFile). Adjusted to 1.\n"
                initLine = 1
            }
            
            if (finalLine == -1) {
                finalLine = numLinesFile
            } else if (finalLine > numLinesFile) {
                message += "End line $finalLine exceeds file length ($numLinesFile). Adjusted to $numLinesFile. "
                finalLine = numLinesFile
            } else if (finalLine < initLine) {
                message += "End line $finalLine is less than start line $initLine. Adjusted to $numLinesFile. "
                finalLine = numLinesFile
            } else if (finalLine - initLine + 1 < minViewSize) {
                val finalLineExpanded = minOf(initLine + minViewSize - 1, numLinesFile)
                message += "View range expanded to meet minimum size of $minViewSize lines. "
                
                if (finalLineExpanded >= numLinesFile) {
                    message += "End line adjusted to last line of file ($numLinesFile)."
                } else {
                    message += "New range: [$initLine, $finalLineExpanded]."
                }
                
                finalLine = finalLineExpanded
            }
            
            message = message.trim()
            return ViewRange(initLine, finalLine, message)
        }
        
        /**
         * Create a text snippet around the specified lines
         */
        fun createSnippet(content: String, replacementStartLine: Int, replacementNumLines: Int, snippetContextLines: Int): Snippet {
            val startLine = maxOf(0, replacementStartLine - snippetContextLines)
            val endLine = replacementStartLine + replacementNumLines - 1 + snippetContextLines
            
            val normalizedContent = content.replace("\r\n", "\n")
            val lines = normalizedContent.split("\n")
            val snippet = lines.subList(startLine, minOf(endLine + 1, lines.size)).joinToString("\n")
            
            return Snippet(snippet, startLine)
        }
        
        /**
         * Create a formatted snippet with line numbers
         */
        fun createSnippetStr(content: String, replacementStartLine: Int, replacementNumLines: Int, snippetContextLines: Int): String {
            val result = createSnippet(content, replacementStartLine, replacementNumLines, snippetContextLines)
            val snippet = result.snippet
            val startLine = result.startLine
            
            return snippet.split("\n").mapIndexed { i, line ->
                "${(i + startLine + 1).toString().padStart(6)}\t$line"
            }.joinToString("\n")
        }
        
        /**
         * Detect the indentation style used in the content
         */
        fun detectIndentation(content: String): IndentInfo {
            val lines = content.split("\n")
            var spaceIndents = 0
            var tabIndents = 0
            var spaceSize = 0
            
            for (line in lines) {
                if (line.trim().isEmpty()) {
                    continue
                }
                
                val leadingSpaces = Pattern.compile("^( +)").matcher(line)
                val leadingTabs = Pattern.compile("^(\t+)").matcher(line)
                
                if (leadingSpaces.find()) {
                    spaceIndents++
                    if (spaceSize == 0) {
                        spaceSize = leadingSpaces.group(1)!!.length
                    }
                } else if (leadingTabs.find()) {
                    tabIndents++
                }
            }
            
            return if (tabIndents > spaceIndents) {
                IndentInfo("tab", 1)
            } else {
                IndentInfo("space", if (spaceSize > 0) spaceSize else 2)
            }
        }
    }
}