import com.alipay.tsingyan.services.composer.flow.test1.CommonUtils
import com.alipay.tsingyan.services.composer.flow.test1.SNIPPET_CONTEXT_LINES
import java.io.File
import java.io.IOException
import java.util.logging.Logger
import java.util.regex.Pattern

data class StrReplaceEntry(
    val oldStr: String,
    val newStr: String,
    val oldStrStartLineNumber: Int,
    val oldStrEndLineNumber: Int
)

data class EditResult(
    val isError: Boolean,
    val index: Int,
    val oldStr: String,
    val oldStrStartLineNumber: Int,
    val oldStrEndLineNumber: Int,
    val newContent: String? = null,
    val newStr: String? = null,
    val newStrStartLineNumber: Int? = null,
    val newStrEndLineNumber: Int? = null,
    val numLinesDiff: Int? = null,
    val message: String? = null
)

class McpError(message: String) : Exception(message)

const val TOOL_DESCRIPTION = """Custom editing tool for editing existing files
* `path` is a file path relative to the workspace root
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>`
* `insert` and `str_replace` commands output a snippet of the edited section for each entry. This snippet reflects the final state of the file after all edits and IDE auto-formatting have been applied.

Notes for using the `str_replace` command:
* Use the `str_replace_entries` parameter with an array of objects
* Each object should have `old_str`, `new_str`, `old_str_start_line_number` and `old_str_end_line_number` properties
* The `old_str_start_line_number` and `old_str_end_line_number` parameters are 1-based line numbers
* Both `old_str_start_line_number` and `old_str_end_line_number` are INCLUSIVE
* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!
* Empty `old_str` is allowed only when the file is empty or contains only whitespaces
* It is important to specify `old_str_start_line_number` and `old_str_end_line_number` to disambiguate between multiple occurrences of `old_str` in the file
* Make sure that `old_str_start_line_number` and `old_str_end_line_number` do not overlap with other entries in `str_replace_entries`
* The `new_str` parameter should contain the edited lines that should replace the `old_str`. Can be an empty string to delete content

IMPORTANT:
* This is the only tool you should use for editing files.
* If it fails try your best to fix inputs and retry.
* DO NOT fall back to removing the whole file and recreating it from scratch.
* Try to fit as many edits in one tool call as possible
* Call view_file tool to read the file before editing it."""

class StrReplaceEditorTool(
    private val workspaceRootPath: String,
    private val lineNumberErrorTolerance: Double = 0.2,
    private val waitForAutoFormatMs: Int = 1000
) {
    private val logger: Logger = Logger.getLogger("str_replace_editor_nested")
    
    init {
        logger.info("Initialized with params: lineNumberErrorTolerance=$lineNumberErrorTolerance, waitForAutoFormatMs=$waitForAutoFormatMs")
    }
    
    /**
     * Read the contents of a file
     */
    fun readFile(path: String): String {
        return try {
            val fullPath = File(workspaceRootPath, path)
            fullPath.readText(Charsets.UTF_8)
        } catch (e: Exception) {
            throw McpError("Error reading file $path: ${e.message}")
        }
    }
    
    /**
     * Handle the view command
     */
    fun handleView(path: String, fileContents: String, viewRange: List<Int>? = null): String {
        logger.info("Handling view command for $path with range: $viewRange")
        val fullPath = File(workspaceRootPath, path).absolutePath
        logger.info("convert to absolute path: $fullPath")
        
        val normalizedContent = CommonUtils.normalizeLineEndings(fileContents)
        val fileLines = normalizedContent.split("\n")
        val numLinesFile = fileLines.size
        
        logger.info("File $path has $numLinesFile lines")
        
        var initLine = 1
        var finalLine = numLinesFile
        var rangeMessage = ""
        
        viewRange?.let {
            try {
                val minViewSize = 500
                val validRange = CommonUtils.validateViewRange(it, numLinesFile, minViewSize)
                initLine = validRange.initLine
                finalLine = validRange.finalLine
                
                if (validRange.message.isNotEmpty()) {
                    rangeMessage = "Note:\n${validRange.message}\n\n"
                }
            } catch (e: Exception) {
                throw IllegalArgumentException("Invalid view_range: ${e.message}")
            }
        }
        
        val slicedContents = if (finalLine != -1) {
            fileLines.subList(initLine - 1, finalLine)
        } else {
            fileLines.subList(initLine - 1, fileLines.size)
        }
        val slicedContentsStr = slicedContents.joinToString("\n")
        
        val output = CommonUtils.makeOutput(slicedContentsStr, fullPath, initLine, numLinesFile)
        
        return rangeMessage + output
    }
    
    /**
     * Perform a single string replacement
     */
    fun singleStrReplace(
        path: String,
        content: String,
        oldStr: String,
        newStr: String,
        index: Int,
        oldStrStartLineNumber: Int?,
        oldStrEndLineNumber: Int?
    ): EditResult {
        // Prepare strings by normalizing line endings and removing trailing whitespace
        val oldStrInfo = CommonUtils.prepareTextForEditing(oldStr)
        val processedOldStr = oldStrInfo.content
        
        val newStrInfo = CommonUtils.prepareTextForEditing(newStr)
        val processedNewStr = newStrInfo.content
        
        fun makeSuccessEditResult(
            newContent: String,
            newStr: String,
            newStrStartLineNumber: Int,
            newStrEndLineNumber: Int,
            numLinesDiff: Int
        ): EditResult {
            return EditResult(
                isError = false,
                index = index,
                oldStr = processedOldStr,
                oldStrStartLineNumber = oldStrStartLineNumber ?: 0,
                oldStrEndLineNumber = oldStrEndLineNumber ?: 0,
                newContent = newContent,
                newStr = newStr,
                newStrStartLineNumber = newStrStartLineNumber,
                newStrEndLineNumber = newStrEndLineNumber,
                numLinesDiff = numLinesDiff
            )
        }
        
        fun makeErrorEditResult(message: String): EditResult {
            return EditResult(
                isError = true,
                index = index,
                oldStr = processedOldStr,
                oldStrStartLineNumber = oldStrStartLineNumber ?: 0,
                oldStrEndLineNumber = oldStrEndLineNumber ?: 0,
                message = message
            )
        }
        
        // Normalize content
        val normalizedContent = CommonUtils.normalizeLineEndings(content)
        val lines = normalizedContent.split("\n").toMutableList()
        val originalNumLines = lines.size
        
        // Handle empty file case
        if (normalizedContent.trim().isEmpty() && processedOldStr.trim().isEmpty()) {
            val newContent = processedNewStr
            val newLines = newContent.split("\n")
            val numLinesDiff = newLines.size - originalNumLines
            return makeSuccessEditResult(newContent, processedNewStr, 1, newLines.size, numLinesDiff)
        }
        
        // Find matches for the old string
        val matches = findMatches(normalizedContent, processedOldStr)
        
        if (matches.isEmpty()) {
            return makeErrorEditResult("No exact match found for the old string in the file.")
        }
        
        // Use line numbers to disambiguate if provided
        val selectedMatch = if (oldStrStartLineNumber != null && oldStrEndLineNumber != null) {
            val expectedRange = oldStrStartLineNumber..oldStrEndLineNumber
            val bestMatch = findClosestMatch(matches, expectedRange, lineNumberErrorTolerance)
            bestMatch ?: return makeErrorEditResult("No match found at the specified line numbers.")
        } else {
            if (matches.size > 1) {
                return makeErrorEditResult("Multiple matches found. Please specify line numbers to disambiguate.")
            }
            matches[0]
        }
        
        // Perform the replacement
        val startLineIndex = selectedMatch.startLine - 1
        val endLineIndex = selectedMatch.endLine - 1
        
        // Remove old lines
        for (i in endLineIndex downTo startLineIndex) {
            lines.removeAt(i)
        }
        
        // Insert new lines
        val newLines = if (processedNewStr.isEmpty()) {
            emptyList()
        } else {
            processedNewStr.split("\n")
        }
        
        lines.addAll(startLineIndex, newLines)
        
        val newContent = lines.joinToString("\n")
        val newStrStartLineNumber = startLineIndex + 1
        val newStrEndLineNumber = startLineIndex + newLines.size
        val numLinesDiff = newLines.size - (selectedMatch.endLine - selectedMatch.startLine + 1)
        
        return makeSuccessEditResult(newContent, processedNewStr, newStrStartLineNumber, newStrEndLineNumber, numLinesDiff)
    }
    
    /**
     * Handle string replacement for multiple entries
     */
    fun handleStrReplace(path: String, fileContents: String, strReplaceEntries: List<StrReplaceEntry>): String {
        logger.info("Handling str_replace command for $path with ${strReplaceEntries.size} entries")
        
        // Check for overlapping entries
        val overlappingEntry = findOverlappingEntry(strReplaceEntries)
        if (overlappingEntry != null) {
            val errorMessage = genOverlappingEntryErrorMessage(overlappingEntry)
            logger.warning(errorMessage)
            return CommonUtils.errorToolResponse(errorMessage).message
        }
        
        var currentContent = fileContents
        val results = mutableListOf<EditResult>()
        var totalLinesDiff = 0
        
        // Sort entries by start line number in reverse order to avoid line number shifts
        val sortedEntries = strReplaceEntries.sortedByDescending { it.oldStrStartLineNumber }
        
        for ((index, entry) in sortedEntries.withIndex()) {
            val result = singleStrReplace(
                path,
                currentContent,
                entry.oldStr,
                entry.newStr,
                index,
                entry.oldStrStartLineNumber,
                entry.oldStrEndLineNumber
            )
            
            if (result.isError) {
                logger.warning("Error in replacement $index: ${result.message}")
                return CommonUtils.errorToolResponse(result.message ?: "Unknown error").message
            }
            
            currentContent = result.newContent!!
            totalLinesDiff += result.numLinesDiff!!
            results.add(result)
        }
        
        // Write the modified content back to the file
        try {
            val fullPath = File(workspaceRootPath, path)
            fullPath.writeText(currentContent, Charsets.UTF_8)
        } catch (e: Exception) {
            throw McpError("Error writing to file $path: ${e.message}")
        }
        
        // Generate response message
        val snippets = results.map { result ->
            val snippetStr = CommonUtils.createSnippetStr(
                currentContent,
                result.newStrStartLineNumber!! - 1,
                result.newStrEndLineNumber!! - result.newStrStartLineNumber!! + 1,
                SNIPPET_CONTEXT_LINES
            )
            "Entry ${result.index + 1}:\n$snippetStr"
        }.joinToString("\n\n")
        
        return "Successfully applied ${results.size} string replacements to $path.\n\n$snippets"
    }
    
    private fun findMatches(content: String, searchStr: String): List<Match> {
        val matches = mutableListOf<Match>()
        val lines = content.split("\n")
        val searchLines = searchStr.split("\n")
        
        for (i in 0..lines.size - searchLines.size) {
            var matched = true
            for (j in searchLines.indices) {
                if (lines[i + j] != searchLines[j]) {
                    matched = false
                    break
                }
            }
            if (matched) {
                matches.add(Match(i + 1, i + searchLines.size))
            }
        }
        
        return matches
    }
    
    private fun findClosestMatch(matches: List<Match>, expectedRange: IntRange, tolerance: Double): Match? {
        return matches.minByOrNull { match ->
            val distance = minOf(
                kotlin.math.abs(match.startLine - expectedRange.first),
                kotlin.math.abs(match.endLine - expectedRange.last)
            )
            distance
        }?.takeIf { match ->
            val distance = minOf(
                kotlin.math.abs(match.startLine - expectedRange.first),
                kotlin.math.abs(match.endLine - expectedRange.last)
            )
            distance <= tolerance * expectedRange.count()
        }
    }
    
    private fun findOverlappingEntry(entries: List<StrReplaceEntry>): Pair<StrReplaceEntry, StrReplaceEntry>? {
        for (i in entries.indices) {
            for (j in i + 1 until entries.size) {
                val entry1 = entries[i]
                val entry2 = entries[j]
                
                val range1 = entry1.oldStrStartLineNumber..entry1.oldStrEndLineNumber
                val range2 = entry2.oldStrStartLineNumber..entry2.oldStrEndLineNumber
                
                if (range1.intersect(range2).isNotEmpty()) {
                    return Pair(entry1, entry2)
                }
            }
        }
        return null
    }
    
    private fun genOverlappingEntryErrorMessage(overlapping: Pair<StrReplaceEntry, StrReplaceEntry>): String {
        val (entry1, entry2) = overlapping
        return "Overlapping entries found: " +
                "Entry 1: lines ${entry1.oldStrStartLineNumber}-${entry1.oldStrEndLineNumber}, " +
                "Entry 2: lines ${entry2.oldStrStartLineNumber}-${entry2.oldStrEndLineNumber}"
    }
}

data class Match(
    val startLine: Int,
    val endLine: Int
)