package com.alipay.tsingyan.services.composer

import cn.hutool.core.util.StrUtil
import com.alipay.tsingyan.inline2.bean.FastApplyBean
import com.alipay.tsingyan.inline2.diff.InlineDiffButtonAction
import com.alipay.tsingyan.inline2.diff.InlineDiffWindow
import com.alipay.tsingyan.inline2.stream.StreamDiffHandler
import com.alipay.tsingyan.inline2.stream.StreamDiffRequest
import com.alipay.tsingyan.inline2.theme.ColorManager
import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.model.composer.FileActionInfoModel
import com.alipay.tsingyan.model.composer.FileTag
import com.alipay.tsingyan.model.composer.Tag
import com.alipay.tsingyan.model.enums.CommonResultEnum
import com.alipay.tsingyan.model.enums.ComposerActionEnum
import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.composer.flow.FlowUtil
import com.alipay.tsingyan.services.composer.ui.ComposerTextAttributes
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.talk.UIThemeListener
import com.alipay.tsingyan.utils.DiffUtil
import com.alipay.tsingyan.utils.messages.Topics
import com.alipay.tsingyan.utils.messages.listener.ApplyResult
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.webview.service.JSApiService
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.codeStyle.CodeStyleManager
import com.intellij.ui.EditorNotifications
import com.intellij.util.DocumentUtil
import org.jetbrains.concurrency.runAsync
import java.util.*


class ComposerCodeApplyService : Disposable {
    private val logger: Logger = Logger.getInstance(this::class.java)
    var theme: ThemeEnum = UIThemeListener.getTheme()
    var themeColor: ThemeColor = ColorManager.getColor(theme)
    private var isNew = false

    private var questionID = ""
    private var questionContent = ""

    private var autoCloseTag = false

    /**
     * 编辑器的应用意图，注意，如果用户进行多轮会话未采纳时，应保证此对象的唯一性，即只有一个
     * 用户一轮采纳、拒绝之后，editorIntent 重置为空，为下一轮做准备
     * 流式 Apply 时的特殊逻辑处理
     *
     * 1. 多个问题一直未采纳时，会使用 selectedCode 字段来保存最开始的文件状态。使用 oldDocument 来保存本轮 Apply 之前用户的代码，
     * 2. 用户 Diff 对比时，使用 selectedCode 与 editor.document 的内容作对比
     * 3. 用户采纳时，使用 editor.document 代码持久化
     * 4. 用户拒绝时，使用 selectedCode 代码进行回退
     * 5. Apply 取消时，使用 oldDocument 代码进行回退
     */
    private var editorIntent: CodeApplyIntent? = null

    /**
     * 应用流式输出的结果，判断逻辑如下
     * 1. 当前文件不存在，直接创建
     * 2. 当前文件处于别的 AI 状态，直接中断，返回异常结果
     * 3. 多次FastApply 时，会使用当前编辑器的内容作为上下文，但是 Diff 逻辑里的展示，要以 Apply 之前的原代码为主
     */
    fun applyCodeStream(bean: FastApplyBean, project: Project) {
        val filePath = bean.fileUrl!!
        var currVFile = VfsUtils.getLocalVfsFile(filePath, project)
        if (null == currVFile) {
            bean.isNewFile = true
            currVFile = VfsUtils.getOrCrateVfsFile(filePath, project)
            questionID = bean.questionUid ?: ""
            project.service<SnapShotWrapper>().composerDoAdd(bean.sessionId!!, filePath, true)
        } else {
            // 同一个问题，同一个文件可能有多次 Apply，此处只记录问题第一次 Apply 时的状态。
            if (null == editorIntent || StrUtil.isEmpty(questionID) || questionID != bean.questionUid) {
                questionID = bean.questionUid ?: ""
                project.service<SnapShotWrapper>().composerDoAdd(bean.sessionId!!, filePath, false)
                questionContent = VfsUtils.getContent(currVFile)
            }
        }


        if (null == currVFile) {
            logger.info("FastApply 新增文件失败")
            project.service<JSApiService>().sendApplyStatus(bean.traceId!!, bean.fileUrl!!, CommonResultEnum.ERROR.name)
            return
        }

        if (!project.service<FileAITagService>().isActionToCodeEnable(currVFile!!)) {
            project.service<JSApiService>()
                .sendApplyStatus(bean.traceId!!, bean.fileUrl!!, CommonResultEnum.CONFLICT.name)
        } else {
            project.service<FileAITagService>().putActionToCodeTag(listOf(FileTag(bean.fileUrl!!, Tag.ACTION_TO_CODE)))
        }
        // TODO 要考虑后期不打开编辑器,而实现 FastApply 的场景
        runInEdt {
            FileEditorManager.getInstance(project).openFile(currVFile, true).firstOrNull { it is TextEditor }
                ?.let { currFileEditor ->
                    // 此后对编辑器的操作是有时序的，要放在一个线程上下文内执行，并确保每次都是档次生成的代码
                    invokeLater {
                        if (bean.isAgent) {
                            addMarkUpCodeWithAgent(project, currFileEditor as TextEditor, bean)
                        } else {
                            addMarkUpCodeV2(project, currFileEditor as TextEditor, bean)
                        }
                    }
                }
        }
    }

    /**
     * 采纳代码
     * 前端过来的采纳或拒绝，不做重复发送
     */
    fun accept(doSend: Boolean) {
        editorIntent?.let { doAccept(it, doSend = doSend) }
    }

    /**
     * 拒绝代码
     * 前端过来的采纳或拒绝，不做重复发送
     */
    fun reject(doSend: Boolean) {
        editorIntent?.let { doReject(it, doSend) }
    }

    // 关闭文件后续还原操作
    fun closeFile() {
        val codeApplyIntent = editorIntent ?: return
        if (codeApplyIntent.isProcessing) {
            runWriteAction {
                codeApplyIntent.getDocument().setText(codeApplyIntent.oldDocument)
            }
            codeApplyIntent.isCancel = true
            doCloseTopBar(codeApplyIntent)
        }
        editorIntent = null
    }


    /**
     * 是否是进程中
     */
    fun isProcessing(): Boolean {
        return true == editorIntent?.isProcessing
    }

    /**
     * 初始化编辑器顶部 ToolBar
     */
    private fun initEditorToolBar(intent: CodeApplyIntent) {
        val fileEditor = intent.fileEditor
        fileEditor.putUserData(ComposerKey.SHOW, true)
        fileEditor.putUserData(ComposerKey.DIFF) { doShowDiff(intent) }
        fileEditor.putUserData(ComposerKey.APPLY) { doAccept(intent) }
        fileEditor.putUserData(ComposerKey.CANCEL) { doReject(intent) }
        fileEditor.putUserData(ComposerKey.DISABLE, true)
        updateTopToolBar(intent)
    }

    /**
     * 关闭顶部弹窗,关闭文件标识
     */
    private fun doCloseTopBar(intent: CodeApplyIntent) {
        intent.fileEditor.putUserData(ComposerKey.SHOW, false)
        intent.isProcessing = false
        intent.getProject().service<FileAITagService>().removeActionToCodeTag(intent.fileEditor.file!!)
        updateTopToolBar(intent)
    }

    /**
     * 添加流式输出的结果
     * 1. 流式输出的过程中，别的 AI 提示暂不要出现
     */
    private fun addMarkUpCodeV2(
        project: Project,
        fileEditor: TextEditor,
        bean: FastApplyBean,
    ) {
        // 获取对应的老代码
        // 这里要注意一点儿，DIff 的老代码要以之前的老代码为主
        // 如果当前存在对话意图，则以对话意图内的老代码为主，否则取文件系统内的代码内容
        if (null == editorIntent) {
            editorIntent = CodeApplyIntent(fileEditor).apply {
                this.uuid = UUID.randomUUID().toString()
                // 默认全选的逻辑是否要去掉
                this.selectedCode =
                    VfsUtils.getContentByDocument(fileEditor.file!!) ?: VfsUtils.getContent(fileEditor.file!!)
            }
        }
        editorIntent?.let { codeApplyIntent ->
            codeApplyIntent.isCancel = false
            codeApplyIntent.isProcessing = true
            val editor = fileEditor.editor
            initEditorToolBar(codeApplyIntent)
            laterUpdateStatus(editor, codeApplyIntent)

            val streamDiffRequest = StreamDiffRequest(
                oldLines = codeApplyIntent.selectedCode.split("\n").toMutableList(),
                StreamDiffHandler(project, editor, 0, editor.document.lineCount, codeApplyIntent)
            )
            runAsync {
                try {
                    streamDiffRequest.processStreamWithAutoFix(
                        FlowUtil.getFastApplyCode(bean, project),
                        callFun = {
                            val (currAddLines, currRemoveLines) = DiffUtil.computeDifferences(
                                codeApplyIntent.oldDocument,
                                codeApplyIntent.newCode
                            )
                            val (totalAddLines, totalRemoveLines) = DiffUtil.computeDifferences(
                                codeApplyIntent.selectedCode,
                                codeApplyIntent.newCode
                            )
                            project.service<JSApiService>()
                                .sendApplyStatus(
                                    bean.traceId!!,
                                    bean.fileUrl!!,
                                    CommonResultEnum.SUCCESS.name,
                                    currAddLines,
                                    currRemoveLines,
                                    totalAddLines,
                                    totalRemoveLines,
                                )
                        },
                        refreshDiff = {
                            this.refreshEditorDiffView()
                            editorIntent?.isProcessing = false
                            fileEditor.putUserData(ComposerKey.DISABLE, false)
                            this.updateTopToolBar(codeApplyIntent)
                            // 如果是新文件，则格式化一遍代码
                            if (bean.isNewFile) {
                                PsiDocumentManager
                                    .getInstance(project)
                                    .getPsiFile(editor.document)
                                    ?.let {
                                        CodeStyleManager.getInstance(project).reformat(it)
                                    }
                            }
                        }
                    )
                } catch (e: Exception) {
                    logger.info("流式生成代码异常", e)
                    editorIntent?.isProcessing = false
                    project.service<JSApiService>()
                        .sendApplyStatus(bean.traceId!!, bean.fileUrl!!, CommonResultEnum.ERROR.name)
                }
            }
        }
    }

    /**
     * 添加流式输出的结果
     * 1. 流式输出的过程中，别的 AI 提示暂不要出现
     */
    private fun addMarkUpCodeWithAgent(
        project: Project,
        fileEditor: TextEditor,
        bean: FastApplyBean,
    ) {
        // 获取对应的老代码
        // 这里要注意一点儿，DIff 的老代码要以之前的老代码为主
        // 如果当前存在对话意图，则以对话意图内的老代码为主，否则取文件系统内的代码内容
        if (null == editorIntent) {
            editorIntent = CodeApplyIntent(fileEditor).apply {
                this.uuid = UUID.randomUUID().toString()
                // 默认全选的逻辑是否要去掉
                this.selectedCode =
                    VfsUtils.getContentByDocument(fileEditor.file!!) ?: VfsUtils.getContent(fileEditor.file!!)
            }
        }
        editorIntent?.let { codeApplyIntent ->
            codeApplyIntent.isCancel = false
            codeApplyIntent.isProcessing = true
            val editor = fileEditor.editor
            initEditorToolBar(codeApplyIntent)
            laterUpdateStatus(editor, codeApplyIntent)

            val streamDiffRequest = StreamDiffRequest(
                oldLines = codeApplyIntent.selectedCode.split("\n").toMutableList(),
                StreamDiffHandler(project, editor, 0, editor.document.lineCount, codeApplyIntent)
            )
            runAsync {
                try {
                    val callFun = {
                        val changePatch = DiffUtil.generateGitPatch(
                            codeApplyIntent.oldDocument,
                            codeApplyIntent.newCode
                        )

                        val (currAddLines, currRemoveLines) = DiffUtil.computeDifferences(
                            codeApplyIntent.oldDocument,
                            codeApplyIntent.newCode
                        )

                        val (totalAddLines, totalRemoveLines) = DiffUtil.computeDifferences(
                            codeApplyIntent.selectedCode,
                            codeApplyIntent.newCode
                        )

                        // TODO 暂时不发送结果
                        val result = ApplyResult(
                            bean.getQuestionHash(),
                            bean.fileUrl,
                            currAddLine = currAddLines,
                            currRemoveLine = currRemoveLines,
                            totalAddLine = totalAddLines,
                            totalRemoveLine = totalRemoveLines,
                            diffPatch = changePatch
                        )
                        ApplicationManager
                            .getApplication()
                            .messageBus
                            .syncPublisher(Topics.AGENT_APPLY_TOPIC)
                            .applyResult(result)
                    }

                    val refreshDiff = {
                        this.refreshEditorDiffView()
                        editorIntent?.isProcessing = false
                        fileEditor.putUserData(ComposerKey.DISABLE, false)
                        this.updateTopToolBar(codeApplyIntent)
                        // 如果是新文件，则格式化一遍代码
                        if (bean.isNewFile) {
                            PsiDocumentManager
                                .getInstance(project)
                                .getPsiFile(editor.document)
                                ?.let {
                                    CodeStyleManager.getInstance(project).reformat(it)
                                }
                        }
                    }

                    if (null != bean.editPlus){
                        streamDiffRequest.processNoStream(bean.draftCode ?: "",callFun,refreshDiff)
                    }else{
                        streamDiffRequest.processStreamWithAutoFix(
                            FlowUtil.getSimpleFastApplyCode(project, bean),
                            callFun,
                            refreshDiff
                        )
                    }
                } catch (e: Exception) {
                    logger.info("流式生成代码异常", e)
                    editorIntent?.isProcessing = false
//                    project.service<JSApiService>()
//                        .sendApplyStatus(bean.traceId!!, bean.fileUrl!!, CommonResultEnum.ERROR.name)
                }
            }
        }
    }

    /**
     * 清除标记代码
     */
    private fun cleanMarkupCodeV1(intent: CodeApplyIntent, action: (() -> Unit)? = null) {
        val editor = intent.getEditor()
        if (editor.isDisposed && !intent.isCancel) {
            return
        }

        // 老的高亮逻辑，移除所有高亮信息，并将老代码写入
        editor.markupModel.removeAllHighlighters()
        WriteCommandAction.runWriteCommandAction(editor.project) {
            try {
                // 将旧代码写入
                editor.document.setText(intent.selectedCode)
            } catch (e: Exception) {
                logger.info("cleanMarkupCode2 error", e)
            }
            action?.invoke()
        }

        // TODO  2期拒绝时,变更为根据高亮信息，移除新增代码
    }

    /**
     * 采纳用户的代码
     */
    fun doAccept(intent: CodeApplyIntent, mergeCodeStr: String? = null, doSend: Boolean = true) {
        logger.info("Composer accept doAction")
        if (doSend) {
            sendResult(intent, ComposerActionEnum.accept)
        }

        acceptMarkupCode(intent, mergeCodeStr)
        doCloseTopBar(intent)
        isNew = false
        editorIntent = null
    }

    /**
     * 拒绝用户的代码
     */
    fun doReject(intent: CodeApplyIntent, doSend: Boolean = true) {
        logger.info("Composer cancel doAction ")

        if (doSend) {
            sendResult(intent, ComposerActionEnum.reject)
        }
        runInEdt {
            cleanMarkupCodeV1(intent)
            doCloseTopBar(intent)
            val relativeFilePath = VfsUtils.getRelativeFilePath(intent.getEditor())
            relativeFilePath?.let {
                editorIntent = null
                //拒绝时，新建的文件默认删除
                if (isNew) {
                    VfsUtils.deleteFile(relativeFilePath, intent.getProject())
                }
            }
        }
    }

    /**
     *  做取消操作
     */
    fun doCancel() {
        editorIntent?.let {
            it.isCancel = true
            // 设置编辑器代码为老的代码
            doReject(it, false)
        }
    }

    /**
     * 重新计算 Diff，重新计算高亮的内容
     * 记录逻辑如下：
     * 1.
     */
    fun refreshEditorDiffView() {
        editorIntent?.let { intent ->
            val editor = intent.getEditor()
            val diffRequest = DiffUtil.computeDiff(intent.selectedCode, editor.document.text)
            val rangeForNew = DiffUtil.getHighLightRangeForNew(diffRequest)

            val currDiffChange = DiffUtil.computeDiff(
                intent.oldDocument,
                intent.newCode
            )
            // 开始添加高亮信息
            invokeLater {
                DocumentUtil.writeInRunUndoTransparentAction {
                    for (range in rangeForNew) {
                        for (lineNum in range.startLine..range.endLine) {
                            if (lineNum <= editor.document.lineCount) {
                                // 高亮的行数是从0开始计算
                                editor.markupModel.addLineHighlighter(
                                    lineNum - 1,
                                    HighlighterLayer.ADDITIONAL_SYNTAX,
                                    ComposerTextAttributes()
                                )
                            }
                        }
                    }

                    try {
                        val targetLine = if (null != currDiffChange?.line1 && -1 != currDiffChange.line1){
                            currDiffChange.line1
                        }else if (null != currDiffChange?.line0 && -1 != currDiffChange.line0){
                            currDiffChange.line0
                        }else{
                            editor.document.lineCount
                        }
                        val lineStartOffset = editor.document.getLineStartOffset(targetLine)
                        editor.scrollingModel.scrollTo(editor.offsetToLogicalPosition(lineStartOffset), ScrollType.CENTER_UP);
                    } catch (e: Exception) {
                        LogUtil.info("跳转到当前编辑行失败",e)
                    }
                }
            }
        }
    }

    /**
     * 获取指定的
     */
    fun getRejectCode(): String {
        return editorIntent?.selectedCode ?: ""
    }

    fun markAutoCloseTag() {
        autoCloseTag = true
    }

    fun isAutoClose() = autoCloseTag

    /**
     * 展示Diff面板
     */
    private fun doShowDiff(intent: CodeApplyIntent) {
        val buttonActions: MutableList<InlineDiffButtonAction> = mutableListOf(
            object : InlineDiffWindow.CancelAction("拒绝") {
                override fun doAction() {
                    doReject(intent)
                }
            },
            object : InlineDiffWindow.DiffAcceptAction("采纳") {
                override fun doAccept(mergeCodeStr: String?) {
                    if (null == mergeCodeStr) {
                        doAccept(intent)
                    } else {
                        var mergeCode = mergeCodeStr
                        if (!mergeCodeStr.endsWith("\n")) {
                            mergeCode += "\n"
                        }
                        doAccept(intent, mergeCode)
                    }
                }
            },
        )

        invokeLater {
            InlineDiffWindow(
                intent.getProject(),
                buttonActions,
                intent.selectedCode,
                intent.getDocument().text,
                fileType = intent.fileEditor.file.fileType
            ).show()
        }
    }

    /**
     * 接受用户的代码
     */
    private fun acceptMarkupCode(intent: CodeApplyIntent, mergeCode: String? = null) {
        runInEdt {
            val editor = intent.getEditor()
            editor.markupModel.removeAllHighlighters()
            mergeCode?.let {
                runWriteAction { editor.document.setText(mergeCode) }
            }
        }
    }

    /**
     * 更新顶部的ToolBar
     */
    private fun updateTopToolBar(intent: CodeApplyIntent) {
        runInEdt {
            EditorNotifications.getInstance(intent.getProject()).updateNotifications(intent.fileEditor.file!!)
        }
    }


    //过段时间，更新一下 currIntent 中的部分所需的状态
    private fun laterUpdateStatus(editor: Editor, intent: CodeApplyIntent) {
        invokeLater {
            val document = editor.document
            val selectionModel = editor.selectionModel
            val selectionStart = selectionModel.selectionStart
            val selectionEnd = selectionModel.selectionEnd
            val startLine = document.getLineNumber(selectionStart)

            // 老代码信息
            intent.oldStartOffset = selectionStart
            intent.oldEndOffset = selectionEnd
            intent.oldDocument = editor.document.text

            // 合并后代码信息
            intent.startOffset = document.getLineStartOffset(startLine)
            intent.endOffset = document.getLineStartOffset(document.getLineNumber(selectionEnd))
            intent.newCodeStartOffSet = intent.startOffset

            intent.selectEnd = selectionEnd == editor.document.textLength
            intent.isProcessing = true
        }
    }

    /**
     * 发送采纳结果给前端
     */
    private fun sendResult(intent: CodeApplyIntent, type: ComposerActionEnum) {
        logger.info("发送结果数据 type：${type.action}")
        intent
            .getProject()
            .service<JSApiService>()
            .sendComposerAction("", mutableListOf(FileActionInfoModel().apply {
                this.filePath = VfsUtils.getRelativeFilePath(intent.getEditor()) ?: ""
                this.action = type
            }))
    }

    override fun dispose() {
        editorIntent = null
    }
}