import kotlinx.coroutines.*
import kotlinx.serialization.*
import kotlinx.serialization.json.*
import java.util.logging.Logger
import java.lang.Exception

enum class ContentType {
    TEXT, IMAGE, EMBEDDED_RESOURCE
}

@Serializable
data class Tool(
    val name: String,
    val description: String,
    val inputSchema: JsonObject
)

@Serializable
data class TextContent(
    val type: String = "text",
    val text: String
)

@Serializable 
data class ImageContent(
    val type: String = "image",
    val data: String
)

@Serializable
data class EmbeddedResource(
    val type: String = "embedded_resource",
    val resource: String
)

class McpServerError(message: String) : Exception(message)

class Server(private val name: String) {
    private val logger: Logger = Logger.getLogger("str_replace_editor_nested")
    private lateinit var editorTool: StrReplaceEditorTool
    
    fun initialize(workspaceRootPath: String?) {
        editorTool = StrReplaceEditorTool(workspaceRootPath ?: "")
    }
    
    suspend fun listTools(): List<Tool> {
        logger.info("Listing tools")
        return listOf(
            Tool(
                name = "str_replace_editor_flattened",
                description = TOOL_DESCRIPTION,
                inputSchema = buildJsonObject {
                    put("type", "object")
                    put("properties", buildJsonObject {
                        put("path", buildJsonObject {
                            put("description", "Full path to file relative to the workspace root, e.g. 'services/api_proxy/file.py' or 'services/api_proxy'.")
                            put("type", "string")
                        })
                        put("str_replace_entries", buildJsonObject {
                            put("description", "Required parameter of `str_replace` command. A list of entries to replace. Each entry is a dictionary with keys `old_str`, `old_str_start_line_number`, `old_str_end_line_number` and `new_str`. `old_str` from different entries should not overlap.")
                            put("type", "array")
                            put("items", buildJsonObject {
                                put("type", "object")
                                put("properties", buildJsonObject {
                                    put("old_str", buildJsonObject {
                                        put("description", "The string in `path` to replace.")
                                        put("type", "string")
                                    })
                                    put("old_str_start_line_number", buildJsonObject {
                                        put("description", "The line number of the first line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.")
                                        put("type", "integer")
                                    })
                                    put("old_str_end_line_number", buildJsonObject {
                                        put("description", "The line number of the last line of `old_str` in the file. This is used to disambiguate between multiple occurrences of `old_str` in the file.")
                                        put("type", "integer")
                                    })
                                    put("new_str", buildJsonObject {
                                        put("description", "The string to replace `old_str` with. Can be an empty string to delete content.")
                                        put("type", "string")
                                    })
                                })
                                put("required", buildJsonArray {
                                    add("old_str")
                                    add("new_str")
                                    add("old_str_start_line_number")
                                    add("old_str_end_line_number")
                                })
                            })
                        })
                    })
                    put("required", buildJsonArray {
                        add("path")
                        add("str_replace_entries")
                    })
                }
            )
        )
    }
    
    suspend fun callTool(name: String, arguments: Map<String, Any>): List<TextContent> {
        try {
            logger.info("Calling tool: $name with arguments: $arguments")
            
            val path = arguments["path"] as? String
            if (path.isNullOrBlank()) {
                val errorMsg = "Missing or invalid required parameter `path`"
                logger.warning(errorMsg)
                throw McpServerError(errorMsg)
            }
            
            val fileDetails = editorTool.readFile(path)
            
            @Suppress("UNCHECKED_CAST")
            val strReplaceEntriesRaw = arguments["str_replace_entries"] as? List<Map<String, Any>>
            val strReplaceEntries = validateStrReplaceEntries(strReplaceEntriesRaw)
            
            val result = editorTool.handleStrReplace(path, fileDetails, strReplaceEntries)
            
            return listOf(TextContent(text = result))
            
        } catch (e: Exception) {
            val errorMsg = "Error processing editor operation: ${e.message}"
            logger.severe("$errorMsg\n${e.stackTraceToString()}")
            throw McpServerError(errorMsg)
        }
    }
    
    private fun validateStrReplaceEntries(entriesRaw: List<Map<String, Any>>?): List<StrReplaceEntry> {
        if (entriesRaw.isNullOrEmpty()) {
            throw IllegalArgumentException("str_replace_entries parameter is required and cannot be empty")
        }
        
        return entriesRaw.map { entryMap ->
            val oldStr = entryMap["old_str"] as? String
                ?: throw IllegalArgumentException("old_str is required in str_replace_entries")
            
            val newStr = entryMap["new_str"] as? String
                ?: throw IllegalArgumentException("new_str is required in str_replace_entries")
            
            val oldStrStartLineNumber = when (val value = entryMap["old_str_start_line_number"]) {
                is Int -> value
                is Number -> value.toInt()
                else -> throw IllegalArgumentException("old_str_start_line_number must be an integer")
            }
            
            val oldStrEndLineNumber = when (val value = entryMap["old_str_end_line_number"]) {
                is Int -> value
                is Number -> value.toInt()
                else -> throw IllegalArgumentException("old_str_end_line_number must be an integer")
            }
            
            StrReplaceEntry(oldStr, newStr, oldStrStartLineNumber, oldStrEndLineNumber)
        }
    }
}

suspend fun serve() {
    val server = Server("str-replace-editor")
    val workspaceRootPath = System.getenv("WORKSPACE")
    server.initialize(workspaceRootPath)
    
    // Simulate stdio server behavior
    println("Server initialized and ready to serve requests")
    
    // In a real implementation, this would handle stdin/stdout communication
    // For now, we'll just keep the coroutine alive
    try {
        while (true) {
            delay(1000)
            // Handle incoming requests here
        }
    } catch (e: Exception) {
        println("Server error: ${e.message}")
        throw e
    }
}