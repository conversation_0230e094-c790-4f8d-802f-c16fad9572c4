package com.alipay.tsingyan.services.completion.edit.util

import cn.hutool.core.util.StrUtil
import com.alipay.tsingyan.model.enums.LanguageEnum
import com.alipay.tsingyan.services.completion.AntCompletion
import com.alipay.tsingyan.services.completion.edit.*
import com.alipay.tsingyan.services.completion.edit.model.*
import com.alipay.tsingyan.services.completion.edit.service.LanguageSupport
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.completion.CodeUtils
import com.alipay.tsingyan.utils.contentEqualsNew
import com.intellij.codeInsight.template.TemplateManager
import com.intellij.diff.util.LineRange
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actions.EditorActionUtil
import com.intellij.openapi.editor.ex.DocumentEx
import com.intellij.openapi.editor.ex.util.EditorUtil
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.Pair
import com.intellij.openapi.util.ProperTextRange
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vfs.VfsUtilCore
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiFile
import com.intellij.util.concurrency.annotations.RequiresEdt
import com.intellij.util.containers.ContainerUtil
import com.intellij.util.diff.Diff
import com.intellij.util.diff.FilesTooBigForDiffException
import com.intellij.util.text.TextRanges
import org.apache.commons.lang3.StringUtils
import java.util.*
import kotlin.math.min

/**
 * 编辑器工具类
 */
object AntEditorUtil {
    val KEY_REQUESTS = Key.create<MutableList<EditorRequest>>("ant.editorRequests")
    val KEY_LANGUAGE = Key.create<String>("ant.editorLanguage")
    val MAGIC_ARRAY = intArrayOf(41, 32, 123)
    val MAGIC_ARRAY3 = intArrayOf(32, 41, 32, 123)
    val MAGIC_ARRAY2 = intArrayOf(41, 123)
    val MAGIC_ARRAY4 = intArrayOf(41)

    @JvmOverloads
    fun indentLine(
        project: Project?,
        editor: Editor,
        lineNumber: Int,
        indent: Int,
        caretOffset: Int,
        shouldUseSmartTabs: Boolean = EditorActionUtil.shouldUseSmartTabs(project, editor)
    ): Int {
        val editorSettings = editor.settings
        val tabSize = editorSettings.getTabSize(project)
        val document = editor.document
        val text = document.immutableCharSequence
        var spacesEnd = 0
        var lineStart = 0
        var lineEnd = 0
        var tabsEnd = 0
        if (lineNumber < document.lineCount) {
            lineStart = document.getLineStartOffset(lineNumber)
            lineEnd = document.getLineEndOffset(lineNumber)
            spacesEnd = lineStart
            var inTabs = true
            while (spacesEnd <= lineEnd && spacesEnd != lineEnd) {
                val c = text[spacesEnd]
                if (c != '\t') {
                    if (inTabs) {
                        inTabs = false
                        tabsEnd = spacesEnd
                    }
                    if (c != ' ') {
                        break
                    }
                }
                spacesEnd++
            }
            if (inTabs) {
                tabsEnd = lineEnd
            }
        }
        var newCaretOffset = caretOffset
        if (newCaretOffset >= lineStart && newCaretOffset < lineEnd && spacesEnd == lineEnd) {
            spacesEnd = newCaretOffset
            tabsEnd = Math.min(spacesEnd, tabsEnd)
        }
        val oldLength = getSpaceWidthInColumns(text, lineStart, spacesEnd, tabSize)
        val tabsEnd2 = getSpaceWidthInColumns(text, lineStart, tabsEnd, tabSize)
        var newLength = oldLength + indent
        if (newLength < 0) {
            newLength = 0
        }
        var tabsEnd3 = tabsEnd2 + indent
        if (tabsEnd3 < 0) {
            tabsEnd3 = 0
        }
        if (!shouldUseSmartTabs) {
            tabsEnd3 = newLength
        }
        val buf = StringBuilder(newLength)
        var i = 0
        while (i < newLength) {
            if (tabSize <= 0 || !editorSettings.isUseTabCharacter(project) || i + tabSize > tabsEnd3) {
                buf.append(' ')
                i++
            } else {
                buf.append('\t')
                i += tabSize
            }
        }
        val newSpacesEnd = lineStart + buf.length
        if (newCaretOffset >= spacesEnd) {
            newCaretOffset += buf.length - (spacesEnd - lineStart)
        } else if (newCaretOffset >= lineStart && newCaretOffset > newSpacesEnd) {
            newCaretOffset = newSpacesEnd
        }
        return newCaretOffset
    }

    private fun getSpaceWidthInColumns(seq: CharSequence, startOffset: Int, endOffset: Int, tabSize: Int): Int {
        var result = 0
        for (i in startOffset until endOffset) {
            if (seq[i] == '\t') {
                result = (result / tabSize + 1) * tabSize
            } else {
                result++
            }
        }
        return result
    }

    /**
     * 是否是选中的正在编辑的编辑器
     *
     * @return
     */
    fun isSelectedEditor(editor: Editor): Boolean {
        val myProject = editor.project
        if (null == myProject || myProject.isDisposed) {
            return false
        }
        var editorManager: FileEditorManager = FileEditorManager.getInstance(myProject)
        /**
         * 判断是否是当前正在编辑的编辑器
         */
        if (null == editorManager) {
            return false
        }
        if (editorManager is FileEditorManagerImpl) {
            val current = (editorManager as FileEditorManagerImpl).getSelectedTextEditor(true)
            return null != current && current == editor
        }
        val current = editorManager.selectedEditor
        return current is TextEditor && editor == current.editor
    }

    /**
     * 计算空格的行偏移量
     *
     * @param lineContent
     * @return
     */
    fun whitespacePrefixLength(lineContent: String): Int {
        val maxLength = lineContent.length
        var i = 0
        while (i < maxLength) {
            val c = lineContent[i]
            if (c != ' ' && c != '\t') {
                return i
            }
            i++
        }
        return i
    }

    /**
     * 获取光标前的空白
     */
    fun trailingWhitespace(text: String): String {
        if (text.isEmpty()) {
            return ""
        }
        var endOffset = text.length
        var ch: Char = text[endOffset - 1]
        while (endOffset > 0 && ch != '\n' && Character.isWhitespace(ch)) {
            endOffset--
            if(endOffset < 1 || endOffset > text.length){
                break
            }
            ch = text[endOffset - 1]
        }
        return text.substring(endOffset)
    }


    fun leadingWhitespaceLength(text: String): Int {
        val length = text.length
        var offset: Int
        offset = 0
        while (offset < length) {
            val ch = text[offset]
            if (ch == '\n' || !Character.isWhitespace(ch)) {
                break
            }
            ++offset
        }
        return offset
    }

    /**
     * 获取文档的修改痕迹
     *
     * @param document
     * @return
     */
    fun getDocumentModificationStamp(document: Document): Long {
        return if (document is DocumentEx) {
            document.modificationSequence.toLong()
        } else document.modificationStamp
    }

    /**
     * 将对应的请求添加到编辑器中
     *
     * @param editor
     * @param request
     */
    fun addEditorRequest(editor: Editor, request: EditorRequest) {
        EditorUtil.disposeWithEditor(editor, request.disposable!!)
        if (!KEY_REQUESTS.isIn(editor)) {
            KEY_REQUESTS[editor] = ContainerUtil.createLockFreeCopyOnWriteList()
        }
        KEY_REQUESTS.getRequired(editor).add(request)
    }

    /**
     * 缓存当前editor的语言
     */
    fun getLanguage(editor: Editor) : String{
        if (!KEY_LANGUAGE.isIn(editor)) {
            var language: String? = null
            var fileExtension:LanguageEnum = LanguageEnum.UNKOOW
            val fileName = PsiDocumentManager.getInstance(editor.project!!)
                .getPsiFile(editor.document)?.name
            if (fileName != null){
                fileExtension = LanguageEnum.getLanguageEnum(fileName)
            }

            if (LanguageEnum.UNKOOW == fileExtension){
                ApplicationManager.getApplication().runReadAction {
                    language =
                        PsiDocumentManager.getInstance(editor.project!!)
                            .getPsiFile(editor.document)?.language?.id?.lowercase()
                }
            } else {
                language = fileExtension.name.lowercase()
            }

            KEY_LANGUAGE[editor] = language
        }

        return KEY_LANGUAGE.get(editor)
    }

    /**
     * 创建对应的执行请求
     *
     * @param editor
     * @param offset
     * @param completionType
     * @return
     */
    @RequiresEdt
    fun createEditorRequest(editor: Editor, offset: Int, completionType: CompletionTypeEnum, antRequestTypeEnum: AntRequestTypeEnum): BasicEditorRequest? {

        var language: LanguageSupport
        val project = editor.project ?: return null
        var document: Document = editor.document
        var file: PsiFile? = PsiDocumentManager.getInstance(project).getPsiFile(document)
        var sourceCodePrefix = ""
        var sourceCodeSuffix = ""
        if (completionType == CompletionTypeEnum.CodeEdits){
            var lineRange:LineRange
            if (antRequestTypeEnum.name == AntRequestTypeEnum.PreCodeEdits.name){
                //调用PreCodeEdits，这时候要根据传入的offset去判断当前的行号，不能用光标所在位置的行号。
                val nextTabLine = document.getLineNumber(offset)
                lineRange = CommonUtils.getCodeEditsContext(
                    nextTabLine,
                    editor.document.lineCount,
                    AppConstant.COMPLETION_CONFIG.editsCursorBefore,
                    AppConstant.COMPLETION_CONFIG.editsCursorAfter
                )
            } else {
                // 获取上下文范围（光标附近的行范围）
                val caretModel = editor.caretModel
                val document = editor.document
                // 当前光标所在的行号（从0开始）
                val caretLine = caretModel.logicalPosition.line
                lineRange = CommonUtils.getCodeEditsContext(
                    caretLine,
                    editor.document.lineCount,
                    AppConstant.COMPLETION_CONFIG.editsCursorBefore,
                    AppConstant.COMPLETION_CONFIG.editsCursorAfter
                )
            }
            // 从 document 中获取上下文内容
            val codeEditPair = CommonUtils.getCodeEditsPrefixAndSuffix(document, lineRange.start, lineRange.end, offset)
            sourceCodePrefix = codeEditPair.first
            sourceCodeSuffix = codeEditPair.second
        }
        return if (file == null) {
            null
        } else BasicEditorRequest(
            project,
            null,
            completionType,
            LanguageInfo(file.language, ""),
            AppConstant.FILE_URL,
            document.text,
            offset,
            LineInfoModel.create(document, offset),
            editor.caretModel.logicalPosition.column,
            editor.settings.isUseTabCharacter(project),
            editor.settings.getTabSize(project),
            getDocumentModificationStamp(document),
            antRequestTypeEnum,
            sourceCodePrefix,
            sourceCodeSuffix
        )
    }

    fun getRelativeFilePath(project: Project, file: PsiFile): String {
        var relativePath: String = file.name
        val vFile = file.virtualFile
        var root = ProjectRootManager.getInstance(project).fileIndex.getContentRootForFile(vFile)
        if(root == null){
            return relativePath
        }
        var path = VfsUtilCore.getRelativePath(vFile, root)
        if (!(vFile == null || path == null)) {
            relativePath = path
        }
        return relativePath
    }


    fun createEditorCompletion(
        completionsModel: CompletionsModel,
        codefuseCompletion: AntCompletion,
        dropLinePrefix: Boolean,
        contextID: String
    ): AntInlayList? {
        val request = completionsModel.editorRequest
        val lines = ArrayList<String>(codefuseCompletion.completion)
        if (request == null || lines.isEmpty() || (lines.size == 1 && lines[0] != null && (lines[0].isEmpty() || lines[0].equals("\n")))) {
            return null
        }
        if (!request.requestType.isManual){
            dropOverlappingTrailingLines(lines, request.documentContent, request.offset)
        }
        if (lines.isEmpty()) {
            return null
        }
        val replacementText = createReplacementText(request.lineInfo, lines)
        val replaceLinePrefix = dropLinePrefix && adjustWhitespace(lines, request.lineInfo)
        return if (lines.isEmpty()) {
            null
        } else DefaultInlayList(
            codefuseCompletion,
            createReplacementRange(
                request,
                replaceLinePrefix
            ),
            replacementText,
            createEditorInlays2(
                request,
                lines
            ),
            contextID!!,
            completionsModel
        )
    }

    fun apiChoiceWithoutPrefix(apiChoice: AntCompletion, prefix: String): AntCompletion? {
        if (prefix.isEmpty()) {
            return apiChoice
        }
        val ignoreFirstWhiteSpace = leadingWhitespace(prefix).isEmpty()
        val completion = apiChoice.completion
        var remainingPrefix = prefix
        var i = 0
        val completionSize = completion.size
        while (i < completionSize) {
            val line = completion[i]?:break
            val prefixLineEnd = remainingPrefix.indexOf(10.toChar())
            val prefixLine =
                remainingPrefix.substring(0, if (prefixLineEnd == -1) remainingPrefix.length else prefixLineEnd)
            if (!ignoreFirstWhiteSpace || i != 0) {
                if (!(if (prefixLineEnd == -1) line.startsWith(prefixLine) else line == prefixLine)) {
                    return null
                }
            } else {
                val trimmedLine = stripLeading(line)
                if (!(if (prefixLineEnd == -1) trimmedLine.startsWith(prefixLine) else trimmedLine == prefixLine)) {
                    return null
                }
            }
            if (prefixLineEnd == -1) {
                val newCompletions = ArrayList<String>(completionSize - i)
                newCompletions.add(
                    line.substring(
                        (if (!ignoreFirstWhiteSpace || i != 0) 0 else leadingWhitespace(line)
                            .length) + prefixLine.length
                    )
                )
                if (i + 1 < completionSize) {
                    newCompletions.addAll(completion.subList(i + 1, completionSize))
                }
                return apiChoice.withCompletion(newCompletions)
            }
            remainingPrefix = remainingPrefix.substring(prefixLineEnd + 1)
            i++
        }
        return null
    }

    fun stripLeading(text: String): String {
        if (text.isEmpty()) {
            return ""
        }
        val length = leadingWhitespaceLength(text)
        return if (length == 0) text else text.substring(length)
    }


    private fun createEditorInlays2(request: EditorRequest, lines: List<String>): MutableList<AntEditorInlay?> {
        val inlays = ArrayList<AntEditorInlay>()
        val offset = request.offset
        if (lines.size > 1 && request.lineInfo.isBlankLine && lines[0].isEmpty()) {
            inlays.add(DefaultCopilotEditorInlay(AntCompletionTypeEnum.Block, offset, lines.subList(1, lines.size).toMutableList()))
        } else {
            val editorLineSuffix: String = request.lineInfo.lineSuffix
            val completionLine = lines[0]
            val diffs: List<Pair<Int, String>?>? = createDiffInlays2(editorLineSuffix, completionLine)
            if (!diffs.isNullOrEmpty()) {
                val iterator: Iterator<*> = diffs.iterator()
                while (iterator.hasNext()) {
                    val diff: Pair<Int, String> = iterator.next() as Pair<Int, String>
                    val delta = diff.getFirst() as Int
                    inlays.add(
                        DefaultCopilotEditorInlay(
                            AntCompletionTypeEnum.Inline,
                            offset + delta,
                            java.util.List.of(diff.second)
                        )
                    )
                }
            }

            if (lines.size > 1) {
                inlays.add(DefaultCopilotEditorInlay(AntCompletionTypeEnum.Block, offset, lines.subList(1, lines.size).toMutableList()))
            }
        }

        return inlays.toMutableList()
    }

    fun createDiffInlays2(editorLineSuffix: String, completion: String): List<Pair<Int, String>?>? {
        val commonPrefix: String = findCommonPrefix2(completion, editorLineSuffix)
        val editorAdjusted = editorLineSuffix.substring(commonPrefix.length)
        val completionAdjusted = completion.substring(commonPrefix.length)
        var editorChars = editorAdjusted.chars().toArray()
        val completionChars = completionAdjusted.chars().toArray()
        patchCharPairs2(completionChars)
        val patchDelta = commonPrefix.length
        return try {
            if (editorLineSuffix.replace("\\s".toRegex(), "").contentEqualsNew("){") && !(completionChars.contains(65537) || completionChars.contains(41))){ //char 41 is ), char 65537 is )
                editorChars = MAGIC_ARRAY2
            }
            if (editorLineSuffix.trim().contentEqualsNew(")")){
                editorChars = MAGIC_ARRAY4
            }
            val changelist = Diff.buildChanges(editorChars, completionChars)
            if (changelist == null) {
                null
            } else {
                val result: LinkedList<Pair<Int, String>?> = LinkedList<Pair<Int, String>?>()
                val changes = changelist.toList()
                val iterator: Iterator<*> = changes.iterator()
                while (iterator.hasNext()) {
                    val change = iterator.next() as Diff.Change
                    if (change.inserted > 0) {
                        result.add(
                            Pair.create<Int, String>(
                                change.line0 + patchDelta,
                                unpatchCharPairs2(
                                    completionChars,
                                    change.line1,
                                    change.inserted
                                )
                            )
                        )
                    }
                }
                result
            }
        } catch (var13: FilesTooBigForDiffException) {
            null
        }
    }

    fun createDiffInlays3(editor: String, completion: String): List<Pair<Int, String>>? {
        val commonPrefix: String = findCommonPrefix3(completion, editor)
        val editorAdjusted = editor.substring(commonPrefix.length)
        val completionAdjusted = completion.substring(commonPrefix.length)
        val editorChars = editorAdjusted.chars().toArray()
        val completionChars = completionAdjusted.chars().toArray()
        patchCharPairs3(completionChars)
        val patchDelta = commonPrefix.length
        try {
            val changelist = Diff.buildChanges(editorChars, completionChars) ?: return null
            val result = LinkedList<Pair<Int, String>>()
            val changes = changelist.toList()
            val it: Iterator<Diff.Change> = changes.iterator()
            while (it.hasNext()) {
                val change = it.next()
                if (change.inserted > 0) {
                    result.add(
                        Pair.create<Int, String>(
                            change.line0 + patchDelta,
                            unpatchCharPairs3(
                                completionChars,
                                change.line1,
                                change.inserted
                            )
                        )
                    )
                }
            }
            return result
        } catch (e: FilesTooBigForDiffException) {
            return null
        }
    }

    fun unpatchCharPairs3(patchedData: IntArray, offset: Int, count: Int): String {
        val result = IntArray(count)
        for (i in 0 until count) {
            val c = patchedData[offset + i]
            when (c) {
                65536 -> result[i] = 40  //char 40 is (
                65537 -> result[i] = 41 //char 41 is )
                65538 -> result[i] = 123 //char 123 is {
                65539 -> result[i] = 125 //char 125 is }
                65540 -> result[i] = 91 //char 91 is [
                65541 -> result[i] = 93 //char 93 is ]
                else -> result[i] = c
            }
        }
        return String(result, 0, count)
    }

    private fun findCommonPrefix3(data: String, reference: String): String {
        val maxSize = min(data.length.toDouble(), reference.length.toDouble()).toInt()
        var first = 0
        var i = 0
        while (i < maxSize && data[i] == reference[i]) {
            first++
            i++
        }
        return data.substring(0, first)
    }

    fun patchCharPairs3(chars: IntArray): IntArray {
        var closeIndex: Int = 0
        val stringRanges: TextRanges = findStringRanges3(chars) //找到 " 和 ' 的范围
        for (i in chars.indices) {
            val c = chars[i]
            if ((c == 40 || c == 41) && isInRange3(stringRanges, i)) { //char 40 is (, char 41 is )
                chars[i] = 65536 + (if (c == 41) 1 else 0)
            } else if (c == 40 && (firstMatchingPair3(
                    chars,
                    i + 1,
                    ')',
                    '(',
                    stringRanges
                ).also {
                    closeIndex = it
                }) != -1
            ) {
                chars[i] = 65536
                chars[closeIndex] = 65537
            }
        }
        return chars
    }

    private fun firstMatchingPair3(
        chars: IntArray,
        startIndex: Int,
        pairClose: Char,
        pairOpen: Char,
        excludedRanges: TextRanges
    ): Int {
        var openCount = 0
        for (i in startIndex until chars.size) {
            if (!isInRange3(excludedRanges, i)) {
                val c = chars[i]
                if (c == pairOpen.code) {
                    openCount++
                } else if (c != pairClose.code) {
                    continue
                } else if (openCount == 0) {
                    return i
                } else {
                    openCount--
                }
            }
        }
        return -1
    }

    private fun findStringRanges3(chars: IntArray): TextRanges {
        val ranges = TextRanges()
        var singleQuotedStart = -1
        var doubleQuotedStart = -1
        for (i in chars.indices) {
            val c = chars[i]
            if (c == 34 && singleQuotedStart == -1) { //char 34 is "
                if (doubleQuotedStart == -1) {
                    doubleQuotedStart = i
                } else {
                    ranges.union(ProperTextRange(doubleQuotedStart, i))
                    doubleQuotedStart = -1
                }
            } else if (c == 39 && doubleQuotedStart == -1) { //char 39 is '
                if (singleQuotedStart == -1) {
                    singleQuotedStart = i
                } else {
                    ranges.union(ProperTextRange(singleQuotedStart, i))
                    singleQuotedStart = -1
                }
            }
        }
        return ranges
    }

    private fun isInRange3(ranges: TextRanges, i: Int): Boolean {
        val it: Iterator<*> = ranges.iterator()
        while (it.hasNext()) {
            val range = it.next() as TextRange
            if (range.contains(i)) {
                return true
            }
            if (i > range.endOffset) {
                return false
            }
        }
        return false
    }

    fun unpatchCharPairs2(patchedData: IntArray, offset: Int, count: Int): String? {
        val result = IntArray(count)
        for (i in 0 until count) {
            val c = patchedData[offset + i]
            when (c) {
                65536 -> result[i] = 40 // (
                65537 -> result[i] = 41 // )
                65538 -> result[i] = 123 // {
                65539 -> result[i] = 125 // }
                65540 -> result[i] = 91 // [
                65541 -> result[i] = 93 // ]
                else -> result[i] = c
            }
        }
        return String(result, 0, count)
    }

    fun patchCharPairs2(chars: IntArray): IntArray? {
        val stringRanges: TextRanges = findStringRanges(chars)
        for (i in chars.indices) {
            val c = chars[i]
            if ((c == 40 || c == 41) && isInRange(stringRanges, i)) {
                chars[i] = 65536 + if (c == 41) 1 else 0
            } else if (c == 40) {
                val closeIndex: Int =
                    firstMatchingPair2(chars, i + 1, ')', '(', stringRanges)
                if (closeIndex != -1) {
                    chars[i] = 65536
                    chars[closeIndex] = 65537
                }
            }
        }
        return chars
    }

    private fun findCommonPrefix2(data: String, reference: String): String {
        val maxSize = Math.min(data.length, reference.length)
        var first = 0
        var i = 0
        while (i < maxSize && data[i] == reference[i]) {
            ++first
            ++i
        }
        return data.substring(0, first)
    }

    private fun firstMatchingPair2(
        chars: IntArray,
        startIndex: Int,
        pairClose: Char,
        pairOpen: Char,
        excludedRanges: TextRanges
    ): Int {
        var openCount = 0
        for (i in startIndex until chars.size) {
            if (!isInRange(excludedRanges, i)) {
                val c = chars[i]
                if (c == pairOpen.code) {
                    ++openCount
                } else if (c == pairClose.code) {
                    if (openCount == 0) {
                        return i
                    }
                    --openCount
                }
            }
        }
        return -1
    }


    fun createDiffInlays(editor: String, completion: String): List<Pair<Int, String>>? {
        val commonPrefix = findCommonPrefix(completion, editor)
        val editorAdjusted = editor.substring(commonPrefix.length)
        val completionAdjusted = completion.substring(commonPrefix.length)
        val editorChars = editorAdjusted.chars().toArray()
        val completionChars = completionAdjusted.chars().toArray()
        patchCharPairs(completionChars)
        val patchDelta = commonPrefix.length
        return try {
            val changelist = Diff.buildChanges(editorChars, completionChars) ?: return null
            val result = LinkedList<Pair<Int, String>>()
            val it: Iterator<Diff.Change> = changelist.toList().iterator()
            while (it.hasNext()) {
                val change = it.next()
                if (change.inserted > 0) {
                    result.add(
                        Pair.create(
                            Integer.valueOf(change.line0 + patchDelta),
                            unpatchCharPairs(completionChars, change.line1, change.inserted)
                        )
                    )
                }
            }
            result
        } catch (e: FilesTooBigForDiffException) {
            null
        }
    }

    fun unpatchCharPairs(patchedData: IntArray, offset: Int, count: Int): String? {
        val result = IntArray(count)
        for (i in 0 until count) {
            val c = patchedData[offset + i]
            when (c) {
                65536 -> result[i] = 40
                65537 -> result[i] = 41
                65538 -> result[i] = 123
                65539 -> result[i] = 125
                65540 -> result[i] = 91
                65541 -> result[i] = 93
                else -> result[i] = c
            }
        }
        return String(result, 0, count)
    }

    fun patchCharPairs(chars: IntArray): IntArray {
        var closeIndex: Int = 0
        val stringRanges = findStringRanges(chars)
        for (i in chars.indices) {
            val c = chars[i]
            if ((c == 40 || c == 41) && isInRange(stringRanges, i)) {
                chars[i] = 65536 + if (c == 41) 1 else 0
            } else if (c == 40 && firstMatchingPair(chars, i + 1, ')', '(', stringRanges).also {
                    closeIndex = it
                } != -1) {
                chars[i] = 65536
                chars[closeIndex] = 65537
            }
        }
        return chars
    }

    fun isSpacesOrTabs(text: CharSequence, withNewlines: Boolean): Boolean {
        for (i in 0 until text.length) {
            if (!isSpaceOrTab(text[i], withNewlines)) {
                return false
            }
        }
        return true
    }

    fun isSpaceOrTab(c: Char, withNewline: Boolean): Boolean {
        return c == ' ' || c == '\t' || (withNewline && c == '\n')
    }

    private fun createReplacementRange(request: EditorRequest, replaceLinePrefix: Boolean): TextRange {
        val startOffset: Int
        val endOffset: Int
        val lineInfo = request.lineInfo
        startOffset = if (replaceLinePrefix) {
            lineInfo.lineStartOffset
        } else {
            request.offset
        }
        endOffset =
            if (isReplaceLineSuffix(request)) {
                lineInfo.lineEndOffset - trailingWhitespaceLength(
                    lineInfo.lineSuffix
                )
            } else {
                request.offset
            }
        return TextRange.create(startOffset, endOffset)
    }

    private fun isReplaceLineSuffix(request: EditorRequest): Boolean {
        val lineSuffix = request.lineInfo.lineSuffix
        return isSpacesOrTabs(lineSuffix, false)
    }

    fun trailingWhitespaceLength(text: String): Int {
        var ch: Char
        if (text.isEmpty()) {
            return 0
        }
        val length = text.length
        var endOffset = length
        while (endOffset > 0 && (text[endOffset - 1].also { ch = it } == ' ' || ch == '\t')) {
            endOffset--
        }
        return length - endOffset
    }

    private fun findStringRanges(chars: IntArray): TextRanges {
        val ranges = TextRanges()
        var singleQuotedStart = -1
        var doubleQuotedStart = -1
        for (i in chars.indices) {
            val c = chars[i]
            if (c == 34 && singleQuotedStart == -1) {
                doubleQuotedStart = if (doubleQuotedStart == -1) {
                    i
                } else {
                    ranges.union(ProperTextRange(doubleQuotedStart, i))
                    -1
                }
            } else if (c == 39 && doubleQuotedStart == -1) {
                singleQuotedStart = if (singleQuotedStart == -1) {
                    i
                } else {
                    ranges.union(ProperTextRange(singleQuotedStart, i))
                    -1
                }
            }
        }
        return ranges
    }

    private fun firstMatchingPair(
        chars: IntArray,
        startIndex: Int,
        pairClose: Char,
        pairOpen: Char,
        excludedRanges: TextRanges
    ): Int {
        var openCount = 0
        for (i in startIndex until chars.size) {
            if (!isInRange(excludedRanges, i)) {
                val c = chars[i]
                if (c == pairOpen.code) {
                    openCount++
                } else if (c != pairClose.code) {
                    continue
                } else if (openCount == 0) {
                    return i
                } else {
                    openCount--
                }
            }
        }
        return -1
    }

    private fun isInRange(ranges: TextRanges, i: Int): Boolean {
        val it: Iterator<*> = ranges.iterator()
        while (it.hasNext()) {
            val range = it.next() as TextRange
            if (range.contains(i)) {
                return true
            }
            if (i > range.endOffset) {
                return false
            }
        }
        return false
    }

    private fun findCommonPrefix(data: String, reference: String): String {
        val maxSize = Math.min(data.length, reference.length)
        var first = 0
        var i = 0
        while (i < maxSize && data[i] == reference[i]) {
            first++
            i++
        }
        return data.substring(0, first)
    }

    private fun adjustWhitespace(completionLines: MutableList<String>, lineInfo: LineInfoModel): Boolean {
        val editorWhitespacePrefix = lineInfo.whitespaceBeforeCursor
        if (completionLines.isEmpty() || editorWhitespacePrefix.isEmpty()) {
            return false
        }
        val isEditorEmptyLine = lineInfo.isBlankLine
        var replacePrefixInEditor = false
        val firstLine = completionLines[0]
        var firstLineFixed = firstLine
        if (firstLine.startsWith(editorWhitespacePrefix)) {
            firstLineFixed = firstLine.substring(editorWhitespacePrefix.length)
            replacePrefixInEditor = isEditorEmptyLine
        } else if (isEditorEmptyLine) {
            val lineLeadingWhitespace = leadingWhitespace(firstLine)
            firstLineFixed = firstLine.substring(lineLeadingWhitespace.length)
            replacePrefixInEditor = !firstLine.isEmpty() && !lineLeadingWhitespace.startsWith(editorWhitespacePrefix)
        }
        completionLines[0] = firstLineFixed
        return replacePrefixInEditor
    }

    fun leadingWhitespace(text: String): String {
        return if (text.isEmpty()) {
            ""
        } else text.substring(
            0,
            leadingWhitespaceLength(text)
        )
    }


    private fun createReplacementText(lineInfo: LineInfoModel, lines: List<String>): String {
        val text = StringUtil.join(lines, "\n")
        if (!lineInfo.isBlankLine) {
            val ws = lineInfo.whitespaceBeforeCursor
            if (text.startsWith(ws)) {
                return text.substring(ws.length)
            }
        }
        return text
    }

    private fun dropOverlappingTrailingLines(
        lines: ArrayList<String>,
        editorContent: String,
        offset: Int
    ): Boolean {
        var offset = offset
        if (offset < editorContent.length && editorContent[offset] == '\n') {
            offset++
        }
        if (offset >= editorContent.length) {
            return false
        }
        val overlap = findOverlappingLines(lines, getNextLines(editorContent, offset, lines.size))
        for (i in 0 until overlap) {
            lines.removeAt(lines.size - 1)
        }
        return overlap >= 1
    }

    fun getNextLines(text: String, offset: Int, maxLines: Int): List<String> {
        val lines = LinkedList<String>()
        var done = 0
        var last = offset
        while (true) {
            if (done >= maxLines) {
                break
            }
            val next = text.indexOf(10.toChar(), last)
            if (next != -1) {
                lines.add(text.substring(last, next))
                last = next + 1
                done++
            } else if (text.length > last) {
                lines.add(text.substring(last))
                done++
            } else {
                done++
            }
        }
        return lines
    }

    fun findOverlappingLines(withTrailing: List<String>, withLeading: List<String>): Int {
        if (withTrailing.isEmpty() || withLeading.isEmpty()) {
            return 0
        }
        val trailingSize = withTrailing.size
        val maxLines = Math.min(trailingSize, withLeading.size)
        var overlapping = 0
        for (i in 1..maxLines) {
            if (linesMatch(withLeading.subList(0, i), withTrailing.subList(trailingSize - i, trailingSize), true)) {
                overlapping = i
            } else if (overlapping > 0) {
                break
            }
        }
        return overlapping
    }

    fun linesMatch(a: Iterable<String>, b: Iterable<String>, trimEnd: Boolean): Boolean {
        val itA = a.iterator()
        val itB = b.iterator()
        while (itA.hasNext() && itB.hasNext()) {
            val itemA = itA.next()
            val itemB = itB.next()
            if (!(if (trimEnd) itemA.stripTrailing() == itemB.stripTrailing() else itemA == itemB)) {
                return false
            }
        }
        return !itA.hasNext() && !itB.hasNext()
    }

    // 从字符串末尾开始，不包含空格和数字
    fun containsNoLetterOrDigit(str: String): Boolean {
        val regex = Regex("[A-Za-z0-9]+") // 匹配大小写字母和数字的正则表达式
        return regex.containsMatchIn(str)
    }

    fun containsBalanced(str: String): Boolean {
        // 检查字符串是否包含括号
        if (str.contains('(') && str.contains(')')) {
            // 没有括号
            return true
        }
        // 使用上面定义的函数检查括号是否平衡
        return false
    }

    fun isAvaliableRequest(myEditor: Editor, offset: Int): Boolean {
        val document = myEditor.document  // 获取编辑器的文档对象

        // 获取当前光标所在行号
        val lineNumber = document.getLineNumber(offset)
        // 获取当前行的开始偏移量和结束偏移量
        val startLineOffset = document.getLineStartOffset(lineNumber)
        val endLineOffset = document.getLineEndOffset(lineNumber)
        // 获取当前光标所在行的内容
        val currentLine = document.getText(TextRange(startLineOffset, endLineOffset))
        //当前行后缀
        val lineSuffix = document.getText(TextRange(offset, endLineOffset))
        if(StringUtils.isNotBlank(lineSuffix)){
            return false
        }

        // 获取当前光标所在位置的前一个字符和后一个字符
        var preStr = ""
        var nextStr = ""
        if (startLineOffset < offset) {
            preStr = document.getText(TextRange(offset - 1, offset))
        }
        if (endLineOffset > offset) {
            nextStr = document.getText(TextRange(offset, offset + 1))
        }
        if(CodeUtils.isNoteOrBlank(currentLine.trim())){
            return true
        }
        // 如果上一个元素不为空格，换行，制表符，不触发补全
        if (!StrUtil.equalsAny(preStr, " ","", "\n", "\t")){
            return false
        }
        return true

    }

    fun getLineSuffix(myEditor: Editor, offset: Int): String{
        val document = myEditor.document  // 获取编辑器的文档对象
        // 获取当前光标所在行号
        val lineNumber = document.getLineNumber(offset)
        val endLineOffset = document.getLineEndOffset(lineNumber)
        //当前行后缀
        if (offset <= endLineOffset){
            val lineSuffix = document.getText(TextRange(offset, endLineOffset))
            return lineSuffix
        }
        return ""
    }


    fun getLineText(myEditor: Editor, offset: Int): String{
        val document = myEditor.document  // 获取编辑器的文档对象
        // 获取当前光标所在行号
        val lineNumber = document.getLineNumber(offset)
        val startLineOffset = document.getLineStartOffset(lineNumber)
        val endLineOffset = document.getLineEndOffset(lineNumber)
        //当前行后缀
        if (startLineOffset <= endLineOffset){
            return document.getText(TextRange(startLineOffset, endLineOffset))
        }
        return ""
    }

    fun getSelectedEditorSafely(project: Project): Editor? {
        return try {
            val editorManager = FileEditorManager.getInstance(project)
            editorManager?.selectedTextEditor
        } catch (var2: Exception) {
            null
        }
    }

    /**
     * 判断是否有LiveTemplate
     */
    fun hasLiveTemplate(project: Project?): Boolean {
        if (project == null) {
            return false
        }
        val selectedEditorSafely = getSelectedEditorSafely(project)
        if (selectedEditorSafely == null) {
            return false
        }

        val activeTemplate = TemplateManager.getInstance(project).getActiveTemplate(selectedEditorSafely)
        if (activeTemplate != null) {
            return true
        }

        return false
    }

    /**
     * 获取当前行内容
     */
    fun getCurrentLineContent(editor: Editor): String {
        try {
            if (editor.isDisposed){
                return ""
            }
            val document = editor.document
            val offset = editor.caretModel.offset
            val lineNumber = document.getLineNumber(offset)
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            return document.text.substring(lineStartOffset, lineEndOffset)
        } catch (e: Exception){
            LogUtil.info("getCurrentLineContent error", e)
        }
        return ""
    }

}