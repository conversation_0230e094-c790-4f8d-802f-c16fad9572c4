package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.model.completion.CompletionResultModel
import com.alipay.tsingyan.utils.AppConstant
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 结果缓存
 */
class ResultCache(
    /**
     * 最后一行前缀
     */
    prefixContent: String,
    /**
     * 结果集future
     */
    requestFuture: Future<CompletionResultModel>?,
) {
    val prefixContent: String
    val requestFuture: Future<CompletionResultModel>?


    /**
     * 用来判断future是否已被读取
     */
    val isReadFromFuture: AtomicBoolean = AtomicBoolean(false)
    var result: CompletionResultModel? = null


    init {
        this.prefixContent = prefixContent
        this.requestFuture = requestFuture

    }

    @Synchronized
    fun getRemoteResult(): CompletionResultModel? {
        if (isReadFromFuture.compareAndSet(false, true)) {
            result = requestFuture?.get(AppConstant.COMPLETION_CONFIG.timeOut, TimeUnit.MILLISECONDS)
        }
        return result
    }
}