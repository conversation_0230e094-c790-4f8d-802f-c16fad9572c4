package com.alipay.tsingyan.services.completion.edit

import cn.hutool.core.date.DateUtil
import com.alipay.tsingyan.BuildInfo
import com.alipay.tsingyan.utils.CommonUtils
import com.intellij.openapi.diagnostic.Logger

/**
 * 日志工具
 */
object LogUtil {
    private val LOGGER = Logger.getInstance(LogUtil::class.java)

    /**
     * 日志记录
     */
    fun info(content: String) {
        logDevInfo(content)
        LOGGER.info(content)
    }

    fun info(content: String, isCloudIde: Boolean) {
        if (BuildInfo.isDebug){
            LOGGER.info(content)
            logDevInfo(content)
            return
        }

        if (CommonUtils.isCloudIDE){
            if (isCloudIde){
                LOGGER.info(content)
            } else {
                LOGGER.debug(content)
            }
            return
        }

        LOGGER.info(content)
    }

    fun info(tag:String, content: String, isCloudIde: Boolean) {
        info("$tag $content", isCloudIde)
    }

    fun info(content: String, e: Throwable) {
        val stackTrace = e.stackTraceToString()
        val contentWithStackTrace = "$content\n$stackTrace"
        info(contentWithStackTrace)
    }

    fun debug(content: String) {
        logDevInfo(content)
        LOGGER.debug(content)
    }

    fun debug(content: String, throwable: Throwable) {
        LOGGER.debug(content, throwable)
    }


    /**
     * 日志记录
     */
    fun error(content: String, throwable: Throwable) {
        LOGGER.error(content, throwable)
    }

    /**
     * 日志记录
     */
    fun error(content: String) {
        LOGGER.error(content)
    }

    /**
     * 用于插件开发过程中，快速打印插件开发日志
     */
    private fun logDevInfo(content: String, throwable: Throwable? = null){
        onDebug {
            if (null == throwable){
                println("[${DateUtil.now()}] $content")
            }else{
                println("[${DateUtil.now()}] $content")
                throwable.printStackTrace()
            }
        }
    }
}

fun onDebug(e: () -> Unit) {
    if (BuildInfo.isDebug){
        e.invoke()
    }
}