package com.alipay.tsingyan.services.completion.edit.model

enum class AntRequestTypeEnum {
    /**
     * 自动补充类型
     */
    Automatic,

    /**
     * 强制类型
     */
    Forced,

    /**
     * 手动补全
     */
    Manual,

    /**
     * mock fastApply模拟前后补全
     */
    MOCK,

    PreCodeEdits;

    /**
     * 自动或强制补全
     *
     * @return
     */
    val isAutomaticOrForced: Boolean
        get() = this == Automatic || this == Forced

    /**
     * 强制或手动补全
     *
     * @return
     */
    val isForcedOrManual: Boolean
        get() = this == Forced || this == Manual

    /**
     * 自动补全
     *
     * @return
     */
    val isAutomatic: Boolean
        get() = this == Automatic

    /**
     * 手动触发的请求
     */
    val isManual: Boolean
        get() = this == Manual

    val isMock: Boolean
        get() = this == MOCK
}