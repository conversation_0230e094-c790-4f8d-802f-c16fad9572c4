package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.intellij.openapi.editor.Document
import com.intellij.openapi.util.TextRange

/**
 * 代码行模型
 */
class LineInfoModel(
    lineCount: Int,
    lineNumber: Int,
    lineStartOffset: Int,
    columnOffset: Int,
    line: String,
    nextLineIndent: Int
) {
    /**
     * 当前编辑器对应的总行数行数
     */
    private val lineCount: Int

    /**
     * 替换内容所在的行号
     */
    val lineNumber: Int

    /**
     * 行起始StartOffset
     */
    val lineStartOffset: Int

    /**
     * 字符串的长度，即要替换的字符串长度
     */
    private val columnOffset: Int

    /**
     * 当前的行数据
     */
    private val line: String

    /**
     * 下一行数据索引
     */
    private val nextLineIndent: Int

    init {
        this.lineCount = lineCount
        this.lineNumber = lineNumber
        this.lineStartOffset = lineStartOffset
        this.columnOffset = columnOffset
        this.line = line
        this.nextLineIndent = nextLineIndent
    }

    /**
     * 判断行信息是否为空
     *
     * @return
     */
    val isBlankLine: Boolean
        get() = line.isBlank()

    /**
     * 获取光标前的空白
     *
     * @return
     */
    val whitespaceBeforeCursor: String
        get() = AntEditorUtil.trailingWhitespace(linePrefix)

    /**
     * 获取前缀
     *
     * @return
     */
    val linePrefix: String
        get() = line.substring(0, columnOffset)
    val lineEndOffset: Int
        get() = lineStartOffset + line.length

    /**
     * 获取行后缀
     *
     * @return
     */
    val lineSuffix: String
        get() = line.substring(columnOffset)

    companion object {
        /**
         * 根据当前编辑器的元素创建行信息
         *
         * @param document
         * @param offset
         * @return
         */
        fun create(document: Document, offset: Int): LineInfoModel {
            val lineNum = document.getLineNumber(offset)
            val lineRange = TextRange.create(document.getLineStartOffset(lineNum), document.getLineEndOffset(lineNum))
            return LineInfoModel(
                document.lineCount,
                lineNum,
                lineRange.startOffset,
                offset - lineRange.startOffset,
                document.getText(lineRange),
                calculateNextLineIndent(document, offset)
            )
        }

        /**
         * 计算下一行的起始头位置
         *
         * @param document
         * @param offset
         * @return
         */
        private fun calculateNextLineIndent(document: Document, offset: Int): Int {
            val maxLines = document.lineCount
            for (line in document.getLineNumber(offset) + 1 until maxLines) {
                val start = document.getLineStartOffset(line)
                val end = document.getLineEndOffset(line)
                if (start != end) {
                    val lineContent = document.getText(TextRange.create(start, end))
                    if (!lineContent.isBlank()) {
                        return AntEditorUtil.whitespacePrefixLength(lineContent)
                    }
                }
            }
            return -1
        }
    }
}