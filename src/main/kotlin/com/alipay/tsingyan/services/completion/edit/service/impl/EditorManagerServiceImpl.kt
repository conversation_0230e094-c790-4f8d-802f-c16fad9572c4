package com.alipay.tsingyan.services.completion.edit.service.impl

import cn.hutool.core.collection.CollUtil
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.agent.*
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.codeedits.CodeEditsService
import com.alipay.tsingyan.codeedits.StagedRange
import com.alipay.tsingyan.codeedits.editor.NavigationTabService
import com.alipay.tsingyan.model.UserTypeModel
import com.alipay.tsingyan.model.completion.*
import com.alipay.tsingyan.model.enums.PermissionsEnum
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.alipay.tsingyan.services.completion.edit.*
import com.alipay.tsingyan.services.completion.edit.model.*
import com.alipay.tsingyan.services.completion.edit.model.EditorRequest
import com.alipay.tsingyan.services.completion.edit.service.AntCompletionService
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.input.InputTrackingService
import com.alipay.tsingyan.services.newcompletion.cache.CacheService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.contentEqualsNew
import com.alipay.tsingyan.utils.debounce.DebounceTime
import com.intellij.codeInsight.completion.impl.CompletionServiceImpl
import com.intellij.codeInsight.lookup.LookupManager
import com.intellij.injected.editor.EditorWindow
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.*
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.editor.impl.ImaginaryEditor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.KeyWithDefaultValue
import com.intellij.openapi.util.TextRange
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread
import com.intellij.util.concurrency.annotations.RequiresEdt
import org.apache.commons.lang3.StringUtils
import org.apache.http.util.TextUtils
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Flow
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicReference
import java.util.function.Consumer
import kotlin.Triple
import kotlin.math.abs

/**
 * 编辑器管理实现类(内嵌样式)
 * <AUTHOR>
 */
class EditorManagerServiceImpl : EditorManagerService {
    private val LOGGER: Logger = Logger.getInstance(EditorManagerServiceImpl::class.java)
    val settingData = service<TsingYanSettingStore>()
    val cacheService = service<CacheService>()
    val codeEditsService = service<CodeEditsService>()
    val inputTrackingService = service<InputTrackingService>()

    /**
     * 补全服务(内嵌样式)
     */
    val antCompletionService = service<AntCompletionService>()
    val localUserStore: LocalUserStore = service<LocalUserStore>()
    val tracerService = service<CodeFuseTracerService>()
    var agentService: AgentService = service<AgentService>()

    /**
     * 当前编辑器是否支持
     */
    private val KEY_EDITOR_SUPPORTED: Key<Boolean> = Key.create("Ant.editorSupported")

    /**
     * 请求结果，用来渲染，埋点等
     */
    private val KEY_LAST_REQUEST: Key<EditorRequestResultList> = Key.create("Ant.editorRequest")

    private val KEY_LAST_CODEEDITS_REQUEST: Key<EditorRequestResultList> = Key.create("Ant.codeEditsRequest")

    private val KEY_LAST_NEXTTAB_REQUEST: Key<EditorRequestResultList> = Key.create("Ant.nextTabRequest")

    private val KEY_LAST_PRE_CODEEDITS_KEY: Key<EditorRequestResultList> = Key.create("Ant.preCodeEditsRequest")
    /**
     * 用来缓存最新一次的cache key
     */
    private val KEY_LAST_CACHE_KEY: Key<String> = Key.create("Ant.cacheKey")

    /**
     * 镶嵌结果
     */
    val lastInlayList = AtomicReference<AntInlayList>(null)

    /**
     * 补全活动进行中
     */
    private val KEY_PROCESSING: Key<Boolean> = KeyWithDefaultValue.create("Ant.process", false)

    /**
     * 取消请求
     */
    protected val requestAlarm = CancellableAlarm(this)

    /**
     * 预加载专用的请求队列，避免与常规请求冲突
     */
    protected val nextTabRequestAlarm = CancellableAlarm(this)

    private val ACCEPT_COUNT: AtomicInteger = AtomicInteger(0)
    private val LAST_UPLOD_FILE_COUNT: AtomicInteger = AtomicInteger(0)
    private val NEW_CURRENT_FILE_COUNT: AtomicInteger = AtomicInteger(0)
    private val LAST_APPLY_TIME: AtomicLong = AtomicLong(System.currentTimeMillis()-3600)
    private val LAST_IS_APPLY: AtomicBoolean = AtomicBoolean(false)

    private val CODEEDITES_REQUEST: AtomicBoolean = AtomicBoolean(false)
    private val NEXT_TAB_REQUEST: AtomicBoolean = AtomicBoolean(false)
    private val lastCodeEditsShowData = AtomicReference<Triple<EditorRequest, StagedRange?, String>>(null)
    private val lastNextTabShowData = AtomicReference<Triple<EditorRequest, String?, Int>>(null)
    private val codeEditsCancelCountMap = ConcurrentHashMap<String, Pair<Long, Int>>()
    private val recordFileTimeStamp = ConcurrentHashMap<String, Long>()

    // 预加载相关字段
    private val preloadedCodeEditsData = AtomicReference<Triple<EditorRequest, StagedRange?, String>>(null)
    private val isPreloadDataReady = AtomicBoolean(false) //标记预加载数据是否已准备好
    private val isApplyLastNextTab = AtomicBoolean(false) //预加载取消计数
    private val isApplyCodeEdits = AtomicBoolean(false) //预加载取消计数


    /**
     * 补全入口，编辑器发生变化
     *
     * @param editor      Editor
     * @param offset      当前光标所在的位置
     */
    @RequiresEdt
    override fun editModified(editor: Editor, startOffset: Int, requestType: AntRequestTypeEnum, commandName: String?) {
        LogUtil.info("completions", "editModified ${requestType}", false)
        if (!antPreCheck(editor, requestType)){
            LogUtil.info("completions","editModified antPreCheck", false)
            return
        }
        val lastRequest = KEY_LAST_REQUEST[editor]
        val key = getVirtualFilePath(editor)
        if (!TextUtils.isEmpty(key)){
            recordFileTimeStamp.put(key, System.currentTimeMillis())
            codeEditsCancelCountMap.remove(key)
        }
        if (isDuplicateRequest(editor.document, startOffset, requestType, lastRequest)) {
            LogUtil.info("completions", "editModified inlay duplication request. ${startOffset}", false)
        } else if (isLookupUnsupported(requestType, editor)) {
            LogUtil.info("completions", "editModified isLookupUnsupported. ${startOffset}", false)
        } else {
            cancelCompletionRequests(editor)
            if (!requestType.isMock) {
                if (isBlacklistedCommand() || isUnsupportedEditorState(editor)){
                    return
                }
            }
            
            // 编辑时清理预加载数据
            clearPreloadData()
            
            val editorRequest = antCompletionService.createRequest(editor, startOffset, CompletionTypeEnum.GhostText, requestType) ?: return
            LogUtil.info("completions", "request id ${editorRequest.requestId}, start", false)

            // 后缀不能包含数组和字母
            if (requestType != AntRequestTypeEnum.MOCK && editorRequest.lineInfo.lineSuffix.isNotEmpty() && (AntEditorUtil.containsNoLetterOrDigit(editorRequest.lineInfo.lineSuffix) || AntEditorUtil.containsBalanced(editorRequest.lineInfo.lineSuffix))) {
                // 只有输入事件触发的才检查空格并触发codeEdits
                if (requestType.isAutomatic && !TextUtils.isEmpty(commandName) && (commandName!!.contentEquals("Typing") || commandName!!.contentEquals("输入"))) {
                    // 检查用户输入的字符是否为空格
                    val userInputChar = if (startOffset > 0 && startOffset <= editor.document.text.length) {
                        editor.document.text[startOffset - 1]
                    } else {
                        null
                    }
                    if (userInputChar == ' ' || userInputChar == '\t'){
                        LogUtil.info("completions", "Trigger codeEdits 1 request id ${editorRequest.requestId}, input event triggered, user input space or tab, trigger codeEdits", false)
                        // 触发codeEdits
                        codeEditsTrigger(editor, AntRequestTypeEnum.Automatic)
                    }
                }
                LOGGER.info("request id ${editorRequest.requestId}, lineInfo.lineSuffix is ${editorRequest.lineInfo.lineSuffix} containsNoLetterOrDigit, end")
                return
            }

            //先清理，将埋点上报后，再去组装新的KEY_LAST_REQUEST
            disposeInlays(editor, InlayDisposeContextEnum.Typing)
            //隐藏nextTab的提示
            NavigationTabService.getInstance().hide()

            AntEditorUtil.addEditorRequest(editor, editorRequest)
            // 注册到Editor的生命周期中，确保Editor被dispose时也会清理EditorRequestResultList
            KEY_LAST_REQUEST.get(editor)?.dispose()
            KEY_LAST_REQUEST.set(editor, EditorRequestResultList(editorRequest))
            editorRequest.languageStr = AntEditorUtil.getLanguage(editor)
            val delayTime: Int = if (requestType.isManual || requestType==AntRequestTypeEnum.MOCK) {
                0
            } else {
                DebounceTime.getDebounceTime(editor, LAST_IS_APPLY.get(), LAST_APPLY_TIME.get())
            }

            var runnable: Runnable? = null
            if (requestType.isManual) {
                runnable = Runnable{
                    //当是主动触发的请求，并且返回结果为空，这时触发nextTab请求
                    if (requestType.isManual && !editorRequest.isCancelled){
                        nextTabTrigger(editor, editorRequest.offset)
                    }
                }
            }
            queueCompletionRequest(
                editor, editorRequest, null, false, false, delayTime, runnable
            ) { first: AntInlayList? ->
                if (null != first) {
                    LogUtil.info("completions","request id ${editorRequest.requestId}, doInsertInlays", false)
                    editorRequest.runtimeData.beginRenderingTime = System.currentTimeMillis()
                    doInsertInlays(first, editorRequest, editor, true, InlayDisposeContextEnum.Typing)
                }
            }
        }
    }

    override fun codeEditsTrigger(editor: Editor, requestType: AntRequestTypeEnum) {
        LogUtil.info("codeEdits", "codeEditsTrigger ${requestType}", false)
        if (!antPreCheck(editor, requestType)){
            LogUtil.info("codeEdits","codeEditsTrigger antPreCheck", false)
            return
        }
        if (!agentService.isAgentFinishInit() || !agentService.isHeatBeating()){
            LogUtil.info("codeEdits","codeEditsTrigger agentService error", false)
            return
        }
        if (requestType.isAutomatic && !isTriggerCodeEdits(editor)){
            LogUtil.info("codeEdits","codeEditsTrigger is not Triggered rules", false)
            return
        }
        val startOffset = editor.caretModel.offset
        val lastRequest = KEY_LAST_CODEEDITS_REQUEST[editor]
        if (isDuplicateCodeEditsRequest(editor.document, startOffset, requestType, lastRequest)){
            LogUtil.info("codeEdits", "codeEditsTrigger duplication request. ${startOffset}", false)
        } else if (isCodeEditsLookupUnsupported(requestType, editor)) {
            LogUtil.info("codeEdits", "codeEditsTrigger isCodeEditsLookupUnsupported. ${startOffset}", false)
        } else {
            cancelCompletionRequests(editor)
            if (isBlacklistedCommand() || isUnsupportedEditorState(editor)){
                return
            }

            val editorRequest = antCompletionService.createRequest(editor, startOffset, CompletionTypeEnum.CodeEdits, requestType) ?: return
            LogUtil.info("codeEdits", "codeEditsTrigger request id ${editorRequest.requestId}, start", false)
            //先清理，将埋点上报后，再去组装新的KEY_LAST_REQUEST
            disposeCodeEditsInlays(editor, InlayDisposeContextEnum.UserAction)
            disposeNextTabInlays(editor, InlayDisposeContextEnum.UserAction)
            // 空行也不触发
            if (editorRequest.lineInfo.isBlankLine){
                LogUtil.info("codeEdits","codeEditsTrigger is not Triggered ${editorRequest.requestId} blank line", false)
                return
            }
            editorRequest.languageStr = AntEditorUtil.getLanguage(editor)
            AntEditorUtil.addEditorRequest(editor, editorRequest)
            // 注册到Editor的生命周期中，确保Editor被dispose时也会清理EditorRequestResultList
            KEY_LAST_CODEEDITS_REQUEST.get(editor)?.dispose()
            KEY_LAST_CODEEDITS_REQUEST.set(editor, EditorRequestResultList(editorRequest))
            CODEEDITES_REQUEST.set(true)
            isApplyCodeEdits.set(false)
            requestAlarm.cancelAllAndAddRequest(
                {
                    if (!editorRequest.isCancelled) {
                        inputTrackingService.flushCachedInputChangeData(editor)
                        requestCodeEdits(editor, startOffset, editorRequest, AppConstant.COMPLETION_CONFIG.editsTriggerDebounceTime) {
                                tripleResult: Triple<EditorRequest, StagedRange?, String>? ->
                            run {
                                if (tripleResult?.second == null && "showNextTab" == tripleResult?.third){
                                    //返回是空的，这时候去请求nextTab
                                    nextTabTrigger(editor, startOffset)
                                } else {
                                    editorRequest.runtimeData.beginRenderingTime = System.currentTimeMillis()
                                    LogUtil.info("codeEdits", "codeEditsTrigger CodeEdits request id ${editorRequest.requestId} ${editorRequest.isCancelled}, start showCodeEditsView", false)
                                    if (tripleResult != null && !tripleResult.first.isCancelled && tripleResult.second != null) {
                                        //显示之前，记录一下数据
                                        val isShowSuccess = showCodeEditsView(editor, tripleResult.second!!, tripleResult.third, tripleResult.first)
                                        LogUtil.info("codeEdits isShowSuccess ${tripleResult.first.requestId} ${isShowSuccess}")
                                        if (isShowSuccess && !tripleResult.first.isCancelled){
                                            lastCodeEditsShowData.set(tripleResult)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    CODEEDITES_REQUEST.set(false)
                },
                AppConstant.COMPLETION_CONFIG.editsTriggerDebounceTime
            )
            return
        }
    }

    private fun nextTabTrigger(editor: Editor, startOffset: Int){
        ApplicationManager.getApplication().invokeLater {
            LogUtil.info("codeEdits", "nextTabTrigger ${startOffset}", false)
            //1、检查请求是否重复
            val lastRequest = KEY_LAST_NEXTTAB_REQUEST[editor]
            if (isDuplicateCodeEditsRequest(editor.document, startOffset, AntRequestTypeEnum.Automatic, lastRequest)){
                LogUtil.info("codeEdits", "nextTabTrigger duplication request. ${startOffset}", false)
                return@invokeLater
            }
            //2、创建新请求
            val request = antCompletionService.createRequest(
                editor,
                startOffset,
                CompletionTypeEnum.CodeEdits,
                AntRequestTypeEnum.Automatic
            ) ?: return@invokeLater
            // 空行也不触发
            if (request.lineInfo.isBlankLine){
                LogUtil.info("codeEdits","nextTabTrigger is not Triggered ${request.requestId} blank line", false)
                return@invokeLater
            }
            request.languageStr = AntEditorUtil.getLanguage(editor)
            //3、把request加入到队列、缓存起来
            cancelCompletionRequests(editor)
            AntEditorUtil.addEditorRequest(editor, request)
            // 注册到Editor的生命周期中，确保Editor被dispose时也会清理EditorRequestResultList
            KEY_LAST_NEXTTAB_REQUEST.get(editor)?.dispose()
            KEY_LAST_NEXTTAB_REQUEST.set(editor, EditorRequestResultList(request))
            //4、放入nextTab线程池中，开始请求
            NEXT_TAB_REQUEST.set(true)
            nextTabRequestAlarm.cancelAllAndAddRequest({
                //5、将inputTracking中未发送的数据先发送给本地核心
                inputTrackingService.flushCachedInputChangeData(editor)
                //6、发起next tab请求
                codeEditsService.requestNextTab(
                    editor,
                    startOffset,
                    request,
                    0
                ) { nextTabTripleResult: Triple<EditorRequest, String?, Int> ->
                    run {
                        val lineNum: Int = nextTabTripleResult.third
                        if (lineNum > 0 && !request.isCancelled) {
                            ApplicationManager.getApplication().invokeLater {
                                //7、显示要跳转到的行号
                                val currentLine = editor.caretModel.logicalPosition.line
                                LogUtil.info("codeEdits", "nextTabTrigger show $lineNum", false)
                                if (CommonUtils.isValidLineNumber(currentLine, editor)){
                                    request.runtimeData.beginRenderingTime = System.currentTimeMillis()
                                    NavigationTabService.getInstance()
                                        .show(currentLine, editor, "Tab, to Line ${nextTabTripleResult.third}")
                                    if (!request.isCancelled) {
                                        lastNextTabShowData.set(nextTabTripleResult)
                                        // 开始预加载lineNumber对应行的codeEdits，这里是显示的linNumber，如果要用api去预加载，所以需要-1
                                        codeEditsTriggerWithPreload(editor, nextTabTripleResult.third-1, AntRequestTypeEnum.PreCodeEdits)
                                    }
                                } else {
                                    LogUtil.info("codeEdits", "nextTabTrigger show $lineNum invalid line", false)
                                }
                            }
                        }
                    }
                }
                NEXT_TAB_REQUEST.set(false)
            }, 0)
        }
    }

    /**
     * 支持预加载的codeEditsTrigger，用于预先获取指定行号的codeEdits数据
     * @param editor 编辑器
     * @param targetLineNumber 目标行号
     * @param requestType 请求类型
     */
    override fun codeEditsTriggerWithPreload(editor: Editor, targetLineNumber: Int, requestType: AntRequestTypeEnum) {
        LogUtil.info("codeEdits", "codeEditsTriggerWithPreload targetLineNumber: $targetLineNumber, requestType: $requestType", false)

        //检查project
        val myProject = editor.project
        if (myProject == null){
            return
        }

        if (!agentService.isAgentFinishInit() || !agentService.isHeatBeating()){
            LogUtil.info("codeEdits","codeEditsTriggerWithPreload agentService error", false)
            return
        }

        if (isBlacklistedCommand() || isUnsupportedEditorState(editor)){
            return
        }

        // 重新计算目标行的上下文和前后缀
        val targetOffset = editor.document.getLineEndOffset(targetLineNumber)
        val document = editor.document

        // 取消之前的请求
        cancelCompletionRequests(editor)
        disposeCodeEditsInlays(editor, InlayDisposeContextEnum.UserAction)
        // 使用当前光标位置创建EditorRequest，避免TextRange错误
        val editorRequest = antCompletionService.createRequest(editor, targetOffset, CompletionTypeEnum.CodeEdits, requestType) ?: return
        // 空行也不触发
        val targetLineStartOffset = document.getLineStartOffset(targetLineNumber)
        val targetLineEndOffset = document.getLineEndOffset(targetLineNumber)
        val targetLineText = document.getText(TextRange(targetLineStartOffset, targetLineEndOffset))
        if (targetLineText.trim().isEmpty()){
            LogUtil.info("codeEdits","codeEditsTriggerWithPreload target line is blank, not triggered", false)
            return
        }

        // 设置预加载状态
        isPreloadDataReady.set(false)
        isApplyLastNextTab.set(false)
        isApplyCodeEdits.set(false)
        preloadedCodeEditsData.set(null)

        editorRequest.languageStr = AntEditorUtil.getLanguage(editor)
        AntEditorUtil.addEditorRequest(editor, editorRequest)
        // 注册到Editor的生命周期中，确保Editor被dispose时也会清理EditorRequestResultList
        KEY_LAST_CODEEDITS_REQUEST.get(editor)?.dispose()
        KEY_LAST_CODEEDITS_REQUEST.set(editor, EditorRequestResultList(editorRequest))
        CODEEDITES_REQUEST.set(true)
        requestAlarm.cancelAllAndAddRequest(
            {
                if (!editorRequest.isCancelled) {
                    inputTrackingService.flushCachedInputChangeData(editor)
                    requestCodeEditsForPreload(editor, targetOffset, editorRequest, 0) {
                        tripleResult: Triple<EditorRequest, StagedRange?, String>? ->
                        run {
                            // 只检查请求是否被取消
                            if (!editorRequest.isCancelled) {
                                if (tripleResult != null && !tripleResult.first.isCancelled && !TextUtils.isEmpty(tripleResult.third)) {
                                    val lineRange = CommonUtils.getCodeEditsContextForLine(editor, targetLineNumber,AppConstant.COMPLETION_CONFIG.editsCursorBefore, AppConstant.COMPLETION_CONFIG.editsCursorAfter)
                                    val sourceText = CommonUtils.getLinesFromDocument(editor.document, lineRange.start, lineRange.end)
                                    val stageRange = StagedRange(lineRange, sourceText, tripleResult.third)

                                    val finalTriple = Triple(tripleResult.first, stageRange, tripleResult.third)
                                    LogUtil.info("codeEdits", "preload data ready for line: $targetLineNumber, requestId: ${editorRequest.requestId}", false)
                                    preloadedCodeEditsData.set(finalTriple)
                                    isPreloadDataReady.set(true)
                                    
                                    // 根据开关决定是否预加载成功后立即显示CodeEdits页面
                                    if (!editorRequest.isCancelled && isApplyLastNextTab.get()){
                                        ApplicationManager.getApplication().invokeLater {
                                            displayPreloadedData(editor)
                                        }
                                    }
                                } else {
                                    LogUtil.info("codeEdits", "preload failed for line: $targetLineNumber, requestId: ${editorRequest.requestId}", false)
                                    clearPreloadData()
                                }
                            }
                        }
                    }
                }
                CODEEDITES_REQUEST.set(false)
            },
            0
        )
    }

    /**
     * 专门用于预加载的requestCodeEdits方法
     */
    private fun requestCodeEditsForPreload(editor: Editor, startOffset: Int, codeEditsRequest: EditorRequest, delayTime: Int, onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?) {
        if (codeEditsRequest.isCancelled || editor.isDisposed || editor.project == null) {
            return
        }
        FileDocumentManager.getInstance().getFile(editor.document)?.path?.let {
            codeEditsService.requestPreCodeEdits(
                project = editor.project!!,
                fileUrl = it,
                contextText = editor.document.text,
                startOffset = startOffset,
                request = codeEditsRequest,
                delayTime = delayTime,
                onFirstCompletion = onFirstCompletion
            )
        }
    }

    /**
     * 显示预加载的数据
     */
    private fun displayPreloadedData(editor: Editor) {
        val preloadData = preloadedCodeEditsData.get()
        if (preloadData != null && isPreloadDataReady.get()) {
            LogUtil.info("codeEdits", "displaying preloaded data for request: ${preloadData.first.requestId}", false)
            
            preloadData.first.runtimeData.beginRenderingTime = System.currentTimeMillis()
            val isShowSuccess = showCodeEditsView(editor, preloadData.second!!, preloadData.third, preloadData.first)
            LogUtil.info("codeEdits", "preload display success: $isShowSuccess, requestId: ${preloadData.first.requestId}", false)
            
            if (isShowSuccess && !preloadData.first.isCancelled) {
                lastCodeEditsShowData.set(preloadData)
            }
            
            // 清理预加载数据
            clearPreloadData()
        }
    }

    /**
     * 清理预加载数据
     */
    private fun clearPreloadData() {
        preloadedCodeEditsData.set(null)
        isPreloadDataReady.set(false)
    }

    /**
     * 将所有的请求封装到 Alarm 队列内
     *
     * @param editor            Editor
     * @param contentRequest    EditorRequest
     * @param maxCompletions    最大请求次数，预留参数
     * @param enableCaching     是否使用缓存，预留参数
     * @param cycling           是否循环，预留参数
     * @param onFirstCompletion 执行完成后消费者执行的内容
     */
    private fun queueCompletionRequest(
        editor: Editor,
        contentRequest: EditorRequest,
        maxCompletions: Int?,
        enableCaching: Boolean,
        cycling: Boolean,
        delayTime: Int,
        runnable: Runnable? = null,
        onFirstCompletion: Consumer<AntInlayList?>?
    ) {

        requestAlarm.cancelAllAndAddRequest(
            {
                if (!contentRequest.isCancelled) {
                    requestCompletions(
                        editor,
                        contentRequest,
                        maxCompletions,
                        enableCaching,
                        cycling,
                        onFirstCompletion,
                        delayTime,
                        runnable
                    )
                }
            },
            delayTime
        )
    }

    private fun requestCodeEdits(editor: Editor, startOffset: Int, codeEditsRequest: EditorRequest, delayTime: Int,onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?){
        codeEditsService.requestCodeEdits(editor, startOffset, codeEditsRequest, delayTime, onFirstCompletion)
    }

    private fun showCodeEditsView(editor: Editor,
                                  stagedRange: StagedRange,
                                  completionText: String,
                                  request: EditorRequest): Boolean{
        return codeEditsService.showCodeEditsView(editor, stagedRange, completionText, request, Runnable{
            //补全快捷键 alt + \，结果被弃用，触发next tab
            if (request.requestType == AntRequestTypeEnum.Manual && !isApplyCodeEdits.get() && !editor.isDisposed){
                nextTabTrigger(editor, editor.caretModel.offset)
            }
        })
    }

    override fun getLastShowCodeEditsLineNum(editor: Editor): Int {
        return if (lastCodeEditsShowData.get() != null){
            lastCodeEditsShowData.get()!!.first.lineInfo.lineNumber
        } else {
            -1
        }
    }

    override fun isCodeEditsViewShowing(): Boolean {
        return codeEditsService.isCodeEditsViewShowing()
    }

    override fun isCodeEditsViewRequesting(): Boolean {
        return CODEEDITES_REQUEST.get()
    }

    override fun isNextTabViewShowing(): Boolean {
        return NavigationTabService.getInstance().isShowing()
    }

    override fun isNextTabViewRequesting(): Boolean {
        return NEXT_TAB_REQUEST.get()
    }

    /**
     * 真正发送请求的地方
     * 发送流程
     * 1、发送之前需取消对应编辑器之前的所有请求
     * 2、发送使用Flow请求，建议的生产者和消费者
     *
     * @param editor            Editor
     * @param request           EditorRequest
     * @param maxCompletions    最大请求次数，预留参数
     * @param enableCaching     是否使用缓存，预留参数
     * @param cycling           是否循环，预留参数
     * @param onFirstCompletion 执行完成后消费者执行的内容
     */
    @RequiresBackgroundThread
    private fun requestCompletions(
        editor: Editor,
        request: EditorRequest,
        maxCompletions: Int?,
        enableCaching: Boolean,
        cycling: Boolean,
        onFirstCompletion: Consumer<AntInlayList?>?,
        delayTime: Int,
        runnable: Runnable? = null
    ) {
        if (!AntEditorUtil.isSelectedEditor(editor)) {
            return
        }
        antCompletionService.completions(
            request,
            maxCompletions,
            enableCaching,
            cycling,
            editor,
            delayTime,
            runnable,
            object : Flow.Subscriber<CompletionInlayModel?> {
                @Volatile
                private var subscription: Flow.Subscription? = null
                override fun onSubscribe(subscription: Flow.Subscription) {
                    subscription.also { this.subscription = it }.request(1L)
                    val disposable = request.disposable
                    val subscription2 = this.subscription
                    Objects.requireNonNull(subscription2)
                    Disposer.tryRegister(disposable!!) { subscription2!!.cancel() }
                }

                override fun onNext(item: CompletionInlayModel?) {
                    LogUtil.info("completions","request id :${request.requestId} onNext requestCompletions", false)
                    if (null == item) {
                        return
                    }

                    if (CollUtil.isNotEmpty(item.antInlayLists) && null != item.antInlayLists!![0]) {
                        // 执行添加内嵌代码的业务逻辑
                        ApplicationManager.getApplication().invokeLater {
                            // 在UI线程去做后置判断
                            if (AppConstant.LAST_REQUEST_TIME.get() != item.requestTime) {
                                LOGGER.info("onNext 渲染时发现新请求, 补全请求被过滤:${request.lineInfo.linePrefix}")
                                return@invokeLater
                            }

                            if (request !is BasicEditorRequest || null == editor.project || editor.isDisposed) {
                                LOGGER.info("onNext request != BasicEditorRequest 或者 没找到project, 补全请求被过滤:${request.requestId}")
                                return@invokeLater
                            }

                            if (request.isCancelled) {
                                LOGGER.info("onNext 渲染时发现请求被cancel，被过滤:${request.requestId}")
                                return@invokeLater
                            }

                            var stored = KEY_LAST_REQUEST[editor]
                            // 如果没有stored或者stored已有数据，需要创建新的EditorRequestResultList
                            if (stored == null || stored.getSize() > 0) {
                                // 先dispose旧的对象
                                stored?.dispose()
                                // 创建新的EditorRequestResultList
                                stored = EditorRequestResultList(request)
                            }
                            
                            // 添加所有antInlayList到stored中
                            item.antInlayLists!!.forEach { antInlayList ->
                                stored.addInlays(antInlayList)
                            }
                            KEY_LAST_REQUEST.set(editor, stored)
                            KEY_LAST_CACHE_KEY.set(editor, item.cacheHashValue)
                            onFirstCompletion!!.accept(item.antInlayLists!![0])
                        }
                    }
                }

                override fun onError(throwable: Throwable) {
                    LOGGER.error("嵌入样式执行渲染失败 ", throwable)
                }

                override fun onComplete() {
                }
            })
    }

    /**
     * @param inlays                内嵌内容列表
     * @param request               EditorRequest
     * @param editor                Editor
     * @param disposeExistingInlays 是否取消内嵌的字体 保留字段
     * @param context               嵌入字体上下文
     */
    private fun renderInlaysNextLine(
        inlays: AntInlayList?, // 假设 AntInlayList 是一个可迭代的类型
        request: EditorRequest,
        editor: Editor) {

        if (inlays != null) {
            inlays.runtimeData = request.runtimeData.copy()
            inlays.lineSuffix = request.lineInfo.lineSuffix
            setLastInlayList(inlays)
        }

        val insertedInlays = java.util.ArrayList<Inlay<DefaultInlayRender>?>()
        val inlayModel = editor.inlayModel
        var index = 0

        //判断是否显示
        for (inlay in inlays!!) {
            if (inlay != null && !inlay.isEmptyCompletion) {
                val renderer = DefaultInlayRender(request, inlay.type, inlay.lines, false, "")
                var editorInlay: Inlay<DefaultInlayRender>? = null
                LogUtil.info("completions",String.format("render type:%s,offset:%s", inlay.type.name, inlay.editorOffset), false)
                when (inlay.type) {
                    AntCompletionTypeEnum.Inline -> editorInlay =
                        inlayModel.addInlineElement(inlay.editorOffset, true, Int.MAX_VALUE - index, renderer)

                    AntCompletionTypeEnum.AfterLineEnd -> editorInlay =
                        inlayModel.addAfterLineEndElement(inlay.editorOffset, true, renderer)

                    AntCompletionTypeEnum.Block -> editorInlay =
                        inlayModel.addBlockElement(inlay.editorOffset, true, false, Int.MAX_VALUE - index, renderer)

                    else -> {}
                }
                if (editorInlay != null) {
                    val finalEditorInlay: Inlay<DefaultInlayRender> = editorInlay
                    renderer.inlay = finalEditorInlay
                }
                insertedInlays.add(editorInlay)
                index++
            }
        }
    }

    /**
     * @param inlays                内嵌内容列表
     * @param request               EditorRequest
     * @param editor                Editor
     * @param disposeExistingInlays 是否取消内嵌的字体 保留字段
     * @param context               嵌入字体上下文
     */
    private fun doInsertInlays(
        inlays: AntInlayList?,
        request: EditorRequest,
        editor: Editor,
        disposeExistingInlays: Boolean,
        context: InlayDisposeContextEnum
    ) {
        if (disposeExistingInlays) {
            disposeInlays(editor, context)
        }

        val requestOffset = request.offset
        val currOffset = editor.caretModel.currentCaret.offset
        if (requestOffset != currOffset) {
            if (currOffset <= editor.document.text.length && requestOffset <= editor.document.text.length) {
                val substring = if (requestOffset <= currOffset){
                    editor.document.text.substring(requestOffset, currOffset)
                } else {
                    editor.document.text.substring(currOffset, requestOffset)
                }
                if (substring.trim().isNotBlank()) {
                    LOGGER.info(String.format("render check caret not match, cancel render .currOffset:%d,request:%d", currOffset, request.offset))
                    return
                }
            }
        }

        if (inlays != null) {
            inlays.runtimeData = request.runtimeData.copy()
            inlays.lineSuffix = request.lineInfo.lineSuffix
            setLastInlayList(inlays)
        }

        val insertedInlays = java.util.ArrayList<Inlay<DefaultInlayRender>?>()
        val inlayModel = editor.inlayModel
        var index = 0

        //判断是否显示
        var isShowTips = false
        val totalLineCount = inlays!!.getLineCount()
        val tips = CommonUtils.getTips(editor)
        val isAllInlineType = inlays.isAllInlineType()
        val inlayCount = inlays.getInlineCount()
        for (inlay in inlays) {
            if (inlay != null && !inlay.isEmptyCompletion) {
                if (totalLineCount > 1 && index == 0 && !inlays.isAcceptLine && !isAllInlineType && inlayCount == 1) {
                    isShowTips = true
                } else {
                    isShowTips = false
                }
                val renderer = DefaultInlayRender(request, inlay.type, inlay.lines, isShowTips, tips)
                var editorInlay: Inlay<DefaultInlayRender>? = null
                LogUtil.info("completions",String.format("render type:%s,offset:%s", inlay.type.name, inlay.editorOffset), false)
                when (inlay.type) {
                    AntCompletionTypeEnum.Inline -> editorInlay =
                        inlayModel.addInlineElement(inlay.editorOffset, true, Int.MAX_VALUE - index, renderer)

                    AntCompletionTypeEnum.AfterLineEnd -> editorInlay =
                        inlayModel.addAfterLineEndElement(inlay.editorOffset, true, renderer)

                    AntCompletionTypeEnum.Block -> editorInlay =
                        inlayModel.addBlockElement(inlay.editorOffset, true, false, Int.MAX_VALUE - index, renderer)

                    else -> {}
                }
                if (editorInlay != null) {
                    val finalEditorInlay: Inlay<DefaultInlayRender> = editorInlay
                    renderer.inlay = finalEditorInlay
                }
                insertedInlays.add(editorInlay)
                index++
            }
        }
    }

    /**
     * 应用当前编辑器推荐的样式
     *
     * @param editor Editor
     * @return Boolean
     */
    @RequiresEdt
    override fun applyCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean {
        if (editor.isDisposed) {
            LogUtil.info("antcomplete can't applyCompletion editor already disposed")
            return false
        }
        val project = editor.project
        if (project == null || project.isDisposed) {
            LogUtil.info("antcomplete can't applyCompletion project disposed or null")
            return false
        } else if (isProcessing(editor)) {
            LogUtil.info("can't apply inlays while processing")
            return false
        } else {
            // 采纳后 remove cachex
            val cacheHashKey = KEY_LAST_CACHE_KEY.get(editor, "")
            if (StringUtils.isNotBlank(cacheHashKey)){
                cacheService.remove(cacheHashKey)
                KEY_LAST_CACHE_KEY.set(editor, "")
            }

            if (applyInlayType == ApplyInlayType.WHOLE){
                disposeInlays(editor, InlayDisposeContextEnum.Applied)
                doApplyCompletion(project, editor)
            } else if (applyInlayType == ApplyInlayType.NEXT_LINE){
                disposeInlays(editor, InlayDisposeContextEnum.TypingAsSuggested)
                doApplyLineCompletion(project, editor)
            }
            return true
        }
    }

    /**
     * 应用推荐的代码块
     *
     * @param project Project
     * @param editor  Editor
     */
    @RequiresEdt
    private fun doApplyLineCompletion(project: Project, editor: Editor) {
        val inlayItem = getLastInlayList() ?: return
        val request = KEY_LAST_REQUEST[editor]?:return
        var isNeedAddSymbol = false
        if (inlayItem.replacementText.startsWith("\n") && !inlayItem.isAcceptLine) {
            isNeedAddSymbol = true
        }
        val line: String? = inlayItem.refactorByAcceptLine()
        if (StringUtils.isNotBlank(line)) {
            //获取插入内容,如果没有后缀，需要给lineCode前面加一个\n换行
            val codeLine = line?.let { this.getCompletionInsertContent(inlayItem, editor, it, isNeedAddSymbol) }
            //插入line内容，并上报埋点
            if (codeLine != null) {
                this.insertInlayContent(editor, codeLine)
                inlayItem.isAcceptLine = true
                submitTraceByLine(line)
            }
            //还有代码，显示剩余的代码内容
            if (inlayItem.getLineCount() > 0) {
                inlayItem.setEditorOffset(editor.caretModel.offset)
                this.renderInlaysNextLine(inlayItem, request.request, editor)
            }
            // 如果已经没有内容了，取消并上报埋点
            if (inlayItem.getLineCount() == 0){
                this.disposeInlays(editor, InlayDisposeContextEnum.Applied)
                submitTrace( true)
            }
        }
    }


    /**
     * 应用推荐的代码块
     *
     * @param project Project
     * @param editor  Editor
     */
    @RequiresEdt
    private fun doApplyCompletion(project: Project, editor: Editor) {
        val antInlayList = getLastInlayList() ?: return
        val range = antInlayList.replacementRange
        val text = antInlayList.replacementText
        submitTrace( true)
        text?.let {
            ACCEPT_COUNT.getAndAdd(text.length)
            NEW_CURRENT_FILE_COUNT.set(editor.document.textLength + text.length)

            if (CommonUtils.isShowAcceptTips()) {
                settingData.state.ACCEPTED_COUNT += 1
            }

            if (ACCEPT_COUNT.get() > 100) {
                val userTypeModel = UserTypeModel()
                userTypeModel.all = abs(NEW_CURRENT_FILE_COUNT.get() - LAST_UPLOD_FILE_COUNT.get())
                userTypeModel.accept = ACCEPT_COUNT.get()
                ACCEPT_COUNT.set(0)
                LAST_UPLOD_FILE_COUNT.set(NEW_CURRENT_FILE_COUNT.get())

                tracerService.submitUserCount(userTypeModel)
            }
        }


        WriteCommandAction.runWriteCommandAction(
            project,
            "Alipay AntCode",
            "Ant CodeIntelligence",
            {
                // 如果已经accept了，则直接插入
                if (antInlayList.isAcceptLine){
                    val codeText = text.let { this.getCompletionInsertContent(antInlayList, editor, it, false) }
                    insertInlayContent(editor, codeText)
                    setLastInlayList(null)
                    return@runWriteCommandAction
                }
                var isReplace: Boolean = false
                var replaceLength = 0
                try {
                    if (!TextUtils.isEmpty(antInlayList.lineSuffix) && AppConstant.COMPLETION_CONFIG.isEnableApplyProcess) {
                        val firstLine = text.lines().first()
                        val splitIndex = firstLine.indexOfLast { it.isLetterOrDigit() }
                        if (splitIndex != -1 && splitIndex < firstLine.length - 1) {
                            // 如果找到了字母或数字，并且该字符不是字符串的最后一个字符，则进行分割
                            val part1 = firstLine.substring(0, splitIndex + 1)
                            val part2 = firstLine.substring(splitIndex + 1)
                            val part2Str = part2.replace("\\s+".toRegex(), "")
                            val lineSuffixStr = antInlayList.lineSuffix.replace("\\s+".toRegex(), "")
                            LogUtil.info("part1 " + part1 + " part2 " + part2 + " lineSuffix " + antInlayList.lineSuffix, false)
                            // 包含判断时，要注意：去掉中间的空格和\t等再去判断
                            // 如果part2包含了后缀，并且后缀不是part2的最后一个字符，则进行替换
                            if (part2Str.isNotEmpty() &&
                                part2Str.contains(lineSuffixStr) &&
                                !part2Str.last().toString().contentEqualsNew(lineSuffixStr)) {
                                LogUtil.info("apply replace action", false)
                                isReplace = true
                                replaceLength = antInlayList.lineSuffix.length
                            }
                        }
                    }
                } catch (e: Exception) {
                    LogUtil.error("doApplyCompletion error", e)
                    isReplace = false
                    replaceLength = 0
                }

                val document = editor.document
                val currDocumentTextLength = document.textLength
                if (range.endOffset + replaceLength <= currDocumentTextLength) {
                    LogUtil.info("isReplace ${isReplace}, replaceLength ${replaceLength}", false)
                    if (isReplace) {
                        document.replaceString(range.startOffset, range.endOffset + replaceLength, text)
                    } else {
                        document.replaceString(range.startOffset, range.endOffset, text)
                    }
                    editor.caretModel.moveToOffset(range.startOffset + text.length)
                    editor.scrollingModel.scrollToCaret(ScrollType.MAKE_VISIBLE)
                }
                //触发codeEdits，判断text有几行
                if (!TextUtils.isEmpty(text) && CommonUtils.isCodeEditsEnable() && AppConstant.COMPLETION_CONFIG.enableApplyTriggerEdits){
                    val isSingleLine = !text.contains("\n")
                    if (isSingleLine){
                        //Tab键采纳后，触发codeEdits
                        LogUtil.info("completions", "Trigger codeEdits 2 tab accept", false)
                        codeEditsTrigger(editor,  AntRequestTypeEnum.Automatic)
                    }
                }
            })

        setLastInlayList(null)
    }

    override fun applyCodeEditsCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean {
        if (editor.isDisposed) {
            LogUtil.info("CodeEdits antcomplete can't applyCompletion editor already disposed")
            return false
        }
        val project = editor.project
        if (project == null || project.isDisposed) {
            LogUtil.info("CodeEdits antcomplete can't applyCompletion project disposed or null")
            return false
        } else if (isProcessing(editor)) {
            LogUtil.info("CodeEdits can't apply inlays while processing")
            return false
        } else {
            isApplyCodeEdits.set(true)
            disposeCodeEditsInlays(editor, InlayDisposeContextEnum.Applied)
            doApplyCodeEditsLineCompletion(project, editor)
            nextTabTrigger(editor, editor.caretModel.offset)
            return true
        }
    }

    private fun doApplyCodeEditsLineCompletion(project: Project, editor: Editor) {
        val lastCodeEditsShowData = this.lastCodeEditsShowData.get() ?: return
        val startLine = lastCodeEditsShowData.second!!.sourceDocumentStartLine
        val endLine = lastCodeEditsShowData.second!!.sourceDocumentEndLine
        submitCodeEditsTrace(true, editor)
        WriteCommandAction.runWriteCommandAction(
            project,
            "Alipay AntCode",
            "Ant CodeIntelligence",
            {
                // 验证行号范围是否有效
                if (startLine < 0 || endLine >= editor.document.lineCount || startLine > endLine) {
                    LogUtil.info("CodeEdits doApplyCodeEditsLineCompletion lineNumber error")
                    return@runWriteCommandAction
                }
                // 获取 startLine 的起始位置
                val startOffset = editor.document.getLineStartOffset(startLine)
                // 获取 endLine 的结束位置
                val endOffset = editor.document.getLineEndOffset(endLine)
                val range = TextRange(startOffset, endOffset)
                val text = lastCodeEditsShowData.second!!.completionText

                try {
                    val document = editor.document
                    val currDocumentTextLength = document.textLength
                    if (range.endOffset <= currDocumentTextLength) {
                        document.replaceString(range.startOffset, range.endOffset, text)
                    }
                    editor.caretModel.moveToOffset(range.startOffset + text.length)
                    editor.scrollingModel.scrollToCaret(ScrollType.MAKE_VISIBLE)
                } catch (e: Exception) {
                    LogUtil.error("CodeEdits doApplyCodeEditsLineCompletion error", e)
                }
                //上报CodeEdits采纳埋点
                //submitTrace( true)
            })
    }

    /**
     * 光标跳转到对应行的末尾。行号存在行号存在lastNextTabShowData
     */
    override fun applyNextTab(
        editor: Editor,
        applyInlayType: ApplyInlayType
    ): Boolean {
        if (editor.isDisposed) {
            LogUtil.info("applyNextTab can't applyCompletion editor already disposed")
            return false
        }
        val project = editor.project
        if (project == null || project.isDisposed) {
            LogUtil.info("applyNextTab can't applyCompletion project disposed or null")
            return false
        } else if (isProcessing(editor)) {
            LogUtil.info("applyNextTab can't apply inlays while processing")
            return false
        } else {
            LogUtil.info("applyNextTab .....")
            //1、跳转到对应行的行尾
            lastNextTabShowData.get()?.let {
                val lineNumber = it.third
                if (lineNumber > 0) {
                    //2、隐藏并埋点
                    isApplyLastNextTab.set(true)
                    disposeNextTabInlays(editor, InlayDisposeContextEnum.Applied)
                    ApplicationManager.getApplication().invokeLater {
                        // 如果开关为false且有预加载数据，则在用户点击nextTab采纳后显示预加载的数据
                        val runnable = Runnable {
                            if (isPreloadDataReady.get() && isApplyLastNextTab.get()) {
                                displayPreloadedData(editor)
                            }
                        }
                        NavigationTabService.getInstance().gotoLine(editor, lineNumber, runnable)
                    }
                    LogUtil.info("codeEdits", "applyNextTab: jumped to line $lineNumber", false)
                }
            }


            return true
        }
    }

    @RequiresEdt
    override fun disposeInlays(editor: Editor, disposeContext: InlayDisposeContextEnum) {
        if (!isAvailable(editor) || isProcessing(editor)) {
            return
        }
        val request = KEY_LAST_REQUEST.get(editor)?:return
        if (disposeContext.isSendRejectedTelemetry) {
            sendRejectedTelemetry(request)
        }
        val antInlayList = request.getCurrentCompletion()
        if (disposeContext.isResetLastRequest) {
            KEY_LAST_REQUEST.get(editor)?.dispose()
            KEY_LAST_REQUEST.set(editor, null)
        }

        if (request.request.offset != editor.caretModel.offset) {
            cancelCompletionRequests(editor)
        }
        this.wrapProcessing(editor,
            Runnable {
                if (disposeContext.isCycling || disposeContext.isApply || disposeContext.isTypingAsSuggested) {
                    //clear directly 不上传埋点
                    this.doDisposeInlaysWithOutSubmitTrace(
                        collectInlays(editor, 0, editor.document.textLength),
                        editor.project
                    )
                } else {
                    doDisposeInlays(editor,collectInlays(editor, 0, editor.document.textLength), antInlayList, editor.project)
                }
            })
    }

    override fun disposeCodeEditsInlays(editor: Editor, disposeContext: InlayDisposeContextEnum) {
        if (!isAvailable(editor) || isProcessing(editor)) {
            return
        }
        val lastCodeEditsRequest = KEY_LAST_CODEEDITS_REQUEST.get(editor)?:return
        if (disposeContext.isResetLastRequest) {
            KEY_LAST_CODEEDITS_REQUEST.set(editor, null)
        }

        this.wrapProcessing(editor,
            Runnable {
                //显示中，并且不是采纳时，上报埋点
                if (!disposeContext.isApply){
                    submitCodeEditsTrace(false, editor)
                }
                codeEditsService.closeCodeEditsView(editor, disposeContext)
            }
        )

        CODEEDITES_REQUEST.set(false)
    }

    override fun disposeNextTabInlays(editor: Editor, disposeContext: InlayDisposeContextEnum) {
        if (!isAvailable(editor) || isProcessing(editor)) {
            return
        }
        KEY_LAST_NEXTTAB_REQUEST.get(editor) ?: return
        KEY_LAST_NEXTTAB_REQUEST.set(editor, null)

        this.wrapProcessing(
            editor,
            Runnable {
                //显示中，并且不是采纳时，上报埋点
                if (disposeContext.isApply) {
                    submitNextTabTrace(true)
                } else {
                    submitNextTabTrace(false)
                }
                NavigationTabService.getInstance().hide()
            }
        )
        NEXT_TAB_REQUEST.set(false)
    }

    /**
     * 清除之前的嵌入字体，并取消内容，并发送埋点信息
     *
     * @param renderers AntDefaultInlayRender
     */
    private fun doDisposeInlays(editor: Editor, renderers: List<DefaultInlayRender>, antInlayList: AntInlayList?, project: Project?) {
        doDisposeInlaysWithOutSubmitTrace(renderers, project)
        submitTrace(false, editor)
    }

    /**
     * 清除之前的嵌入字体，并取消内容，不发送埋点信息
     *
     * @param renderers AntDefaultInlayRender
     */
    private fun doDisposeInlaysWithOutSubmitTrace(renderers: List<DefaultInlayRender>, project: Project?) {
        for (renderer in renderers) {
            val inlay = renderer.inlay
            if (inlay != null) {
                Disposer.dispose(inlay)
            }
        }
    }

    /**
     * 统计当前的内嵌样式个数
     *
     * @param editor                   当前的编辑器
     * @param searchRange              选中的代码块
     * @param inlineInlays             行内样式
     * @param afterLineEndInlays       行后样式
     * @param blockInlays              代码块样式
     * @param matchInLeadingWhitespace 是否匹配前面的空格
     * @return int
     */
    override fun countCompletionInlays(
        editor: Editor,
        searchRange: TextRange,
        inlineInlays: Boolean,
        afterLineEndInlays: Boolean,
        blockInlays: Boolean,
        matchInLeadingWhitespace: Boolean
    ): Int {
        return if (!isAvailable(editor)) {
            0
        } else {
            val startOffset = searchRange.startOffset
            val endOffset = searchRange.endOffset
            val inlayModel = editor.inlayModel
            var totalCount = 0
            if (inlineInlays) {
                totalCount += inlayModel.getInlineElementsInRange(startOffset, endOffset).stream()
                    .filter { inlay: Inlay<*> ->
                        if (inlay.renderer !is DefaultInlayRender) {
                            return@filter false
                        } else if (matchInLeadingWhitespace) {
                            return@filter true
                        } else {
                            val lines: List<String?> =
                                (inlay.renderer as DefaultInlayRender).contentLines
                            if (lines.isEmpty()) {
                                return@filter false
                            } else {
                                if (lines[0] != null && lines[0]!!.isNotEmpty()) {
                                    val whitespaceEnd: Int =
                                        inlay.offset + AntEditorUtil.leadingWhitespaceLength(lines[0]!!)
                                    return@filter searchRange.endOffset >= whitespaceEnd
                                }
                                return@filter false
                            }
                        }
                    }.count().toInt()
            }
            if (blockInlays) {
                totalCount += inlayModel.getBlockElementsInRange(startOffset, endOffset).stream()
                    .filter { inlay: Inlay<*> -> inlay.renderer is DefaultInlayRender }
                    .count().toInt()
            }
            if (afterLineEndInlays) {
                totalCount += inlayModel.getAfterLineEndElementsInRange(startOffset, endOffset).stream()
                        .filter { inlay: Inlay<*> -> inlay.renderer is DefaultInlayRender }
                        .count().toInt()
            }
            totalCount
        }
    }

    /**
     * 取消之前的请求
     *
     * @param editor Editor
     */
    override fun cancelCompletionRequests(editor: Editor) {
        requestAlarm.cancelAllRequests()
        nextTabRequestAlarm.cancelAllRequests()
        val requests = AntEditorUtil.KEY_REQUESTS[editor]
        if (requests != null && !requests.isEmpty()) {
            for (request in requests) {
                request.cancel()
            }
            requests.clear()
        }
        CODEEDITES_REQUEST.set(false)
        NEXT_TAB_REQUEST.set(false)
        
        // 清理预加载数据
        clearPreloadData()
    }

    /**
     * 是否有当前的内嵌样式
     *
     * @param editor Editor
     * @return Boolean
     */
    override fun hasCompletionInlays(editor: Editor): Boolean {
        return countCompletionInlays(editor, TextRange.from(0, editor.document.textLength), true, true, true, true) > 0
    }

    /**
     * 判断是否还有推荐的结果集
     *
     * @param editor Editor
     * @return Boolean
     */
    override fun hasPreviousInlaySet(editor: Editor): Boolean? {
        val request = KEY_LAST_REQUEST[editor]?:return false
        return request.hasPrev()
    }

    /**
     * 显示上一个推荐的结果集
     *
     * @param editor Editor
     */
    override fun showPreviousInlaySet(editor: Editor) {
        val request = KEY_LAST_REQUEST[editor] ?: return
        val set = request.prevCompletion ?: return
        doInsertInlays(set, request.request, editor, true, InlayDisposeContextEnum.Cycling)
        submitUserChangeAdviseCodeType(editor.project,"pre", editor)
    }

    /**
     * 判断是否还有下一个推荐结果
     *
     * @param editor Editor
     * @return Boolean
     */
    override fun hasNextInlaySet(editor: Editor): Boolean? {
        val request = KEY_LAST_REQUEST[editor]?:return false
        return request.hasNext()
    }

    /**
     * 显示下一个推荐的结果集
     *
     * @param editor Editor
     */
    override fun showNextInlaySet(editor: Editor) {
        val request = KEY_LAST_REQUEST[editor]?:return
        val set = request.nextCompletion?:return
        doInsertInlays(set, request.request, editor, true, InlayDisposeContextEnum.Cycling)
        submitUserChangeAdviseCodeType(editor.project,"next", editor)
    }

    /**
     * 判断最后一次请求是否已经结束
     *
     * @return
     */
    override fun isEndLastRequest(): Boolean {
        return (requestAlarm.requestNum <= 0)
    }

    /**
     * 检查是否刚刚应用了NextTab，用于避免在visibleAreaChanged中误销毁预加载的codeEditsView
     *
     * @return Boolean
     */
    override fun isRecentlyAppliedNextTab(): Boolean {
        return isApplyLastNextTab.get()
    }

    /**
     * @param lastRequest EditorRequestResultList
     */
    private fun sendRejectedTelemetry(lastRequest: EditorRequestResultList?) {
    }

    /**
     * 取消当前的列表弹窗
     */
    private fun cancelList() {
        val currentCompletion = CompletionServiceImpl.getCurrentCompletionProgressIndicator()
        currentCompletion?.closeAndFinish(true)
    }

    /**
     * 判断是否是重复的请求
     *
     * @param document      Document
     * @param requestOffset requestOffset
     * @param requestType   AntRequestType
     * @param lastRequest   EditorRequestResultList
     * @return boolean
     */
    private fun isDuplicateRequest(
        document: Document,
        requestOffset: Int,
        requestType: AntRequestTypeEnum,
        lastRequest: EditorRequestResultList?
    ): Boolean {
        return null != lastRequest && !requestType.isForcedOrManual && lastRequest.request.offset == requestOffset && lastRequest.request.documentModificationSequence == AntEditorUtil.getDocumentModificationStamp(
            document
        )
    }

    /**
     * 判断是否是重复的请求
     *
     * @param document      Document
     * @param requestOffset requestOffset
     * @param requestType   AntRequestType
     * @param lastRequest   EditorRequestResultList
     * @return boolean
     */
    private fun isDuplicateCodeEditsRequest(
        document: Document,
        requestOffset: Int,
        requestType: AntRequestTypeEnum,
        lastRequest: EditorRequestResultList?
    ): Boolean {
        val currentLineNum = document.getLineNumber(requestOffset)
        return null != lastRequest && currentLineNum == lastRequest.request.lineInfo.lineNumber && lastRequest.request.documentModificationSequence == AntEditorUtil.getDocumentModificationStamp(
            document
        ) && !requestType.isForcedOrManual
    }

    /**
     * 判断当前是否有弹框出现
     *
     * @param requestType AntRequestType
     * @param editor      Editor
     * @return boolean
     */
    private fun isLookupUnsupported(requestType: AntRequestTypeEnum, editor: Editor): Boolean {
        return requestType.isAutomaticOrForced && LookupManager.getActiveLookup(editor) != null
    }

    private fun isCodeEditsLookupUnsupported(requestType: AntRequestTypeEnum, editor: Editor): Boolean {
        return requestType.isAutomaticOrForced && LookupManager.getActiveLookup(editor) != null
    }

    /**
     * 编辑器是否有效
     */
    override fun isAvailable(editor: Editor): Boolean {
        var isAvailable = KEY_EDITOR_SUPPORTED!![editor]
        if (isAvailable == null) {
            isAvailable = (editor !is EditorWindow)
                    && (editor !is ImaginaryEditor)
                    && (editor !is EditorEx || !editor.isEmbeddedIntoDialogWrapper)
                    && !editor.isViewer
                    && !editor.isOneLineMode
                    && editor.getEditorKind() == EditorKind.MAIN_EDITOR;
            KEY_EDITOR_SUPPORTED[editor] = isAvailable
        }
        return isAvailable && !editor.isDisposed
    }


    private fun wrapProcessing(editor: Editor, block: Runnable) {
        try {
            KEY_PROCESSING.set(editor, true)
            block.run()
        } finally {
            KEY_PROCESSING.set(editor, null)
        }
    }

    /**
     * 初始化请求代码块
     */
    private fun submitTrace(isApply: Boolean, editor: Editor? = null) {
        //不管是生效还是取消操作，最后肯定要走到这一步
        val lastInlayList = getLastInlayList()
        lastInlayList ?: return
        val lastCompletionRTObject = getLastCompletionRTObject()
        if (lastCompletionRTObject != null) {
            val timeStamp = System.currentTimeMillis()
            lastCompletionRTObject.endRenderingTime = timeStamp
            if (isApply) {
                lastCompletionRTObject.acceptTime = timeStamp
                LAST_IS_APPLY.set(true)
                LAST_APPLY_TIME.set(timeStamp)
                lastInlayList.contextId?.takeUnless { it.contentEqualsNew("null") }?.let { validContextId ->
                    lastCompletionRTObject.acceptCodeId = validContextId.toLong()
                    tracerService.submitRuntimeEvent(lastCompletionRTObject, "completion")
                }
            } else {
                LAST_IS_APPLY.set(false)
                LAST_APPLY_TIME.set(timeStamp)
                tracerService.submitRuntimeEvent(lastCompletionRTObject, "completion")
            }
        }

        setLastInlayList(null)

        //补全快捷键 alt + \，结果被弃用，触发next tab
        if (lastInlayList.completionsModel.editorRequest?.requestType == AntRequestTypeEnum.Manual && !isApply && editor != null && !editor.isDisposed){
            nextTabTrigger(editor, editor.caretModel.offset)
        }
    }


    /**
     * 初始化请求代码块
     */
    private fun submitTraceByLine(code: String) {
        //不管是生效还是取消操作，最后肯定要走到这一步
        val lastInlayList = getLastInlayList()
        lastInlayList ?: return
        val lastCompletionRTObject = getLastCompletionRTLineCodeObject(code)
        if (lastCompletionRTObject != null) {
            val timeStamp = System.currentTimeMillis()
            LAST_IS_APPLY.set(true)
            LAST_APPLY_TIME.set(timeStamp)
            lastCompletionRTObject.endRenderingTime = timeStamp
            lastCompletionRTObject.acceptTime = timeStamp
            lastInlayList.contextId?.takeUnless { it.contentEqualsNew("null") }?.let { validContextId ->
                lastCompletionRTObject.acceptCodeId = validContextId.toLong()
                tracerService.submitRuntimeLineCodeEvent(lastCompletionRTObject)
            }
        }
    }

    private fun submitCodeEditsTrace(isApply: Boolean, editor: Editor){
        //不管是采纳还是取消操作，最后肯定要走到这一步
        val lastCodeEditsData = this.lastCodeEditsShowData.get()
        lastCodeEditsData ?: return
        val lastCodeEditsRTObject = getLastCodeEditsRTObject()
        if (lastCodeEditsRTObject != null) {
            val timeStamp = System.currentTimeMillis()
            lastCodeEditsRTObject.endRenderingTime = timeStamp
            if (isApply) {
                lastCodeEditsRTObject.acceptTime = timeStamp
                LAST_IS_APPLY.set(true)
                LAST_APPLY_TIME.set(timeStamp)
                tracerService.submitRuntimeEvent(lastCodeEditsRTObject, "CodeEdits")
            } else {
                recordCancelCount(editor)
                LAST_IS_APPLY.set(false)
                LAST_APPLY_TIME.set(timeStamp)
                tracerService.submitRuntimeEvent(lastCodeEditsRTObject, "CodeEdits")
            }
        }
        this.lastCodeEditsShowData.set(null)
    }

    private fun submitNextTabTrace(isApply: Boolean){
        //不管是采纳还是取消操作，最后肯定要走到这一步
        val lastNextTabData = this.lastNextTabShowData.get()
        lastNextTabData ?: return
        val lastNextTabRTObject = getLastNextTabRTObject()
        if (lastNextTabRTObject != null) {
            val timeStamp = System.currentTimeMillis()
            lastNextTabRTObject.endRenderingTime = timeStamp
            if (isApply) {
                lastNextTabRTObject.acceptTime = timeStamp
                LAST_IS_APPLY.set(true)
                LAST_APPLY_TIME.set(timeStamp)
                tracerService.submitRuntimeEvent(lastNextTabRTObject, "NextTab")
            } else {
                LAST_IS_APPLY.set(false)
                LAST_APPLY_TIME.set(timeStamp)
                tracerService.submitRuntimeEvent(lastNextTabRTObject, "NextTab")
            }
        }
        this.lastNextTabShowData.set(null)
    }


    /**
     * 收集当前的所有的嵌入式字体
     *
     * @param editor      Editor
     * @param startOffset 开始的光标位置
     * @param endOffset   结束的光标位置
     * @return list
     */
    @RequiresEdt
    fun collectInlays(editor: Editor, startOffset: Int, endOffset: Int): List<DefaultInlayRender> {
        val model = editor.inlayModel
        val inlays = ArrayList<Inlay<*>>()
        inlays.addAll(model.getInlineElementsInRange(startOffset, endOffset))
        inlays.addAll(model.getAfterLineEndElementsInRange(startOffset, endOffset))
        inlays.addAll(model.getBlockElementsInRange(startOffset, endOffset))
        val renderers = ArrayList<DefaultInlayRender>()
        for (inlay in inlays) {
            if (inlay.renderer is DefaultInlayRender) {
                renderers.add(inlay.renderer as DefaultInlayRender)
            }
        }
        return renderers
    }


    /**
     * 清理数据
     */
    override fun dispose() {
        // 清理预加载数据
        clearPreloadData()
    }

    private fun isProcessing(editor: Editor): Boolean {
        if (editor == null) {
            return false
        }
        return KEY_PROCESSING.get(editor)
    }

    /**
     * 是否为开启补全开关
     * 1、主动补全不受开关影响
     * 2、自动补全需要看开关
     */
    private fun isEnableCompletion(antRequestTypeEnum: AntRequestTypeEnum): Boolean {
        if (antRequestTypeEnum.isManual) {
            return true
        }

        return settingData.state.codegenSwitch
    }

    /**
     * 检查登录状态
     */
    override fun checkLogin(): Boolean {
        val permission = localUserStore.getUserInfoModel()?.permissionsGpu
        permission?.let {
            if (permission == PermissionsEnum.ACCESS.name) {
                return true
            }
        }


        return false
    }

    private fun antPreCheck(editor: Editor, requestType: AntRequestTypeEnum): Boolean{
        //检查开关
        if (!isEnableCompletion(requestType)){
            LogUtil.info("isEnableAutoCompletion false")
            return false
        }

        //检查登录
        if (!checkLogin()){
            LogUtil.info("permission deny")
            if (requestType.isManual){
                editor.project?.let { NotificationUtils.showBeginLoginMessage(it) }
            }
            return false
        }

        //检查project
        val myProject = editor.project
        if (myProject == null){
            return false
        }

        return true
    }

    /**
     * 当页面切换时的监听
     */
    override fun onChangePage(oldFileName: String?, oldFileDocCount: Int, newFileName: String?, newFileDocCount: Int) {
        LOGGER.debug("onChangePage " + oldFileName + " " + oldFileDocCount + " " + newFileName + " " + newFileDocCount)

        if (ACCEPT_COUNT.get() > 0){
            val userTypeModel = UserTypeModel()
            userTypeModel.all = abs(NEW_CURRENT_FILE_COUNT.get() - LAST_UPLOD_FILE_COUNT.get())
            userTypeModel.accept = ACCEPT_COUNT.get()
            tracerService.submitUserCount(userTypeModel)
        }

        LAST_UPLOD_FILE_COUNT.set(newFileDocCount)
        NEW_CURRENT_FILE_COUNT.set(newFileDocCount)
        ACCEPT_COUNT.set(0)
    }

    /**
     * 提交用户改变建议的代码类型
     */
    private fun submitUserChangeAdviseCodeType(project: Project?, action: String, editor: Editor) {
        // 创建一个JSONObject对象用于存储数据
        val data = JSONObject()
        // 将当前会话ID添加到data中
        data["sessionId"] = getCurrentSessionId()
        data["action"] = action
        // 调用tracerService的submitTypeAndData方法
        if (project != null){
            tracerService.submitTypeAndData(project, TraceTypeEnum.USER_CHANGE_ADVISE_CODE, data)
        }
    }

    private fun isUnsupportedEditorState(editor: Editor): Boolean {
        return if (editor.caretModel.caretCount > 1) true else editor.selectionModel.hasSelection()
    }

    private val COMMAND_BLACKLIST = setOf("Expand Live Template by Tab")
    private fun isBlacklistedCommand(): Boolean {
        val commandName = CommandProcessor.getInstance().currentCommandName
        return commandName != null && COMMAND_BLACKLIST.contains(
            commandName
        )
    }


    @Synchronized
    private fun setLastInlayList(antInlayList: AntInlayList?){
        this.lastInlayList.set(antInlayList)
    }


    @Synchronized
    private fun getLastInlayList():AntInlayList?{
        return this.lastInlayList.get()
    }

    @Synchronized
    private fun getCurrentSessionId(): String {
        val antInlayList = this.lastInlayList.get() ?: return ""
        return antInlayList.runtimeData?.sessionId ?: return ""
    }

    @Synchronized
    private fun getLastCompletionRTObject():CompletionRtModel? {
        val antInlayList = this.lastInlayList.get() ?: return null
        val completionRtModel = antInlayList.runtimeData
        if (completionRtModel != null){
            return completionRtModel.copy()
        }
        return null
    }

    @Synchronized
    private fun getLastCompletionRTLineCodeObject(code:String):CompletionRtModel? {
        val antInlayList = this.lastInlayList.get() ?: return null
        val completionRtModel = antInlayList.runtimeData
        if (completionRtModel != null){
            return completionRtModel.copyLineObject(code)
        }
        return null
    }

    @Synchronized
    private fun getLastCodeEditsRTObject():CompletionRtModel? {
        val codeEditsShowData = this.lastCodeEditsShowData.get() ?: return null
        val codeEditsRTData = codeEditsShowData.first.runtimeData
        if (codeEditsRTData != null){
            return codeEditsRTData.copy()
        }
        return null
    }

    @Synchronized
    private fun getLastNextTabRTObject():CompletionRtModel? {
        val lastNextTabShowData = this.lastNextTabShowData.get() ?: return null
        val nextTabRTData = lastNextTabShowData.first.runtimeData
        if (nextTabRTData != null){
            return nextTabRTData.copy()
        }
        return null
    }

    private fun getCompletionInsertContent(inlayItem: AntInlayList, editor: Editor, line: String, isNeedAddSymbol: Boolean): String {
        var content = line
        if (isNeedAddSymbol && !content.startsWith(AppConstant.ENTER_SYMBOL)) {
            content = "\n$content"
            return content
        }

        if (inlayItem.isAcceptLine && !content.startsWith(AppConstant.ENTER_SYMBOL)) {
            val linePrefix: String = getCursorPrefix(editor, editor.caretModel.offset)
            val lineSuffix: String = getCursorSuffix(editor, editor.caretModel.offset)
            if (StringUtils.isNotBlank(linePrefix) && StringUtils.isBlank(lineSuffix)) {
                content = "\n$content"
            }
        }
        return content
    }

    fun getCursorPrefix(editor: Editor, cursorPosition: Int): String {
        val document = editor.document
        val lineNumber = document.getLineNumber(cursorPosition)
        val lineStart = document.getLineStartOffset(lineNumber)
        return document.getText(TextRange.create(lineStart, cursorPosition)).trim { it <= ' ' }
    }

    fun getCursorSuffix(editor: Editor, cursorPosition: Int): String {
        val document = editor.document
        val lineNumber = document.getLineNumber(cursorPosition)
        val lineEnd = document.getLineEndOffset(lineNumber)
        return document.getText(TextRange.create(cursorPosition, lineEnd)).trim { it <= ' ' }
    }

    private fun insertInlayContent(editor: Editor, content: String?) {
        if (content != null){
            WriteCommandAction.runWriteCommandAction(
                editor.project,
                "Alipay AntCode NEXT_LINE",
                "Ant CodeIntelligence",
                {
                    val code: String = content
                    val code2 = StringUtils.stripEnd(code, " \t\n")
                    val caretOffset = code2.length
                    val startOffset = editor.caretModel.offset
                    val document: Document = editor.document
                    document.insertString(startOffset, code2)
                    editor.caretModel.moveToOffset(editor.caretModel.offset + caretOffset)
                    editor.scrollingModel.scrollToCaret(ScrollType.MAKE_VISIBLE)
                }
            )
        }
    }

    @Synchronized
    private fun isTriggerCodeEdits(editor: Editor) : Boolean {
        val key = getVirtualFilePath(editor)
        if (TextUtils.isEmpty(key)){
            LogUtil.info("codeEdits isTriggerCodeEdits 1 key is null")
            return false
        }

        // 1、是否编辑过，判断距离上次文件的编辑时机是否已经超过了60s，超过了不触发
        val lastModificationTime = recordFileTimeStamp.get(key) ?: return false
        val timeDiff = System.currentTimeMillis() - lastModificationTime
//        LogUtil.info("codeEdits isTriggerCodeEdits timeDiff ${timeDiff} lastModificationTime ${lastModificationTime}")
        if (timeDiff > AppConstant.COMPLETION_CONFIG.editsLastEditTimeDiff){
//            LogUtil.info("codeEdits isTriggerCodeEdits 2 lastModifyTime too long ${timeDiff}")
            return false
        }
        // 2、是否已经取消过5次了，超过5次不触发
        codeEditsCancelCountMap.get(key)?.let {
            if (it.second >= AppConstant.COMPLETION_CONFIG.editsCancelCount){
                LogUtil.info("codeEdits isTriggerCodeEdits 3 recordCancelCount ${it.second}")
                return false
            }
        }

        return true
    }

    @Synchronized
    private fun recordCancelCount(editor: Editor) {
        val key = getVirtualFilePath(editor)
        if (TextUtils.isEmpty(key)){
            return
        }
        if (codeEditsCancelCountMap.get(key) == null){
            codeEditsCancelCountMap.put(key, Pair(System.currentTimeMillis(), 1))
            return
        }
        codeEditsCancelCountMap.get(key)?.let {
            val currentCancelCount = it.second+1
            codeEditsCancelCountMap.put(key, Pair(System.currentTimeMillis(), currentCancelCount))
            LogUtil.info("codeEdits recordCancelCount false ${key} ${currentCancelCount}")
            return
        }
    }

    private fun getVirtualFilePath(editor: Editor) : String{
        try {
            if (FileDocumentManager.getInstance().getFile(editor.document) != null){
                return FileDocumentManager.getInstance().getFile(editor.document)!!.path
            }
        } catch (e: Throwable){
            LogUtil.info("getVirtualFilePath error")
        }
        return ""
    }

    override fun mockEditModifiedStart(editor: Editor) {
        editModified(editor, 0, AntRequestTypeEnum.MOCK, null)
    }

    override fun mockEditModifiedEnd(editor: Editor) {
        editModified(editor, editor.document.textLength, AntRequestTypeEnum.MOCK, null)
    }
}