package com.alipay.tsingyan.services.completion.edit.service

import com.alipay.tsingyan.services.completion.edit.model.AntLanguageEnum
import com.intellij.openapi.extensions.ExtensionPointName
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread
import com.intellij.util.concurrency.annotations.RequiresReadLock

interface LanguageSupport {
    fun isAvailable(psiFile: PsiFile): Boolean
    val fileType: FileType?
    val AntLanguageEnum: AntLanguageEnum

    @RequiresBackgroundThread
    @RequiresReadLock
    fun isEmptyBlockStart(project: Project, psiFile: PsiFile, i: Int): Bo<PERSON>an

    @RequiresBackgroundThread
    fun findBlockEnd(
        project: Project,
        cancellable: Cancellable,
        str: String,
        i: Int,
        str2: String,
        z: Boolean
    ): Int?

    val multiLineStops: Array<String>?
        get() = DEFAULT_MULTI_LINE_STOP
    val singleLineStops: Array<String>?
        get() = DEFAULT_SINGLE_LINE_STOP

    companion object {
        val EP = ExtensionPointName<LanguageSupport>("com.alipay.language")
        val DEFAULT_SINGLE_LINE_STOP = arrayOf("\n")
        val DEFAULT_MULTI_LINE_STOP = arrayOf("\n\n\n")
        fun find(file: PsiFile): LanguageSupport? {
            // TODo 声明出的扩展点，但我们这里是肯定支持的
            return EP.findFirstSafe { e: LanguageSupport -> e.isAvailable(file) }
        }
    }
}