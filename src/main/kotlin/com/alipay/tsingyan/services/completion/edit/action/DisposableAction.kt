package com.alipay.tsingyan.services.completion.edit.action

import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.intellij.application.options.CodeStyle
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorKind
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorActionHandler
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.util.TextRange
import java.awt.event.KeyEvent

class DisposableAction (defaultHandler: EditorActionHandler = DisposeActionHandler()) : EditorAction(defaultHandler),
    DumbAware {
    val editorManagerService = service<EditorManagerService>()

    override fun update(e: AnActionEvent) {
        e.presentation.isEnabled = isIgnoredKeyboardEvent(e)
    }

    /**
     * 判断是否要启动取消Action
     */
    private fun isIgnoredKeyboardEvent(e: AnActionEvent): Boolean {
        val myProject = e.project ?: return false
        val myEditor = getEditor(e.dataContext) ?: return false
        var caretOffsetAfterTab: Int? = null
        /**
         * 判断是否是主编辑器
         * 判断是否ESC对应的键盘事件 ESC对应的KeyCode为27
         */
        if (myEditor.editorKind != EditorKind.MAIN_EDITOR
            || e.inputEvent !is KeyEvent
            || (e.inputEvent as KeyEvent).keyCode != 27
        ) {
            return false
        }
        val document = myEditor.document
        val blockIndent = CodeStyle.getIndentOptions(myProject, document).INDENT_SIZE
        val caretOffset = myEditor.caretModel.offset
        val line = document.getLineNumber(caretOffset)
        if (
//            isNonEmptyLinePrefix(document, line, caretOffset) ||
            AntEditorUtil.indentLine(myProject, myEditor, line, blockIndent, caretOffset)
                .also { caretOffsetAfterTab = it } < caretOffset
        ) {
            return false
        }
        val tabRange = TextRange.create(caretOffset, caretOffsetAfterTab!!)
        val inlineChatService = e.project!!.service<InlineChatService>()
        return editorManagerService.countCompletionInlays(myEditor, tabRange, true, true, true, false) > 0
                || inlineChatService.isHasMarkupCode(myEditor)
                || editorManagerService.isNextTabViewShowing()

    }

    private fun isNonEmptyLinePrefix(document: Document, lineNumber: Int, caretOffset: Int): Boolean {
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        return lineStartOffset != caretOffset
                && !AntEditorUtil.isSpacesOrTabs(
            document.getText(
                TextRange.create(
                    lineStartOffset,
                    caretOffset
                )
            ),
            false
        )
    }

    /**
     * 取消用户操作的Handler
     */
    class DisposeActionHandler : EditorActionHandler() {
        val editorManagerService = service<EditorManagerService>()

        override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext): Boolean {
            return true
        }

        override fun executeInCommand(editor: Editor, dataContext: DataContext): Boolean {
            return false
        }

        override fun doExecute(editor: Editor, caret: Caret?, dataContext: DataContext) {
            val inlineChatService = editor.project!!.service<InlineChatService>()
            if (inlineChatService.isHasMarkupCode(editor)){
                inlineChatService.cleanMarkupCode(editor)
                inlineChatService.closeOptionView(editor)
            }
            editorManagerService.disposeInlays(editor, InlayDisposeContextEnum.UserAction)
            editorManagerService.disposeNextTabInlays(editor, InlayDisposeContextEnum.UserAction)
            super.doExecute(editor, caret, dataContext)
        }
    }
}
