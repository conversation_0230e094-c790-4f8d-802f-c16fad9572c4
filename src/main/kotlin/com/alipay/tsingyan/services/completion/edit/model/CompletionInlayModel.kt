package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.services.completion.edit.AntInlayList
import java.util.*

/**
 * 内嵌的补全模型
 */
class CompletionInlayModel {


    /**
     * 和触发请求时候的事件做对比,过滤无效请求
     *
     */
    var requestTime: Long = 0
    var antInlayLists: MutableList<AntInlayList>? = null
    var requestId: Long? = 0
    var cacheHashValue:String? = null


    fun addAntInlayList(antInlayList: AntInlayList?) {
        antInlayList ?: return
        if (null == antInlayLists) {
            antInlayLists = LinkedList()
        }
        antInlayLists!!.add(antInlayList)
    }
}