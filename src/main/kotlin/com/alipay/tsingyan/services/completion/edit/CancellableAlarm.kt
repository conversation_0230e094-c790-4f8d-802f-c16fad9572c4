package com.alipay.tsingyan.services.completion.edit

import com.intellij.openapi.Disposable
import com.intellij.util.Alarm
import org.jetbrains.annotations.TestOnly
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

/**
 * 此处为一个线程队列，允许用户取消之前添加的线程
 */
class CancellableAlarm (parentDisposable: Disposable) {
    private val LOCK: Any
    private val alarm: Alarm

    init {
        LOCK = Any()
        alarm = Alarm(Alarm.ThreadToUse.POOLED_THREAD, parentDisposable)
    }

    /**
     * 取消所有的请求
     */
    fun cancelAllRequests() {
        synchronized(LOCK) { alarm.cancelAllRequests() }
    }

    /**
     * 取消之前的所有请求，并添加新的请求进来
     *
     * @param request
     * @param delayMillis
     */
    fun cancelAllAndAddRequest(request: Runnable, delayMillis: Int) {
        synchronized(LOCK) {
            alarm.cancelAllRequests()
            alarm.addRequest(request, delayMillis)
        }
    }

    /**
     * 获取正在请求的个数
     */
    val requestNum: Int
        get() {
            synchronized(LOCK) { return alarm.activeRequestCount }
        }

    /**
     * 取消执行请求
     *
     * @param timeout
     * @param timeUnit
     * @throws ExecutionException
     * @throws InterruptedException
     * @throws TimeoutException
     */
    @TestOnly
    @Throws(ExecutionException::class, InterruptedException::class, TimeoutException::class)
    fun waitForAllExecuted(timeout: Int, timeUnit: TimeUnit?) {
        synchronized(LOCK) { alarm.waitForAllExecuted(timeout.toLong(), timeUnit!!) }
    }
}