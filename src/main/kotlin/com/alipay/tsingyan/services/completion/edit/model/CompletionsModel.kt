package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.model.completion.CodeModel


/**
 * 补全模型
 */
class CompletionsModel {
    /**
     * 原生的补全对象
     */
    var codeModelList: List<CodeModel>? = null

    /**
     * 原生的请求对象
     */
    var editorRequest: EditorRequest? = null



    constructor() {}

    /**
     * @param codeModel     补全结果
     * @param editorRequest 请求参数
     */
    constructor(codeModelList: List<CodeModel>, editorRequest: EditorRequest?) {
        this.codeModelList = codeModelList
        this.editorRequest = editorRequest
    }
}