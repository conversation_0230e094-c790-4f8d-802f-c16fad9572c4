package com.alipay.tsingyan.services.completion.edit

import com.alipay.tsingyan.utils.AppConstant
import com.intellij.ide.ui.AntialiasingType
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.editor.impl.FontInfo
import com.intellij.openapi.editor.markup.EffectType
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.util.Key
import com.intellij.ui.paint.EffectPainter2D
import com.intellij.util.ui.GraphicsUtil
import com.intellij.util.ui.UIUtil
import java.awt.Font
import java.awt.FontMetrics
import java.awt.Graphics
import java.awt.Graphics2D
import java.awt.font.FontRenderContext
import java.awt.geom.Rectangle2D
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.util.*

/**
 * 内嵌样式渲染
 */
object InlayRendering {
    private val KEY_CACHED_FONTMETRICS = Key.create<Map<Font, FontMetrics>>(AppConstant.EDITOR_FONT_METRICS_KEY)

    /**
     * 经查询源代码发现，在221之后的版本才会有这个
     */
    private val getEditorFontSize2DMethod: Method? = null

    /**
     * 计算宽度
     *
     * @param editor
     * @param text
     * @param textLines
     * @return
     */
    fun calculateWidth(editor: Editor, text: String, textLines: List<String?>): Int {
        val metrics = fontMetrics(editor, getFont(editor, text))
        var maxWidth = 0
        for (line in textLines) {
            maxWidth = Math.max(maxWidth, metrics.stringWidth(line))
        }
        return maxWidth
    }

    /**
     * 渲染代码块
     *
     * @param editor
     * @param content
     * @param contentLines
     * @param g
     * @param region
     * @param attributes
     */
    fun renderCodeBlock(
        editor: Editor,
        content: String,
        contentLines: List<String?>,
        g: Graphics,
        region: Rectangle2D,
        attributes: TextAttributes
    ) {
        if (!(content.isEmpty() || contentLines.isEmpty())) {
            val clipBounds = g.clipBounds
            val g2 = g.create() as Graphics2D
            GraphicsUtil.setupAAPainting(g2)
            val font = getFont(editor, content)
            g2.font = font
            val metrics = fontMetrics(editor, font)
            val lineHeight = editor.lineHeight.toDouble()
            val fontBaseline = Math.ceil(font.createGlyphVector(metrics.fontRenderContext, "Alb").visualBounds.height)
            val linePadding = (lineHeight - fontBaseline) / 2.0
            val offsetX = region.x
            val offsetY = region.y + fontBaseline + linePadding
            var lineOffset = 0
            g2.clip = if (clipBounds == null || clipBounds == region) region else region.createIntersection(clipBounds)
            for (line in contentLines) {
                renderBackground(g2, attributes, offsetX, region.y + lineOffset.toDouble(), region.width, lineHeight)
                g2.color = attributes.foregroundColor
                g2.drawString(line, offsetX.toFloat(), (offsetY + lineOffset.toDouble()).toFloat())
                if (editor is EditorImpl) {
                    renderEffects(
                        g2, offsetX, offsetY + lineOffset.toDouble(), metrics.stringWidth(line).toDouble(),
                        editor.charHeight, editor.descent, attributes, font
                    )
                }
                lineOffset = (lineOffset.toDouble() + lineHeight).toInt()
            }
            g2.dispose()
        }
    }

    /**
     * 获取当前的字体
     *
     * @param editor
     * @param text
     * @return
     */
    private fun getFont(editor: Editor, text: String): Font {
        return UIUtil
            .getFontWithFallbackIfNeeded(editor.colorsScheme.getFont(EditorFontType.PLAIN).deriveFont(2), text)
            .deriveFont(fontSize(editor))
    }

    /**
     * 度量字体
     *
     * @param editor
     * @param font
     * @return
     */
    private fun fontMetrics(editor: Editor, font: Font): FontMetrics {
        val editorContext = FontInfo.getFontRenderContext(editor.contentComponent)
        val context = FontRenderContext(
            editorContext.transform, AntialiasingType.getKeyForCurrentScope(false),
            editorContext.fractionalMetricsHint
        )
        val cachedMap: Map<Font, FontMetrics> = KEY_CACHED_FONTMETRICS[editor, emptyMap()] as Map<Font, FontMetrics>
        var fontMetrics = cachedMap[font]
        if (fontMetrics == null || !context.equals(fontMetrics.fontRenderContext)) {
            fontMetrics = FontInfo.getFontMetrics(font, context)
            KEY_CACHED_FONTMETRICS[editor] = merge(
                cachedMap,
                mapOf(font to fontMetrics)
            )
        }
        return fontMetrics
    }

    /**
     * 后台渲染
     *
     * @param g
     * @param attributes
     * @param x
     * @param y
     * @param width
     * @param height
     */
    private fun renderBackground(
        g: Graphics2D, attributes: TextAttributes, x: Double, y: Double, width: Double,
        height: Double
    ) {
        val color = attributes.backgroundColor
        if (color != null) {
            g.color = color
            g.fillRoundRect(x.toInt(), y.toInt(), width.toInt(), height.toInt(), 1, 1)
        }
    }

    private fun renderEffects(
        g: Graphics2D, x: Double, baseline: Double, width: Double, charHeight: Int, descent: Int,
        textAttributes: TextAttributes, font: Font?
    ) {
        var effectType = textAttributes.effectType
        val effectColor = textAttributes.effectColor
        if (effectColor != null && effectType != null) {
            g.color = effectColor
            when (effectType) {
                EffectType.LINE_UNDERSCORE -> {
                    EffectPainter2D.LINE_UNDERSCORE.paint(g, x, baseline, width, descent.toDouble(), font)
                    return
                }
                EffectType.BOLD_LINE_UNDERSCORE -> {
                    EffectPainter2D.BOLD_LINE_UNDERSCORE.paint(g, x, baseline, width, descent.toDouble(), font)
                    return
                }
                EffectType.STRIKEOUT -> {
                    EffectPainter2D.STRIKE_THROUGH.paint(g, x, baseline, width, charHeight.toDouble(), font)
                    return
                }
                EffectType.WAVE_UNDERSCORE -> {
                    EffectPainter2D.WAVE_UNDERSCORE.paint(g, x, baseline, width, descent.toDouble(), font)
                    return
                }
                EffectType.BOLD_DOTTED_LINE -> {
                    EffectPainter2D.BOLD_DOTTED_UNDERSCORE.paint(g, x, baseline, width, descent.toDouble(), font)
                    return
                }
                EffectType.BOXED -> return
                else -> return
            }
        }
    }

    fun fontSize(editor: Editor): Float {
        val scheme = editor.colorsScheme
        if (getEditorFontSize2DMethod != null) {
            try {
                return (getEditorFontSize2DMethod.invoke(scheme, *arrayOfNulls(0)) as Float).toFloat()
            } catch (e: IllegalAccessException) {
            } catch (e: InvocationTargetException) {
            }
        }
        return scheme.editorFontSize.toFloat()
    }

    /**
     * 合并数据
     *
     * @param maps
     * @param <K>
     * @param <V>
     * @return
    </V></K> */
    @SafeVarargs
    fun <K, V> merge(vararg maps: Map<K, V>): Map<K, V> {
        if (maps.size == 0) {
            return emptyMap()
        }
        if (maps.size == 1) {
            return java.util.Map.copyOf(maps[0])
        }
        var all: MutableMap<K, V>? = null
        for (map in maps) {
            if (!map.isEmpty()) {
                if (all == null) {
                    all = HashMap()
                }
                all.putAll(map)
            }
        }
        return if (all == null) emptyMap() else Collections.unmodifiableMap(all)
    }
}