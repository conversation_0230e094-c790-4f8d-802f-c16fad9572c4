package com.alipay.tsingyan.services.completion.edit.service.impl

import cn.hutool.core.collection.ListUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.completion.CompletionService
import com.alipay.tsingyan.model.completion.CodeModel
import com.alipay.tsingyan.model.completion.CompletionRequestBean
import com.alipay.tsingyan.model.completion.CompletionResultModel
import com.alipay.tsingyan.prompt.service.OriginalFileFactory
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.AntInlayList
import com.alipay.tsingyan.services.completion.edit.DefaultAntCompletion
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.*
import com.alipay.tsingyan.services.completion.edit.service.AntCompletionService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.newcompletion.cache.CacheService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.CommonUtils.settingData
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.view.status.CodeFuseStatus
import com.alipay.tsingyan.view.status.CodeFuseStatusService
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import org.apache.commons.lang3.StringUtils
import org.apache.http.util.TextUtils
import java.util.concurrent.Flow
import java.util.concurrent.SubmissionPublisher


/**
 * 代码补全实现（内嵌样式）
 */
class AntCompletionServiceImpl : AntCompletionService {
    private val LOGGER: Logger = Logger.getInstance(AntCompletionServiceImpl::class.java)


    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    val completionService: CompletionService = service<CompletionService>()
    val localUserStore: LocalUserStore = service<LocalUserStore>()
    val tracerService = service<CodeFuseTracerService>()
    val cacheService = service<CacheService>()

    /**
     * 创建请求
     */
    override fun createRequest(editor: Editor, offset: Int, completionType: CompletionTypeEnum, antRequestTypeEnum: AntRequestTypeEnum): EditorRequest? {
        return AntEditorUtil.createEditorRequest(editor, offset, completionType, antRequestTypeEnum)
    }


    /**
     * 执行发送请求的操作
     *
     * @param request        编辑器请求
     * @param maxCompletions 最大请求次数
     * @param enableCaching  是否启用缓存
     * @param cycling        没看明白
     * @param subscriber     处理完之后的事件订阅器
     * @return 是否嵌入完成
     */
    override fun completions(
        request: EditorRequest,
        maxCompletions: Int?,
        enableCaching: Boolean,
        cycling: Boolean,
        editor: Editor,
        delayTime: Int,
        runnable: Runnable?,
        subscriber: Flow.Subscriber<CompletionInlayModel?>
    ): Boolean {
        try {
            if (request.requestTimestamp != AppConstant.LAST_REQUEST_TIME.get()) {
                LOGGER.info("ant completions cancel 有发起新请求,补全请求被过滤:$${request.lineInfo.linePrefix}")
                return false
            }

            if (request !is BasicEditorRequest || null == editor.project) {
                LOGGER.info("ant completions cancel request != BasicEditorRequest 或者 没找到project, 补全请求被过滤:$${request.lineInfo.linePrefix}")
                return false
            }

            if (request.isCancelled) {
                LOGGER.info("ant completions cancel 补全请求被cancel:$${request.lineInfo.linePrefix}")
                return false
            }

            request.runtimeData.beginRequestTime = System.currentTimeMillis()

            //1、当光标变化后,如果有内容改变就过滤本次请求
            if (editor.document.lineCount <=  request.lineInfo.lineNumber){
                LOGGER.info("ant completions cancel 补全请求被cancel, 请求的lineNumber大于当前文本行数")
                return false
            }
            val realLineEndOffset = editor.document.getLineEndOffset(request.lineInfo.lineNumber)
            val lineEndOffset = request.lineInfo.lineEndOffset
            if (lineEndOffset != realLineEndOffset) {
                if (realLineEndOffset <= editor.document.text.length) {
                    val substring = if (lineEndOffset <= realLineEndOffset){
                        editor.document.text.substring(lineEndOffset, realLineEndOffset)
                    } else {
                        if (lineEndOffset > editor.document.text.length){
                            LOGGER.info("ant completions cancel 不是有效补全触发条件, 补全请求被过滤: lineEndOffset>length请求时位置比现在所有长度还小")
                            return false
                        }
                        editor.document.text.substring(realLineEndOffset, lineEndOffset)
                    }
                    if (substring.trim().isNotBlank()) {
                        LOGGER.info("ant completions cancel 不是有效补全触发条件, 补全请求被过滤: lineEndOffset != realLineEndOffset $lineEndOffset $realLineEndOffset substring:$substring")
                        return false
                    }
                }
            }

            //2、光标没有变化,检查后缀，后缀包含数字和字母，直接过滤
            val lineSuffix = request.lineInfo.lineSuffix
            if (request.requestType != AntRequestTypeEnum.MOCK){
                if (lineSuffix != null && !StringUtils.isBlank(lineSuffix.trim()) && AntEditorUtil.containsNoLetterOrDigit(lineSuffix.trim())) {
                    LOGGER.info("ant completions cancel 不是有效补全触发条件, 补全请求被过滤: containsNoLetterOrDigit ${lineSuffix.trim()}")
                    return false
                }
            }


            var prompt: String = ""
            var suffix: String = ""
            var requestOffset = request.offset
            val contextText = editor.document.text
            if (contextText.isNotEmpty()) {
                if (requestOffset > contextText.length) {
                    requestOffset = contextText.length
                }
                prompt = contextText.substring(0, requestOffset)
                suffix = contextText.substring(requestOffset)
            }
            //3、处理上文,去掉无用空格,截断参数
//            prompt = CodeUtils.cleanCommonAndBlank(prompt)
            val lineArr = prompt.split(AppConstant.ENTER_SYMBOL)
            val linePrefix = if (lineArr.isNotEmpty()) lineArr.last() else ""
            //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
            if (lineArr.size > AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize) {
                prompt =
                    lineArr.subList(
                        lineArr.size - AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize,
                        lineArr.size
                    ).joinToString(AppConstant.ENTER_SYMBOL)
            }

            //4、处理下文,去掉无用空格,截断参数
//            suffix = CodeUtils.cleanCommonAndBlank(suffix)
            val lastlineArr = suffix.split(AppConstant.ENTER_SYMBOL)
            //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
            if (lastlineArr.size > AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize) {
                suffix = lastlineArr.subList(
                    lastlineArr.size - AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize,
                    lastlineArr.size
                ).joinToString(AppConstant.ENTER_SYMBOL)
            }

//            // 滑块相似度计算
//            var similarSnippets:JSONArray? = null
//            if (AppConstant.COMPLETION_CONFIG.openSlidingWindows && !CommonUtils.isCloudIDE){
//                val startTime = System.currentTimeMillis()
//                if (lineArr.size > OriginalFileFactory.linesPerBlock){
//                    val windowContent = lineArr.subList(
//                        lineArr.size -  OriginalFileFactory.linesPerBlock,
//                        lineArr.size
//                    ).joinToString(AppConstant.ENTER_SYMBOL)
//                    val encode = OriginalFileFactory.enc?.encode(windowContent)
//                    similarSnippets = OriginalFileFactory.calculateTopKResult(request.project, encode)
//                }
//                LOGGER.info("similarSnippets cost time ${System.currentTimeMillis()-startTime} window size :${OriginalFileFactory.calacCount}")
//                //LOGGER.info("similarSnippets $similarSnippets")
//            }

            CodeFuseStatusService.notifyApplication(CodeFuseStatus.CompletionInProgress)

            //5、发送请求
            val completionRequestBean = CompletionRequestBean()
            completionRequestBean.language = request.languageStr
            completionRequestBean.prompt = prompt
            completionRequestBean.suffix = suffix
            completionRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
            completionRequestBean.ideVersion = CommonUtils.getIdeVersion()
            completionRequestBean.userToken = CommonUtils.getUserToken(localUserStore, tsingYanProdService)
            completionRequestBean.fileUrl = FileDocumentManager.getInstance().getFile(editor.document)?.path
            completionRequestBean.recordInfo["debounceTime"] = delayTime?.toString() ?: "0"
            completionRequestBean.recordInfo["preCommitId"] = ProjectCache.getCommitId(editor.project!!)
            completionRequestBean.recordInfo["branch"] = CommonUtils.getBranch(editor.project!!)
            editor.project?.let { completionRequestBean.repo = ProjectCache.getGitData(it) }
//            completionRequestBean.similarSnippets = similarSnippets
            if (request.requestType == AntRequestTypeEnum.Manual){
                completionRequestBean.skipFilter = true
            }
            completionRequestBean.projectUrl = request.project.basePath ?: ""
            completionRequestBean.openFixSafety = settingData.state.codegenSecureSwitch

            request.runtimeData.beginAlgTime = System.currentTimeMillis()
            LogUtil.debug("request id : ${request.requestId}, request network 前缀 :${request.lineInfo.linePrefix} , 补全请求: ${
                JSONObject.toJSONString(
                    completionRequestBean
                )
            }")

            val completionResultPair: Pair<String?, CompletionResultModel?> = cacheService.getCache(completionRequestBean, linePrefix, lineSuffix)
            var completionResultModel = completionResultPair.second
            var cacheHashStr = completionResultPair.first
            LogUtil.info("completions", " ${request.requestId} get cache ${completionResultModel} ${request.requestType}", false)
            if (completionResultModel == null || TextUtils.isEmpty(cacheHashStr) || request.requestType == AntRequestTypeEnum.Manual || request.requestType == AntRequestTypeEnum.MOCK){
                request.runtimeData.status = 0
                // 发起请求
                completionResultModel = completionService.completion(completionRequestBean)
                cacheHashStr = cacheService.cache(completionRequestBean, completionResultModel, linePrefix)
                LogUtil.info("completions",
                    "request id : ${request.requestId}, request network 前缀 :${request.lineInfo.linePrefix} , 补全结果: ${
                        JSONObject.toJSONString(
                            completionResultModel
                        )
                    }, save Cache cacheHashStr ${cacheHashStr}", false
                )
            } else {
                request.runtimeData.status = 1
                LogUtil.info("completions",
                    "request id : ${request.requestId}, use Cache 前缀 :${request.lineInfo.linePrefix} , 补全结果: ${
                        JSONObject.toJSONString(
                            completionResultModel
                        )
                    }, cacheHashStr ${cacheHashStr}", false
                )
            }

            val codeModelResult = completionResultModel?.codeModelList
            val sessionId = completionResultModel?.sessionId
            if (completionResultModel == null || codeModelResult.isNullOrEmpty()) {
                CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:0")
                //返回是空的时候，调用此回调
                if (completionResultModel != null && codeModelResult != null && codeModelResult.isEmpty()){
                    runnable?.run()
                }
                return false
            }
            request.runtimeData.sessionId = sessionId
            request.runtimeData.endAlgTime = System.currentTimeMillis()
            request.runtimeData.completionNum = codeModelResult.size
            CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:" + codeModelResult.size)

            val result = CompletionInlayModel()
            result.requestTime = request.requestTimestamp
            result.requestId = request.requestId
            result.cacheHashValue = cacheHashStr
            val completionsModel = CompletionsModel(codeModelResult, request)
            codeModelResult
                .stream()
                .forEach { it: CodeModel ->
                    val stringList =
                        ListUtil.of(
                            *it.content!!.split("\n".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                        )
                    val inlayList: AntInlayList? = AntEditorUtil
                        .createEditorCompletion(
                            completionsModel,
                            DefaultAntCompletion(stringList),
                            false,
                            it.id.toString()
                        )
                    result.addAntInlayList(inlayList)
                }
            //6、发送结果
            if (request.requestType == AntRequestTypeEnum.MOCK){
                return false
            }
            val publisher: SubmissionPublisher<CompletionInlayModel> = SubmissionPublisher<CompletionInlayModel>()
            return try {
                publisher.subscribe(subscriber)
                publisher.submit(result)
                publisher.close()
                false
            } catch (th: Throwable) {
                try {
                    publisher.close()
                } catch (th2: Throwable) {
                    th.addSuppressed(th2)
                }
                LOGGER.error("completion error", th)
                return false
            }
        } catch (exception: Throwable) {
            LOGGER.error("completion error", exception)
            tracerService.submitException(exception)
            return false
        }
    }
}