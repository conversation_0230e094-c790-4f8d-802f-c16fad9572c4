package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.services.completion.edit.AntInlayList
import com.alipay.tsingyan.services.completion.edit.LogUtil
import it.unimi.dsi.fastutil.objects.ObjectLinkedOpenHashSet
import it.unimi.dsi.fastutil.objects.ObjectSortedSet
import com.intellij.openapi.Disposable

/**
 * 编辑器请求结果列表
 * 
 * <AUTHOR>
 * @date 2024-01-09
 */

class EditorRequestResultList(
    /**
     * 代码补全的请求
     */
    val request: EditorRequest
) : Disposable {
    /**
     * 获取请求
     *
     * @return
     */

    /**
     * 内嵌代码块的锁
     */
    private val inlayLock: Any

    /**
     * 内嵌的结果列表
     */
    private val inlayLists: ObjectLinkedOpenHashSet<AntInlayList?>

    /**
     * 展示索引
     */
    private var index = 0

    /**
     * 最大展示索引
     */
    private var maxShownIndex = 0
    private val hasOnDemandCompletions = false

    /**
     * 标记是否已经被disposed
     */
    @Volatile
    private var disposed = false

    /**
     * 构造参数
     *
     * @param request
     */
    init {
        inlayLock = Any()
        inlayLists = ObjectLinkedOpenHashSet()
    }

    /**
     * 判断是否还有推荐的结果集
     *
     * @return
     */
    fun hasPrev(): Boolean {
        if (disposed) return false
        var z: Boolean
        synchronized(inlayLock) { z = inlayLists.size > 1 }
        return z
    }

    val prevCompletion: AntInlayList?
        get() {
            if (disposed) return null
            synchronized(inlayLock) {
                val size = inlayLists.size
                if (size <= 1) {
                    index = 0
                    return null
                }
                index--
                if (index < 0) {
                    index = size - 1
                }
                return getAtIndexLocked(inlayLists, index)
            }
        }

    fun getCurrentCompletion(): AntInlayList? {
        if (disposed) return null
        synchronized(inlayLock) {
            return getAtIndexLocked(this.inlayLists, this.index)
        }
    }

    /***
     * 是否是按需完成
     * @return
     */
    fun hasOnDemandCompletions(): Boolean {
        if (disposed) return false
        var z: Boolean
        synchronized(inlayLock) { z = hasOnDemandCompletions || inlayLists.size > 1 }
        return z
    }

    /**
     * 是否有下一个推荐的结果集
     *
     * @return
     */
    fun hasNext(): Boolean {
        if (disposed) return false
        var z: Boolean
        synchronized(inlayLock) { z = inlayLists.size > 1 }
        return z
    }

    /**
     * 获取到下一个推荐的结果集
     *
     * @return
     */
    val nextCompletion: AntInlayList?
        get() {
            if (disposed) return null
            synchronized(inlayLock) {
                val size = inlayLists.size
                if (size <= 1) {
                    index = 0
                    return null
                }
                index++
                if (index >= size) {
                    index = 0
                }
                maxShownIndex = Math.max(maxShownIndex, index)
                return getAtIndexLocked(inlayLists, index)
            }
        }

    /**
     * 添加内嵌的样式
     *
     * @param inlays
     */
    fun addInlays(inlays: AntInlayList) {
        if (disposed) return
        synchronized(inlayLock) {
            inlayLists.add(inlays)
            maxShownIndex = Math.max(0, maxShownIndex)
        }
    }

    fun getSize() :Int{
        if (disposed) return 0
        synchronized(inlayLock){
            return inlayLists.size
        }
    }

    override fun dispose() {
//        LogUtil.info("editorList dispose.....")
        disposed = true
        synchronized(inlayLock) {
            inlayLists.clear()
        }
        // 如果request也实现了Disposable，确保它也被dispose
        if (request is Disposable && !request.isCancelled) {
            request.cancel()
        }
    }

    companion object {
        private fun getAtIndexLocked(inlays: ObjectSortedSet<AntInlayList?>, index: Int): AntInlayList? {
            return inlays.stream().skip(index.toLong()).findFirst().orElse(null)
        }
    }
}