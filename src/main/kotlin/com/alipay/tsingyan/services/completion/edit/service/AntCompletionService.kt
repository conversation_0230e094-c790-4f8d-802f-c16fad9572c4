package com.alipay.tsingyan.services.completion.edit.service

import com.alipay.tsingyan.services.completion.edit.model.AntRequestTypeEnum
import com.alipay.tsingyan.services.completion.edit.model.CompletionInlayModel
import com.alipay.tsingyan.services.completion.edit.model.CompletionTypeEnum
import com.alipay.tsingyan.services.completion.edit.model.EditorRequest
import com.intellij.openapi.editor.Editor
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread
import java.util.concurrent.Flow

/**
 *  补全请求接口，用于内嵌样式
 */
interface AntCompletionService {
    /**
     * 创建请求
     *
     * @param editor
     * @param i
     * @return
     */
    fun createRequest(editor: Editor, offset: Int, completionType: CompletionTypeEnum, antRequestTypeEnum: AntRequestTypeEnum): EditorRequest?

    /**
     * 执行发送请求的操作
     *
     * @param request        编辑器请求
     * @param maxCompletions 最大请求次数
     * @param enableCaching  是否启用缓存
     * @param cycling        没看明白
     * @param subscriber     处理完之后的事件订阅器
     * @return 是否嵌入完成
     */
    @RequiresBackgroundThread
    fun completions(
        request: EditorRequest,
        maxCompletions: Int?,
        enableCaching: Boolean,
        cycling: Boolean,
        editor: Editor,
        delayTime: Int,
        runnable: Runnable? = null,
        subscriber: Flow.Subscriber<CompletionInlayModel?>
    ): Boolean
}