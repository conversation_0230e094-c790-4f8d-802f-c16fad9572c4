package com.alipay.tsingyan.services.completion.edit

import com.alipay.tsingyan.services.completion.edit.model.AntCompletionTypeEnum
import com.alipay.tsingyan.services.completion.edit.model.EditorRequest
import com.intellij.openapi.editor.DefaultLanguageHighlighterColors
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.util.text.StringUtil
import com.intellij.ui.JBColor
import org.apache.commons.lang.StringUtils
import org.apache.http.util.TextUtils
import org.jetbrains.annotations.NonNls
import java.awt.Color
import java.awt.FontMetrics
import java.awt.Graphics
import java.awt.Rectangle
import java.util.stream.Collectors

/**
 * 默认内嵌样式
 */
class DefaultInlayRender(
    request: EditorRequest,
    completionTypeEnum: AntCompletionTypeEnum,
    lines: List<String?>,
    isShowTips: <PERSON><PERSON>an,
    tips: String
) : EditorCustomElementRenderer {

    var contentLines: List<String?>

    /**
     * 内容
     */
    private val content: String
    var inlay: Inlay<DefaultInlayRender>? = null

    /**
     * 缓存宽度信息
     */
    private var cachedWidth: Int

    /**
     * 缓存高度信息
     */
    private var cachedHeight: Int
    private val completionTypeEnum: AntCompletionTypeEnum

    init {
        cachedWidth = -1
        cachedHeight = -1
        contentLines = replaceLeadingTabs(lines, request)
        this.completionTypeEnum = completionTypeEnum
        content = StringUtils.join(lines, "\n") + tips
        if (isShowTips) {
            val modifiedContentLines = contentLines.toMutableList()

            val firstNonNullIndex = modifiedContentLines.indexOfFirst { it != null && !TextUtils.isEmpty(it) }
            if (firstNonNullIndex != -1) {
                modifiedContentLines[firstNonNullIndex] = modifiedContentLines[firstNonNullIndex] + tips
            }
            contentLines = modifiedContentLines.toList()
        }
    }

    /**
     * 渲染器实现应覆盖此选项以定义自定义元素的宽度（以像素为单位）。
     * 返回值将定义嵌入的结果。
     * getWidthInPixels（）和传递给渲染器的paint（Inlay、Graphics、Rectangle、TextAttributes）方法的targetRegion参数的宽度。
     * 对于内联和后行尾元素，它应始终为正值
     *
     * @param inlay
     * @return
     */
    override fun calcWidthInPixels(inlay: Inlay<*>): Int {
        if (cachedWidth >= 0) {
            return cachedWidth
        }
        val max = Math.max(1, InlayRendering.calculateWidth(inlay.editor, content, contentLines))
        cachedWidth = max
        return max
    }

    /**
     * 渲染块的高度
     *
     * 块元素的渲染器实现可以覆盖此方法来定义元素的高度（以像素为单位）。
     * 如果未重写，元素的高度将等于编辑器的行高度。
     * 返回值将定义嵌入的结果。getWidthInPixels（）和传递给渲染器的paint（Inlay、Graphics、Rectangle、TextAttributes）方法的targetRegion参数的高度。
     * 返回的值当前未用于内联元素。
     *
     * @param inlay
     * @return
     */
    override fun calcHeightInPixels(inlay: Inlay<*>): Int {
        if (cachedHeight >= 0) {
            return cachedHeight
        }
        val lineHeight = inlay.editor.lineHeight * contentLines.size
        cachedHeight = lineHeight
        return lineHeight
    }

    override fun paint(inlay: Inlay<*>, g: Graphics, targetRegion: Rectangle, textAttributes: TextAttributes) {
        val editor = inlay.editor
        if (!editor.isDisposed) {
            InlayRendering.renderCodeBlock(editor, content, contentLines, g, targetRegion, getTextAttributes(editor))
        }
    }

    /**
     * 获取到对应的代码块的Action
     *
     * @param inlay
     * @return
     */
    override fun getContextMenuGroupId(inlay: Inlay<*>): @NonNls String? {
        return "Ant.inlayContextMenu"
    }

    companion object {
        private fun getTextAttributes(editor: Editor): TextAttributes {
            // TODO 将颜色更改为用户可更改的
            val userColor = Color.gray
            val themeAttributes =
                editor.colorsScheme.getAttributes(DefaultLanguageHighlighterColors.INLAY_TEXT_WITHOUT_BACKGROUND)
            if (userColor != null || themeAttributes == null || themeAttributes.foregroundColor == null) {
                val customAttributes = themeAttributes?.clone() ?: TextAttributes()
                if (userColor != null) {
                    customAttributes.foregroundColor = userColor
                }
                if (customAttributes.foregroundColor == null) {
                    customAttributes.foregroundColor = JBColor.GRAY
                }
                return customAttributes
            }
            return themeAttributes
        }

        fun replaceLeadingTabs(lines: List<String?>, request: EditorRequest): List<String?> {
            return lines
                .stream()
                .map { line: String? ->
                    val tabCount = StringUtil.countChars(
                        line!!, '\t', 0, true
                    )
                    if (tabCount > 0) {
                        return@map StringUtil.repeatSymbol(' ', tabCount * request.tabWidth) + line.substring(tabCount)
                    }
                    line
                }
                .collect(Collectors.toList())
        }
    }

}