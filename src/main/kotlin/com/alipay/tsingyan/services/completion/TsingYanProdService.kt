package com.alipay.tsingyan.services.completion

import com.alipay.tsingyan.inline2.bean.FastApplyBean
import com.alipay.tsingyan.inline2.bean.InlineChatIntentRequestBean
import com.alipay.tsingyan.inline2.bean.InlineChatStreamReqBean
import com.alipay.tsingyan.inline2.bean.StreamResBean
import com.alipay.tsingyan.mcp.server.base.ToolConfMap
import com.alipay.tsingyan.mcp.server.base.ToolSummary
import com.alipay.tsingyan.model.*
import com.alipay.tsingyan.model.completion.CompletionConfigModel
import com.alipay.tsingyan.model.comment.AddCommentRequestBean
import com.alipay.tsingyan.model.comment.AddCommentResultMode
import com.alipay.tsingyan.model.commit.AddCommitMsgRequestBean
import com.alipay.tsingyan.model.commit.AddCommitMsgResultModel
import com.alipay.tsingyan.model.completion.CompletionRequestBean
import com.alipay.tsingyan.model.completion.CompletionResultModel
import com.alipay.tsingyan.model.completion.QueryConfigRequestBean
import com.alipay.tsingyan.model.request.UserInfoRequestBean
import com.alipay.tsingyan.model.log.ErrorLogInfoDO
import com.alipay.tsingyan.model.log.UploadLogRequestBean
import com.alipay.tsingyan.ui.config.UserInfoModel
import kotlinx.coroutines.flow.Flow
import java.net.http.HttpResponse
import java.util.stream.Stream

/**
 * 青燕的接口
 */
interface TsingYanProdService {

//    /**
//     * 查询用户信息和公钥
//     */
//    fun queryPubKey(userInfoRequestBean: UserInfoRequestBean): PublicKeyModel?
//    /**
//     * 查询用并更新公钥
//     */
//    fun queryAndUpdatePubKey():String?

    /**
     * 查询用户信息
     */
    fun queryUserInfo(userInfoRequestBean: UserInfoRequestBean): UserModel?


    /**
     * 查询远程配置
     * 如果有异常，返回默认值
     */
    fun queryConfig(queryConfigRequestBean: QueryConfigRequestBean): CompletionConfigModel

    /**
     * 提交埋点信息
     */
    fun <T> submitTrace(traceModel: TraceModel<T>, traceType: String? = null)

    /**
     * 提交埋点信息
     */
    fun <T> submitTraceTypeStr(traceModel: TraceModelTypeStr<T>)

    /**
     * 代码补全
     */
    fun completion(
        completionRequestBean: CompletionRequestBean
    ): CompletionResultModel?


    /**
     * 注释生成
     */
    fun addcomment(
        addCommentRequestBean: AddCommentRequestBean
    ): AddCommentResultMode?


    fun userLogout(userInfoRequestBean: UserInfoRequestBean): UserInfoModel?

    /**
     * 提交消息生成
     */
    fun addCommitMsg(addCommitMsgRequest: AddCommitMsgRequestBean): AddCommitMsgResultModel?

    fun getMergeTestCode(mergeTestCode : MergeTestCodeModel): String?

    fun getInlineChatIntention(inlineChatIntention: InlineChatIntentRequestBean): String?

    fun getInlineChatCodeGen(inlineChatIntention: InlineChatIntentRequestBean): String?

    /**
     * Text to code的流式请求
     */
    fun queryCodeGeneration(inlineChatStreamReqBean: InlineChatStreamReqBean): Flow<StreamResBean>?

    /**
     * FastApplyu
     */
    fun queryFastApply(bean : FastApplyBean) : Flow<StreamResBean>

    /**
     * 查询快速流式结果
     */
    fun querySimpleFastApply(bean : FastApplyBean) : Flow<StreamResBean>

    fun getFromCache(key: String): HttpResponse<Stream<String>>?
    fun removeFromCache(key: String)

    /**
     * 查询 Agent 配置
     */
    fun getMcpAgentConfig() : List<ToolConfMap>

    /**
     * 上传日志文件
     */
    fun uploadLog(uploadLogRequestBean: UploadLogRequestBean): ErrorLogInfoDO?
}