package com.alipay.tsingyan.services.completion.edit

import com.alipay.tsingyan.services.completion.edit.model.AntCompletionTypeEnum

interface AntEditorInlay {
    val type: AntCompletionTypeEnum
    var lines: MutableList<String>
    var editorOffset: Int

    /**
     * 重置Offset位置
     * @param offset
     */
    fun resetOffset(offset: Int)
    val isEmptyCompletion: <PERSON>olean
        get() {
            val completion = lines
            return completion.isEmpty() || completion.size == 1 && completion[0].isEmpty()
        }
}