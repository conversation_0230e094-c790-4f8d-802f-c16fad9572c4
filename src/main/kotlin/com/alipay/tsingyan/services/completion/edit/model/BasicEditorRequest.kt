package com.alipay.tsingyan.services.completion.edit.model

import cn.hutool.core.util.IdUtil
import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.services.completion.edit.service.LanguageSupport
import com.alipay.tsingyan.utils.AppConstant
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiFileFactory
import com.intellij.util.PathUtilRt

class BasicEditorRequest(
    project: Project,
    language: LanguageSupport?,
    completionType: CompletionTypeEnum,
    fileLanguage: LanguageInfo,
    relativeFilePath: String,
    documentContent: String,
    offset: Int,
    lineInfo: LineInfoModel,
    column: Int,
    useTabIndents: Boolean,
    tabWidth: Int,
    documentModificationSequence: Long,
    requestType: AntRequestTypeEnum,
    sourceCodePrefix: String,
    sourceCodeSuffix: String
) : EditorRequest, Disposable {
    override val project: Project
    private val language: LanguageSupport?
    val completionType: CompletionTypeEnum
    override val isUseTabIndents: Boolean
    override val tabWidth: Int
    override val requestId: Long
    private val fileLanguage: LanguageInfo
    override val relativeFilePath: String
    override val documentContent: String
    override var offset: Int = 0
    override val lineInfo: LineInfoModel
    override val column: Int
    override val requestTimestamp: Long
    override val documentModificationSequence: Long
    override var requestType: AntRequestTypeEnum = AntRequestTypeEnum.Automatic
    override val runtimeData: CompletionRtModel = CompletionRtModel()
    override var languageStr = ""
    override var sourceCodePrefix: String = ""
    override var sourceCodeSuffix: String = ""


    @Volatile
    override var isCancelled = false

    /**
     * @param project
     * @param language
     * @param completionType
     * @param fileLanguage
     * @param relativeFilePath
     * @param documentContent
     * @param offset
     * @param lineInfo
     * @param useTabIndents
     * @param tabWidth
     * @param documentModificationSequence
     */
    init {
        requestTimestamp = System.currentTimeMillis()
        AppConstant.LAST_REQUEST_TIME.set(requestTimestamp)
        this.project = project
        this.completionType = completionType
        this.fileLanguage = fileLanguage
        this.relativeFilePath = relativeFilePath
        this.documentContent = documentContent
        this.offset = offset
        this.language = language
        this.lineInfo = lineInfo
        this.column = column
        this.isUseTabIndents = useTabIndents
        this.tabWidth = tabWidth
        this.documentModificationSequence = documentModificationSequence
        //bugfix 用IdUtil.getSnowflakeNextId()，goland会等待时间较长，为了标记请求的id，直接用时间戳
        requestId = requestTimestamp
        this.requestType = requestType
        this.sourceCodePrefix = sourceCodePrefix
        this.sourceCodeSuffix = sourceCodeSuffix
    }

    /**
     * 判断是否是相等的
     *
     * @param editorRequest
     * @return
     */
    override fun equalsRequest(editorRequest: EditorRequest): Boolean {
        return requestId == editorRequest.requestId
    }

    override fun resetOffset(offset: Int) {
        this.offset = offset
    }

    override val disposable: Disposable
        get() = this

    override fun getLanguage(): LanguageSupport? {
        return language
    }

//    override fun createFile(): PsiFile? {
//        return if (isCancelled) {
//            null
//        } else PsiFileFactory
//            .getInstance(project)
//            .createFileFromText(
//                PathUtilRt.getFileName(relativeFilePath), fileLanguage.language, documentContent,
//                false, false, true
//            )
//    }

    override fun cancel() {
        if (!isCancelled) {
            isCancelled = true
            Disposer.dispose((this as Disposable))
        }
    }

    override fun dispose() {
        isCancelled = true
    }
}