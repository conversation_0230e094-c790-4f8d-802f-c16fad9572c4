package com.alipay.tsingyan.services.completion.edit.model

import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.services.completion.edit.service.Cancellable
import com.alipay.tsingyan.services.completion.edit.service.LanguageSupport
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile

/**
 * 编辑器请求
 */
interface EditorRequest : Cancellable {
    /**
     * 行信息
     *
     * @return
     */
    val lineInfo: LineInfoModel

    /**
     * 获取当前的列号
     *
     * @return
     */
    val column: Int

    /**
     * 判断请求是否重复
     *
     * @param editorRequest
     * @return
     */
    fun equalsRequest(editorRequest: EditorRequest): Boolean
    fun getLanguage(): LanguageSupport?

//    fun createFile(): PsiFile?

    /**
     * 获取编辑器提示内容
     *
     * @return
     */
    val documentContent: String


    val project: Project


    val offset: Int

    /**
     * 重置光标位置
     */
    fun resetOffset(offset: Int)
    val isUseTabIndents: Boolean
    val tabWidth: Int
    val requestId: Long?
    val disposable: Disposable?
    val requestTimestamp: Long
    val documentModificationSequence: Long
    val runtimeData: CompletionRtModel
    var languageStr:String

    /**
     * 获取当前文本的前缀
     *
     * @return
     */
    val currentDocumentPrefix: String
        get() = documentContent.substring(0, offset)

    /**
     * 获取文件的相对路径
     *
     * @return
     */
    val relativeFilePath: String

    var requestType: AntRequestTypeEnum

    /**
     * codeEdit专用参数
     * sourceCodePrefix 前一行+光标所在的前半行
     * sourceCodeSuffix 光标所在的后半行 + 后三行
     */
    var sourceCodePrefix:String
    var sourceCodeSuffix:String
}