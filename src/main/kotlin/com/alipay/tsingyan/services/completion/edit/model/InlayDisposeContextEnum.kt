package com.alipay.tsingyan.services.completion.edit.model

enum class InlayDisposeContextEnum {
    UserAction,
    IdeCompletion,
    CaretChange,
    SelectionChange,
    SettingsChange,
    Cycling,
    TypingAsSuggested,
    Typing,
    Applied;

    val isResetLastRequest: Boolean
        get() = this == SettingsChange || this == Applied
    val isSendRejectedTelemetry: Boolean
        get() = this == UserAction
    val isCycling: Boolean
        get() = this == Cycling
    val isApply: Boolean
        get() = this == Applied
    val isTyping: Boolean
        get() = this == Typing
    val isTypingAsSuggested: Boolean
        get() = this == TypingAsSuggested
}