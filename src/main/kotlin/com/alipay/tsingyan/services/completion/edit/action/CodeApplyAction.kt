package com.alipay.tsingyan.services.completion.edit.action

import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.ApplyInlayType
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.completion.EditorUtilCopy
import com.intellij.application.options.CodeStyle
import com.intellij.codeInsight.lookup.LookupManager
import com.intellij.codeInsight.template.TemplateManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorActionHandler
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.util.TextRange
import java.awt.event.KeyEvent

class CodeApplyAction protected constructor() : EditorAction(ApplyInlaysHandler()), DumbAware {
    private val LOGGER: Logger = Logger.getInstance(CodeApplyAction::class.java)

    val editorManagerService = service<EditorManagerService>()
    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    val localUserStore: LocalUserStore = service<LocalUserStore>()
    init {
        setInjectedContext(true)
    }


    /**
     * 判断是否要启用当前的Action
     *
     * @param e Carries information on the invocation place and data available
     */
    override fun update(e: AnActionEvent) {
        var isIgnoredKeyboardEvent = false
        try {
            if (editorManagerService.isCodeEditsViewShowing() || editorManagerService.isNextTabViewShowing()){
                e.presentation.isEnabled = true
            } else {
                isIgnoredKeyboardEvent = isIgnoredKeyboardEvent(e)

                if (isIgnoredKeyboardEvent) {
                    LOGGER.info("isIgnoredKeyboardEvent = true")
                    val editor = getEditor(e.dataContext)
                    if (editor != null) {
                        if (!editorManagerService.hasCompletionInlays(editor)) {
                            LOGGER.info("hasCompletionInlays = false")
                            e.presentation.isEnabled = false
                            return
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LOGGER.info(e)
        }

        super.update(e)
    }

    private fun isIgnoredKeyboardEvent(e: AnActionEvent): Boolean {
        return if (e.inputEvent !is KeyEvent) {
            false
        } else if ((e.inputEvent as KeyEvent).keyChar != '\t') {
            false
        } else {
            val project = e.project
            if (project == null) {
                false
            } else {
                val editor = getEditor(e.dataContext)
                if (editor == null) {
                    false
                } else {
                    val document = editor.document
                    val blockIndent = CodeStyle.getIndentOptions(project, document).INDENT_SIZE
                    val caretOffset = editor.caretModel.offset
                    val line = document.getLineNumber(caretOffset)
                    if (isNonEmptyLinePrefix(document, line, caretOffset)
                    ) {
                        false
                    } else {
                        val caretOffsetAfterTab: Int =
                            EditorUtilCopy.indentLine(project, editor, line, blockIndent, caretOffset)
                        if (caretOffsetAfterTab < caretOffset) {
                            false
                        } else {
                            val tabRange = TextRange.create(caretOffset, caretOffsetAfterTab)
                            if (editorManagerService.countCompletionInlays(editor, tabRange, true, false, false, false) > 0) {
                                false
                            } else {
                                val endOfLineInlays: Int = editorManagerService.countCompletionInlays(editor, tabRange, false, true, false, false)
                                if (endOfLineInlays > 0) {
                                    false
                                } else {
                                    val blockInlays: Int = editorManagerService.countCompletionInlays(editor, tabRange, false, false, true, false)
                                    if (blockInlays > 0) {
                                        val caretToEndOfLineRange =
                                            TextRange.create(caretOffset, document.getLineEndOffset(line))
                                        editorManagerService.countCompletionInlays(
                                            editor,
                                            caretToEndOfLineRange,
                                            true,
                                            true,
                                            false,
                                            true
                                        ) > 0
                                    } else {
                                        true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun isNonEmptyLinePrefix(document: Document, lineNumber: Int, caretOffset: Int): Boolean {
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        return if (lineStartOffset == caretOffset) {
            false
        } else {
            val linePrefix = document.getText(TextRange.create(lineStartOffset, caretOffset))
            isSpacesOrTabs(linePrefix, false)
        }
    }

    fun isSpaceOrTab(c: Char, withNewline: Boolean): Boolean {
        return c == ' ' || c == '\t' || withNewline && c == '\n'
    }

    fun isSpacesOrTabs(text: CharSequence, withNewlines: Boolean): Boolean {
        for (i in 0 until text.length) {
            val c = text[i]
            if (!isSpaceOrTab(c, withNewlines)) {
                return false
            }
        }
        return true
    }


    /**
     * 用户操作的Handler
     */
    class ApplyInlaysHandler : EditorActionHandler() {
        private val LOGGER: Logger = Logger.getInstance(ApplyInlaysHandler::class.java)
        val editorManagerService = service<EditorManagerService>()

        override fun isEnabledForCaret(editor: Editor, caret: Caret, dataContext: DataContext): Boolean {
            return isSupported(editor) || isSupportedCodeEdits(editor) || isSupportedNextTab(editor)
        }

        override fun executeInCommand(editor: Editor, dataContext: DataContext): Boolean {
            return false
        }

        override fun doExecute(editor: Editor, caret: Caret?, dataContext: DataContext) {
            LogUtil.info("apply","ApplyInlaysHandler doExecute ", false)
            if (editorManagerService.isNextTabViewShowing()){
                editorManagerService.applyNextTab(editor, ApplyInlayType.WHOLE)
            } else if (editorManagerService.isCodeEditsViewShowing()){
                editorManagerService.applyCodeEditsCompletion(editor, ApplyInlayType.WHOLE)
            } else {
                editorManagerService.applyCompletion(editor, ApplyInlayType.WHOLE)
            }

            super.doExecute(editor, caret, dataContext)
        }

        private fun isSupported(editor: Editor): Boolean {
            try {
                val project = editor.project
                return project != null && editor.caretModel.caretCount == 1 && (LookupManager.getActiveLookup(editor) == null)
                        && editorManagerService.hasCompletionInlays(editor) && TemplateManager.getInstance(project).getActiveTemplate(editor) == null
            } catch (e: Exception) {
                LOGGER.info(e)
            }
            return false
        }

        private fun isSupportedCodeEdits(editor: Editor): Boolean {
            try {
                return editorManagerService.isCodeEditsViewShowing()
            } catch (e: Exception) {
                LOGGER.info(e)
            }
            return false
        }

        private fun isSupportedNextTab(editor: Editor): Boolean {
            try {
                return editorManagerService.isNextTabViewShowing()
            } catch (e: Exception) {
                LOGGER.info(e)
            }
            return false
        }
    }
}