package com.alipay.tsingyan.services.completion.edit

import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.services.completion.AntCompletion
import com.alipay.tsingyan.services.completion.edit.model.CompletionsModel
import com.intellij.openapi.util.TextRange

class DefaultInlayList internal constructor(
    override val antCompletion: AntCompletion,
    override val replacementRange: TextRange,
    replacementText: String,
    override var inlays: MutableList<AntEditorInlay?>,
    contextID: String,
    completionsModel: CompletionsModel
) : AntInlayList {
    override var replacementText: String = ""
    override val completionsModel: CompletionsModel
    override val contextId: String
    override var runtimeData: CompletionRtModel? = null
    override var lineSuffix: String = ""
    override var isAcceptLine: Boolean = false

    init {
        this.inlays = java.util.List.copyOf(inlays).toMutableList()
        this.replacementText = replacementText
        this.completionsModel = completionsModel
        contextId = contextID
    }

    override val isEmpty: Boolean
        get() = false

    override fun iterator(): Iterator<AntEditorInlay?> {
        return inlays.iterator()
    }
}