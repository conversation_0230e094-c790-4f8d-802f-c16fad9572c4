package com.alipay.tsingyan.services.completion.edit

import com.alipay.tsingyan.model.completion.CompletionRtModel
import com.alipay.tsingyan.services.completion.AntCompletion
import com.alipay.tsingyan.services.completion.edit.model.AntCompletionTypeEnum
import com.alipay.tsingyan.services.completion.edit.model.CompletionsModel
import com.alipay.tsingyan.utils.AppConstant
import com.intellij.openapi.util.TextRange
import org.apache.commons.lang3.StringUtils

interface AntInlayList : Iterable<AntEditorInlay?> {
    val isEmpty: Boolean

    /**
     * 获取完成
     *
     * @return
     */
    val antCompletion: AntCompletion

    /**
     * 获取替换范围
     *
     * @return
     */
    val replacementRange: TextRange

    /**
     * 获取替换的文本
     *
     * @return
     */
    var replacementText: String

    /**
     * 获取内嵌
     *
     * @return
     */
    var inlays: MutableList<AntEditorInlay?>

    /**
     * 当前执行的请求
     *
     * @return
     */
    val completionsModel: CompletionsModel

    /**
     * 获取到当前的ContextID
     * @return
     */
    val contextId: String?

    /**
     * 获取埋点的数据
     */
    var runtimeData: CompletionRtModel?

    /**
     * 最后的LineSuffix
     */
    var lineSuffix:String

    var isAcceptLine:Boolean

    fun size(): Int {
        var count = 0
        for (item in this) count++
        return count
    }


    fun refactorByAcceptLine(): String? {
        if (this.inlays.isEmpty()) {
            return null
        }
        val lines: MutableList<String> = ArrayList()
        var validContent = ""
        while (StringUtils.isBlank(validContent) && StringUtils.isNotBlank(this.replacementText)) {
            if (this.inlays.isEmpty()) {
                return null
            }
            val chunk = inlays.get(0)
            if (chunk?.lines == null || chunk.lines.isEmpty()) {
                return null
            }
            val line: String = chunk.lines.get(0)
            lines.add(line)
            validContent += line
            if (AntCompletionTypeEnum.Inline === chunk.type) {
                this.inlays.removeAt(0)
            } else if (AntCompletionTypeEnum.Block === chunk.type) {
                chunk.lines.removeAt(0)
            }
            val sb = StringBuilder()
            for (chuck in this.inlays) {
                if (chuck != null) {
                    for (line1 in chuck.lines) {
                        sb.append(line1).append(AppConstant.ENTER_SYMBOL)
                    }
                }
            }
            if (sb.length > 0) {
                sb.deleteCharAt(sb.length - 1)
            }
            this.replacementText = sb.toString()
        }
        return StringUtils.join(lines,AppConstant.ENTER_SYMBOL)
    }

    fun getLineCount(): Int {
        var count = 0
        for (item in this) {
            count += item?.lines?.size ?: 0
        }
        return count
    }

    fun setEditorOffset(offset: Int){
        for (item in this) {
            item?.editorOffset = offset
        }
    }

    fun isAllInlineType(): Boolean {
        for (item in this) {
            if (item?.type != AntCompletionTypeEnum.Inline) {
                return false
            }
        }
        return true
    }

    fun getInlineCount(): Int {
        var count = 0
        for (item in this) {
            if (item?.type == AntCompletionTypeEnum.Inline) {
                count++
            }
        }
        return count
    }
}