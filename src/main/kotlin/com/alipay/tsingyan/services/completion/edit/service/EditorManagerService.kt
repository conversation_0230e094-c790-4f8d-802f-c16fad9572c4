package com.alipay.tsingyan.services.completion.edit.service

import com.alipay.tsingyan.services.completion.edit.model.AntRequestTypeEnum
import com.alipay.tsingyan.services.completion.edit.model.ApplyInlayType
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.util.TextRange
import com.intellij.util.concurrency.annotations.RequiresEdt

/**
 * 编辑器操作
 */
interface EditorManagerService : Disposable {

    /**
     * 判断当前编辑器是否有效
     */
    fun isAvailable(editor:Editor): Bo<PERSON><PERSON>

    /**
     * 去除之前展示的内嵌样式
     *
     * @param editor Editor
     */
    fun disposeInlays(editor: Editor, disposeContext: InlayDisposeContextEnum)

    fun disposeCodeEditsInlays(editor: Editor, disposeContext: InlayDisposeContextEnum)

    fun isCodeEditsViewShowing(): Boolean

    fun isNextTabViewShowing(): Boolean

    fun isCodeEditsViewRequesting(): Boolean

    fun isNextTabViewRequesting(): Boolean

    fun disposeNextTabInlays(editor: Editor, disposeContext: InlayDisposeContextEnum)
    /**
     * 推荐业务入口
     *
     * @param editor      Editor
     * @param offset      当前光标所在的位置
     */
    fun editModified(editor: Editor, startOffset: Int,requestType: AntRequestTypeEnum, commandName: String?)

    fun editorModified(editor: Editor?, requestType: AntRequestTypeEnum?, commandName: String? = null) {
        if (editor == null || requestType==null) {
            return
        }

        editModified(editor, editor.caretModel.offset, requestType, commandName)
    }

    fun codeEditsTrigger(editor: Editor, type: AntRequestTypeEnum)

    fun mockEditModifiedStart(editor: Editor)

    fun mockEditModifiedEnd(editor: Editor)

    /**
     * 支持预加载的codeEditsTrigger，用于预先获取指定行号的codeEdits数据
     * @param editor 编辑器
     * @param targetLineNumber 目标行号
     * @param requestType 请求类型
     */
    fun codeEditsTriggerWithPreload(editor: Editor, targetLineNumber: Int, requestType: AntRequestTypeEnum)

    fun getLastShowCodeEditsLineNum(editor: Editor): Int

    /**
     * 应用当前编辑器推荐的样式
     *
     * @param editor Editor
     * @return Boolean
     */
    fun applyCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean

    /**
     * 应用当前CdeEdits的样式
     *
     * @param editor Editor
     * @return Boolean
     */
    fun applyCodeEditsCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean


    /**
     * 跳转到下一个tab
     *
     * @param editor Editor
     * @return Boolean
     */
    fun applyNextTab(editor: Editor, applyInlayType: ApplyInlayType): Boolean

    /**
     * 统计当前的内嵌样式个数
     *
     * @param editor                   当前的编辑器
     * @param searchRange              选中的代码块
     * @param inlineInlays             行内样式
     * @param afterLineEndInlays       行后样式
     * @param blockInlays              代码块样式
     * @param matchInLeadingWhitespace 是否匹配前面的空格
     * @return int
     */
    @RequiresEdt
    fun countCompletionInlays(
        editor: Editor,
        searchRange: TextRange,
        inlineInlays: Boolean,
        afterLineEndInlays: Boolean,
        blockInlays: Boolean,
        matchInLeadingWhitespace: Boolean
    ): Int

    /**
     * 取消之前的请求
     *
     * @param editor Editor
     */
    @RequiresEdt
    fun cancelCompletionRequests(editor: Editor)

    /**
     * 是否有当前的内嵌样式
     *
     * @param editor Editor
     * @return Boolean
     */
    fun hasCompletionInlays(editor: Editor): Boolean

    /**
     * 判断是否还有推荐的结果集
     *
     * @param editor Editor
     * @return Boolean
     */
    fun hasPreviousInlaySet(editor: Editor): Boolean?

    /**
     * 显示上一个推荐的结果集
     *
     * @param editor Editor
     */
    fun showPreviousInlaySet(editor: Editor)

    /**
     * 判断是否还有下一个推荐结果
     *
     * @param editor Editor
     * @return Boolean
     */
    fun hasNextInlaySet(editor: Editor): Boolean?


    /**
     * 显示下一个推荐的结果集
     *
     * @param editor Editor
     */
    fun showNextInlaySet(editor: Editor)

    /**
     * 判断最后一次请求是否已经结束
     *
     * @return
     */
    fun isEndLastRequest(): Boolean

    /**
     * 检查是否刚刚应用了NextTab，用于避免在visibleAreaChanged中误销毁预加载的codeEditsView
     *
     * @return Boolean
     */
    fun isRecentlyAppliedNextTab(): Boolean

    /**
     * 统一的登录判断
     */
    fun checkLogin():Boolean

    /**
     * 当页面切换时的监听
     */
    fun onChangePage(oldFileName: String?, oldFileDocCount:Int, newFileName: String?, newFileDocCount:Int)

}