package com.alipay.tsingyan.services.completion.edit.service

/**
 * 抽象取消模型
 */
interface Cancellable {
    /**
     * 请求是否被取消
     *
     * @return
     */
    val isCancelled: Boolean

    /**
     * 取消请求
     */
    fun cancel()

    companion object {
        val DUMB: Cancellable = object : Cancellable {
            override val isCancelled: <PERSON>olean
                get() = false

            override fun cancel() {}
        }
    }
}