package com.alipay.tsingyan.services.search

import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.util.FileSearchVo
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.ProjectScope
import com.intellij.util.Processor
import com.intellij.util.containers.CollectionFactory
import com.intellij.util.indexing.FileBasedIndex
import org.apache.http.util.TextUtils


class FileSearchService(val project: Project) {
    /**
     * 文件检索
     */
    fun search(
        keyword: String,
        maxCount: Int = 100,
    ): List<FileSearchVo> {
        val result = if (TextUtils.isEmpty(keyword)) {
            searchRecentFiles()
        } else {
            searchByIndex(keyword, maxCount)
        }
        return result.take(maxCount)
    }

    /**
     * 根据索引进行检索
     */
    fun searchByIndex(keyword: String, maxCount: Int = 100): List<FileSearchVo> {
        val result = mutableListOf<FileSearchVo>()
        val baseDir = VfsUtils.getProjectBaseDir(project)

        val startTime = System.currentTimeMillis()
        val searchScope = ProjectScope.getProjectScope(project)

        fun matchName(name: String, keyword: String): Boolean {
            return if (keyword.length == 1) {
                name.startsWith(keyword)
            } else {
                name.contains(keyword,true)
            }
        }


        ReadAction.nonBlocking {
//            val interval = TimeInterval()
//            interval.start("1")
            val allKeys = CollectionFactory.createSmallMemoryFootprintSet<String?>()
            FilenameIndex.processAllFileNames(Processor { name: String? ->
                if (null != name && matchName(name, keyword)) {
                    allKeys.add(name)
                }
                true
            }, searchScope, null)

            val setKeys = allKeys.toSet()
//            LogUtil.info("检索 key 耗时，${interval.intervalMs()}ms, size:[${setKeys.size}]")
            FileBasedIndex.getInstance().processFilesContainingAnyKey(
                FilenameIndex.NAME,
                setKeys,
                searchScope,
                null,
                null,
                Processor { file: VirtualFile? ->
                    if (null != file && !file.isDirectory) {
                        result.add(FileSearchVo(file.path.removeSuffix(baseDir)))
                    }
                    result.size < maxCount && (System.currentTimeMillis() - startTime < 800)
                })
//            LogUtil.info("总耗时，${interval.intervalMs()}，文件结果个数：${result.size}")
        }.executeSynchronously()
        return result.take(maxCount)
    }

    /**
     * 检索最近的文件
     */
    fun searchRecentFiles(): List<FileSearchVo> {
        return project.getService(RecentFilesService::class.java)
            ?.getOpenFiles(project)
            ?.map { FileSearchVo(it) }
            ?: emptyList()
    }
}