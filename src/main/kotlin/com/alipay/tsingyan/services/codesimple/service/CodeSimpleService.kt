package com.alipay.tsingyan.services.codesimple.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.codesuggestion.service.StaticAnalysisInfo
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.CodeFuseDialogWebBrowser
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager

/**
 * <AUTHOR>
 *
 * 2023-12-25 14:28:17
 *
 */
class CodeSimpleService: Disposable {
    private val LOGGER: Logger = Logger.getInstance(CodeSimpleService::class.java)

    private val analysisCodeAlarm = CancellableAlarm(this)

    fun simpleCode(project: Project, selectCode: String, clickType: ClickType) {
        val webView = project.getService(WebViewService::class.java).getWebView()

        //已经初始化过了，直接使用即可
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            openToolWindow(project)
            simpleCode(project, selectCode, webView, clickType)
            return
        }

        //没有初始化过，需要等待初始化成功
        analysisCodeAlarm.cancelAllAndAddRequest({
            ApplicationManager.getApplication().invokeLater {
                openToolWindow(project)
            }

            //等待页面加载完成后再进行操作
            var count = 10
            while (true){
                if (--count <= 0){
                    return@cancelAllAndAddRequest
                }
                if (webView.getHasLoadedFinished()){
                    Thread.sleep(3500)
                    LogUtil.info("simpleCode invokeLater $count")
                    ApplicationManager.getApplication().invokeLater {
                        simpleCode(project, selectCode, webView, clickType)
                    }
                    return@cancelAllAndAddRequest
                }

                LOGGER.info("simpleCode wait Count $count")
                Thread.sleep(500)
            }

        }, 0)
    }

    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }


    private fun simpleCode(project: Project, selectedText: String, webView: CodeFuseDialogWebBrowser, clickType: ClickType){
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.CHAT_DATA.name
        val jsonObject = JSONObject()
        jsonObject.put("question", selectedText)
        jsonObject.put("intention", AppConstant.CHAT_INTENTION_CODE_SIMPLE)
        jsonObject.put("repo", ProjectCache.getGitData(project))
        jsonObject.put("enterType", clickType.toString())
        try {
            jsonObject.put("staticAnalysisInfo", JSON.toJSONString(
                StaticAnalysisInfo(FileEditorManager.getInstance(project).selectedTextEditor!!)
            ))
        } catch (e: Throwable){
            LOGGER.debug("simpleCode staticAnalysisInfo error")
        }
        jsonObject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
        messageModel.message = jsonObject.toString()
        LOGGER.info("simpleCode sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
    }
}