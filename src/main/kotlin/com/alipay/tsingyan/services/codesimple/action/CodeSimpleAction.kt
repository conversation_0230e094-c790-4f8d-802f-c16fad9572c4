package com.alipay.tsingyan.services.codesimple.action

import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.TraceTypeEnum
import com.alipay.tsingyan.services.codesimple.service.CodeSimpleService
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.NotificationUtils
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.actionSystem.UpdateInBackground

/**
 * <AUTHOR>
 *
 * 2023-12-25 14:28:17
 *
 */
class CodeSimpleAction : AnAction(), UpdateInBackground {
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    private val editorManagerService = service<EditorManagerService>()
    val tracerService = service<CodeFuseTracerService>()


    override fun actionPerformed(e: AnActionEvent) {
        if (!editorManagerService.checkLogin()) {
            e.project?.let { NotificationUtils.showBeginLoginMessage(it) }
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        val project = e.project
        if (editor != null && project != null && !project.isDisposed) {
            val selectionModel = editor.selectionModel
            var selectedText = selectionModel.selectedText ?: ""
            if (selectedText.isEmpty() || selectedText.isBlank()) {
                editor.project?.let { NotificationUtils.notifyMessage("CodeFuse", "未选中代码", it, null) }
                return
            }
            val simpleText = "简化以下代码：\n"+"```\n$selectedText\n```"
            project.service<CodeSimpleService>().simpleCode(project, simpleText, ClickType.RIGHT)
            tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_CODE_SUGGESTION, null)
        }

    }


    override fun update(e: AnActionEvent) {
        super.update(e)

        if (e.project == null){
            e.presentation.isVisible = false
            return
        }

        if (!editorManagerService.checkLogin()) {
            e.project?.let { e.presentation.isVisible = false }
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) as Editor?
        val project = e.project
        if (editor != null && project != null && !project.isDisposed) {
            val selectionModel = editor.selectionModel
            val selectedText = selectionModel.selectedText ?: ""
            if (selectedText.isEmpty() || selectedText.isBlank()) {
                e.presentation.isVisible = false
                return
            }
        }

        e.presentation.isVisible = true
    }

}