package com.alipay.tsingyan.services.chat.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.CodeFuseDialogWebBrowser
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager

/**
 * 代码解析的逻辑
 * author:jianzhi
 *
 * 2023年08月04日16:22:50
 */
class AnalysisCodeService: Disposable {
    private val LOGGER: Logger = Logger.getInstance(AnalysisCodeService::class.java)

    private val analysisCodeAlarm = CancellableAlarm(this)

    fun analysisyToWebView(project: Project, selectCode: String, fileContent: String, clickType: ClickType ) {
        val webView = project.getService(WebViewService::class.java).getWebView()

        //已经初始化过了，直接使用即可
        if (webView.getHasLoadedFinished() || CommonUtils.isCloudIDE) {
            openToolWindow(project)
            analysisyToWebView(project, selectCode, fileContent, webView, clickType)
            return
        }

        //没有初始化过，需要等待初始化成功
        analysisCodeAlarm.cancelAllAndAddRequest({
            ApplicationManager.getApplication().invokeLater {
                openToolWindow(project)
            }

            //等待页面加载完成后再进行操作
            var count = 10
            while (true){
                if (--count <= 0){
                    return@cancelAllAndAddRequest
                }
                if (webView.getHasLoadedFinished()){
                    Thread.sleep(3500)
                    LogUtil.info("analysisyToWebView invokeLater $count")
                    ApplicationManager.getApplication().invokeLater {
                        analysisyToWebView(project, selectCode, fileContent, webView, clickType)
                    }
                    return@cancelAllAndAddRequest
                }

                LOGGER.info("analysisyToWebView wait Count $count")
                Thread.sleep(500)
            }

        }, 0)
    }


    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }


    private fun analysisyToWebView(project: Project, selectedText: String, fileContent:String, webView: CodeFuseDialogWebBrowser, clickType: ClickType){
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.CHAT_DATA.name
        val jsonOject = JSONObject()
        jsonOject.put("question", selectedText)
        jsonOject.put("intention", AppConstant.CHAT_INTENTION_EXPLAIN)
        jsonOject.put("fileContent", fileContent)
        jsonOject.put("repo", ProjectCache.getGitData(project))
        jsonOject.put("enterType", clickType.toString())
        jsonOject.put("recentFilesInfo", project.getService(RecentFilesService::class.java)?.getRecentFiles(project))
        messageModel.message = jsonOject.toString()
        LOGGER.debug("analysisyToWeb sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }

    /**
     * Usually not invoked directly, see class javadoc.
     */
    override fun dispose() {
    }
}
