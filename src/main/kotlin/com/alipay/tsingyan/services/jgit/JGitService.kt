package com.alipay.tsingyan.services.jgit

import cn.hutool.core.io.FileUtil
import cn.hutool.core.thread.ThreadUtil
import org.eclipse.jgit.api.Git
import org.eclipse.jgit.api.ResetCommand
import org.eclipse.jgit.api.errors.EmptyCommitException
import org.eclipse.jgit.api.errors.NoHeadException
import org.eclipse.jgit.diff.DiffEntry
import org.eclipse.jgit.diff.DiffEntry.ChangeType
import org.eclipse.jgit.lib.*
import org.eclipse.jgit.revwalk.RevCommit
import org.eclipse.jgit.revwalk.RevWalk
import org.eclipse.jgit.storage.file.FileRepositoryBuilder
import org.eclipse.jgit.treewalk.AbstractTreeIterator
import org.eclipse.jgit.treewalk.CanonicalTreeParser
import org.eclipse.jgit.treewalk.TreeWalk
import java.io.ByteArrayOutputStream

/**
 * 仓库地址
 */
class JGitService(
    val repoPath: String,
    private val repoId: String,
    private val projectName: String = "Default",
) {

    val gitPath = getPath()
    private lateinit var git: Git

    /**
     * 初始化仓库
     * 初始化仓库是一个略微耗时的操作，上层调用时要保证不能重复初始化
     */
    @Synchronized
    fun initRepo(doCreate: Boolean = false) {
        //目录不存在，则需要删除
        val repository = FileRepositoryBuilder()
            .setGitDir(FileUtil.file(gitPath))
            .setWorkTree(FileUtil.file(repoPath))
            .setup()
            .build()

        //如果存储区不存在，则创建新的存储区，否则使用老的
        if (doCreate || !FileUtil.exist(gitPath)) {
            repository.create(true)
        }
        git = Git(repository)
    }

    /**
     * 删除对应的存储区
     */
    fun del() {
        FileUtil.del(gitPath)
    }

    /**
     * 做提交
     * @throws EmptyCommitException 不允许创建空提交
     */
    @Throws(EmptyCommitException::class)
    fun doAddAndCommit(pathList: List<String>, desc: String, reAdd: Boolean = true): RevCommit? {
        // 如果为空，则只做提交，不进行ADD操作
        if (reAdd && pathList.isNotEmpty()) {
            doAdd(pathList)
        }
        return doCommit(desc)
    }

    /**
     * 仅添加文件
     */
    @Synchronized
    fun doAdd(pathList: List<String>) {
        pathList.forEach { git.add().addFilepattern(it).call() }
    }

    /**
     * 仅仅提交提交暂存区的文件，暂存区即之前 git add 的文件
     * @throws EmptyCommitException 不允许创建空提交
     * TODO 考虑不允许空提交
     */
    @Throws(EmptyCommitException::class)
    @Synchronized
    fun doCommit(desc: String, allowEmpty: Boolean = true): RevCommit? {
        // 偶现提交的时候会报index锁的问题，还不清楚为啥，先在提交的时候默认删除一下试试 @see https://www.eclipse.org/forums/index.php/t/1102984/
        if (git.repository.exactRef("HEAD") == null) {
            // 设置初始 HEAD 引用
            val headRefUpdate: RefUpdate = git.repository.updateRef("HEAD")
            headRefUpdate.link("refs/heads/main")
            // 创建初始空提交
            git.commit()
                .setMessage("Initial commit")
                .setAllowEmpty(true)
                .call()
        }

        verifyLock()
        return git
            .commit()
            .setAllowEmpty(allowEmpty)
            .setMessage(desc)
            .setAuthor("CodeFuse", "alipay.com")
            .call()
    }

    /**
     * 获取 git log
     * 没有提交时，直接抛出异常
     */
    @Throws(NoHeadException::class)
    fun getLog(): Iterable<RevCommit> {
        return git
            .log()
            .setMaxCount(40)
            .call()
    }

    /**
     * 根据 CommitID 解析出对应的 Commit 对象
     */
    fun resolveCommitID(commitID: String): RevCommit {
        val objectId = git.repository.resolve(commitID)
        return RevWalk(git.repository).use { it.parseCommit(objectId) }
    }

    /**
     *  Git Reset 操作
     */
    fun resetCommit(commitID: String, mode: ResetCommand.ResetType = ResetCommand.ResetType.HARD) {
        resetCommit(resolveCommitID(commitID), mode)
    }

    /**
     *  Git Reset 操作
     */
    fun resetCommit(commit: RevCommit, mode: ResetCommand.ResetType = ResetCommand.ResetType.HARD) {
        git.reset()
            .setMode(mode)
            .setRef(commit.name)
            .call()
    }

    /**
     * 根据 Commit 对象获取到
     */
    fun getCommitID(commit: RevCommit): String {
        return commit.name
    }


    /**
     * 获取当前 HEAD 到指定 Commit 之间的差异
     */
    fun getDiffWithHead(commitID: String): List<FileDiff> {
        return getDiff(commitID, Constants.HEAD)
    }

    /**
     * 获取两个 Commit 之间的 Diff
     */
    fun getDiff(commitID1: String, commitID2: String): List<FileDiff> {
        return parseDiffEntry(getDiffBetweenCommits(commitID1, commitID2))
    }

    /**
     * 获取两个 Diff 信息
     */
    fun getDiffBetweenCommits(start: String, end: String): List<DiffEntry> {
        return try {
            // 获取 HEAD 提交的树对象
            val headTreeParser = getTreeParser(start)
            // 获取目标提交的树对象
            val targetTreeParser = getTreeParser(end)

            // 计算差异
            git.diff()
                .setOldTree(targetTreeParser)
                .setNewTree(headTreeParser)
                .call()
                .toList()
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    /**
     * 获取文件的内容
     */
    fun getCommitContent(commitID: String, path: String): String? {
        val commit = resolveCommitID(commitID)
        val treeWalk = TreeWalk.forPath(git.repository, path, commit.tree)

        if (null != treeWalk) {
            val objectId = treeWalk.getObjectId(0)
            val content = getContent(objectId)
            return content
        }
        return null
    }

    /**
     * 手动执行一下 Git GC 操作
     */
    fun gc() {
        git.gc()
    }

    /**
     * 获取对应 Commit 的Tree
     */
    private fun getTreeParser(commitId: String): AbstractTreeIterator {
        val revWalk = RevWalk(git.repository)
        val objectId = git.repository.resolve(commitId)

        val commit = revWalk.parseCommit(objectId)
        val tree = commit.tree
        val reader = git.repository.newObjectReader()
        return CanonicalTreeParser().apply {
            reset(reader, tree)
        }
    }

    /**
     * 解析 DiffEntry，返回文件新老路径
     */
    private fun parseDiffEntry(entryList: List<DiffEntry>): MutableList<FileDiff> {
        val result = mutableListOf<FileDiff>()
        for (entry in entryList) {
            when (entry.changeType) {
                ChangeType.MODIFY -> {
                    result.add(
                        FileDiff(
                            entry.changeType.name,
                            entry.newPath,
                            getContent(entry.oldId),
                            getContent(entry.newId),
                        )
                    )
                }

                ChangeType.ADD -> {
                    result.add(
                        FileDiff(
                            entry.changeType.name,
                            entry.newPath,
                            getContent(entry.oldId),
                            getContent(entry.newId),
                        )
                    )
                }

                ChangeType.DELETE -> {
                    result.add(
                        FileDiff(
                            entry.changeType.name,
                            entry.oldPath,
                            getContent(entry.oldId),
                            getContent(entry.newId),
                        )
                    )
                }

                else -> {

                }
            }
        }
        return result
    }


    /**
     * 获取文件的信息
     */
    private fun getContent(objectId: AbbreviatedObjectId): String {
        return try {
            getContent(objectId.toObjectId())
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 获取文件的信息
     */
    private fun getContent(objectId: ObjectId?): String {
        if (objectId == null || objectId == ObjectId.zeroId()) return "" // 如果对象 ID 为空，返回空字符串
        val loader: ObjectLoader = git.repository.open(objectId)
        val outputStream = ByteArrayOutputStream()
        loader.copyTo(outputStream)
        return outputStream.toString(Charsets.UTF_8.name())
    }

    /**
     * 获取Git的存放路径
     */
    private fun getPath(): String {
        return System.getProperty("user.home") + FileUtil.FILE_SEPARATOR + ".codefuse" + FileUtil.FILE_SEPARATOR + "git-ref" + FileUtil.FILE_SEPARATOR + projectName + FileUtil.FILE_SEPARATOR + repoId + ".git"
    }

    private fun verifyLock(times : Int = 0){
        val lockFile = FileUtil.file(gitPath + FileUtil.FILE_SEPARATOR + "index.lock")
        if (null == lockFile || !lockFile.exists()){
            return
        }else if (times > 5){
            FileUtil.del(gitPath + FileUtil.FILE_SEPARATOR + "index.lock")
        }else{
            ThreadUtil.safeSleep(200)
            verifyLock(times  + 1)
        }
    }

    /**
     * 文件 Diff 信息
     */
    data class FileDiff(
        val type: String,
        val filePath: String,
        val oldCode: String,
        val newCode: String,
    )
}