package com.alipay.tsingyan.services.jgit.model

/**
 * 快照请求参数
 */
data class SnapShotRecord(
    //通用参数
    val traceId: String,
    val sessionId: String,
    var type: SnapShotType,

    //请求问题参数
    val questionId: String,
    var fileList: List<String> = emptyList(),

    //响应问题参数
    val commitId: String? = null,
    val branch: String? = null,
)

/**
 * 快照重置ID
 */
data class SnapShotReset(
    //通用参数
    val traceId: String,
    val sessionId: String,
    val targetCommitId: String,
    val currentCommitId: String = "",
    val currentBranch: String = "",
    val targetBranch: String = "",
)

/**
 * 清除快照
 */
data class SnapShotClean(
    //通用参数
    val traceId: String,
    val sessionId: String,
)

enum class SnapShotType {
    COMPOSER,
    APPLY
}