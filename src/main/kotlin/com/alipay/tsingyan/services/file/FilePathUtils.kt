package com.alipay.tsingyan.services.file

import com.alipay.tsingyan.services.completion.edit.LogUtil
import java.io.File

object FilePathUtils {

    /**
     * 获取相对路径
     *
     * @param absolutePath 绝对路径
     * @param basePath 基础路径
     * @return 相对路径
     */
    fun getRelativePath(absolutePath: String, basePath: String): String {
      try {
          if (basePath.isBlank()||absolutePath.isBlank()){
              return absolutePath
          }
          if (absolutePath.startsWith(basePath)) {
            var relativePath = absolutePath.substring(basePath.length).trimStart(File.separatorChar)
              //兼容windows
              if (relativePath.startsWith("/")){
                  relativePath = relativePath.substring(1)
              }
              return relativePath
          }
          return absolutePath

      }catch (e: Exception) {
          LogUtil.info("getRelativePath error", e)
          return absolutePath
      }

    }

}