package com.alipay.tsingyan.services.file

import cn.hutool.core.io.FileUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson2.JSONObject
import com.alipay.tsingyan.agent.bean.ReferenceBean
import com.alipay.tsingyan.model.RecentFilesInfo
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.impl.EditorHistoryManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import org.apache.http.util.TextUtils
import java.io.File

class RecentFilesService : Disposable {

    private val MAX_LINE_NUMS = 50

    override fun dispose() {
    }

    /**
     * 需要调用方保证在EDT线程中执行，且不能为 ReadAction
     */
    fun getRecentFiles(project: Project): RecentFilesInfo {
        val recentFilesInfo = RecentFilesInfo()
        try {
            // 获取当前编辑器
            val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
            val basePath = project.basePath
            if (editor != null) {
                val document = editor.document
                val caretModel = editor.caretModel
                val currentLine = caretModel.logicalPosition.line
                val startBeforeLine = maxOf(0, currentLine - MAX_LINE_NUMS)
                val totalLines = document.lineCount
                val endAfterLine = minOf(currentLine + MAX_LINE_NUMS, totalLines - 1)
                val selectedText: String? = editor.selectionModel.selectedText

                recentFilesInfo.codeBeforeCursor = getStartEndLineText(startBeforeLine, currentLine, document)
                recentFilesInfo.codeAfterCursor = getStartEndLineText(currentLine, endAfterLine, document)
                recentFilesInfo.selectContent = selectedText

                val virtualFile: VirtualFile? = FileDocumentManager.getInstance().getFile(document)
                if (virtualFile != null) {
                    if (basePath != null && virtualFile.path.startsWith(basePath)) {
                        val relativePath = FilePathUtils.getRelativePath(virtualFile.path, basePath)
                        recentFilesInfo.currentOpenFile = relativePath
                    }
                }
            }
            recentFilesInfo.recentFiles = getOpenFiles(project)
        } catch (e: Throwable) {
            LogUtil.info("getRecentFiles error", e)
        }
        return recentFilesInfo
    }

    fun getOpenFiles(project: Project): MutableList<String> {
        val openFiles: Array<VirtualFile> = FileEditorManager.getInstance(project).openFiles
        val openFilesRelativePaths = mutableListOf<String>()
        // 计算每个文件的相对路径
        for (file in openFiles) {
            val absolutePath = file.path
            if (absolutePath.startsWith(project.basePath!!)) {
                val relativePath = FilePathUtils.getRelativePath(file.path, project.basePath!!)
                openFilesRelativePaths.add(relativePath)
            }
        }
        return openFilesRelativePaths
    }

    private fun getStartEndLineText(startLine: Int, endLine: Int, document: Document): String {
        val stringBuilder = StringBuilder()
        for (line in startLine..endLine) {
            val lineStartOffset = document.getLineStartOffset(line)
            val lineEndOffset = document.getLineEndOffset(line)
            val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
            stringBuilder.append(lineText).append("\n")
            stringBuilder.append("\n")
        }
        return stringBuilder.toString()
    }

    fun getReferenceFileContent(referenceList: String, project: Project): MutableList<ReferenceBean>? {
        var referenceFileInfoList = mutableListOf<ReferenceBean>()
        try {
            referenceFileInfoList = JSON.parseArray(referenceList, ReferenceBean::class.java)
            referenceFileInfoList.forEach { referenceBean ->
                if (referenceBean.type.equals("FILE") && !TextUtils.isEmpty(referenceBean.url)) {
                    //此处url为相对路径 需要转换为绝对路径
                    val virtualFile = if (!referenceBean.url!!.startsWith(project.basePath.toString())) {
                        LocalFileSystem.getInstance()
                            .findFileByPath(project.basePath + File.separatorChar + referenceBean.url!!)
                    } else {
                        LocalFileSystem.getInstance().findFileByPath(referenceBean.url!!)
                    }
                    ApplicationManager.getApplication().runReadAction {
                        val document = FileDocumentManager.getInstance().getDocument(virtualFile!!)
                        if (document != null) {
                            referenceBean.content = document.text
                        }
                    }
                }
            }
        } catch (e: Throwable) {
            LogUtil.info("getReferenceFileContent error", e)
        }
        return referenceFileInfoList
    }

    /**
     * 获取文件信息
     */
    fun getFileContentByUrl(url: String, project: Project): Pair<String,Int> {
        //此处url为相对路径 需要转换为绝对路径
        val virtualFile = VfsUtils.getLocalVfsFile(url, project) ?: return "" to 0
        var content = ""
        var lineNumber = 0
        runReadAction {
            val document = FileDocumentManager.getInstance().getDocument(virtualFile)
            if (document != null) {
                content = document.text
                lineNumber = document.lineCount
            }
        }
        return content to lineNumber
    }

    fun parseRecentFileParam(msg: String?): RecentFileParam? {
        return if (null == msg) {
            RecentFileParam()
        } else {
            try {
                JSONObject.parseObject(msg, RecentFileParam::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }

    /**
     * 获取最近的相对文件列表
     */
    fun getFileRelativePath(project: Project, limit: Int = 10, onlyProjectFile: Boolean = true): List<String> {
        if (limit < 0) {
            return emptyList()
        }
        val basePath = VfsUtils.getProjectBaseDir(project) + FileUtil.FILE_SEPARATOR
        val editorHistoryManager = EditorHistoryManager.getInstance(project)
        // 最近打开的放最前面
        val reversedFile = editorHistoryManager.files.reversed()
        val result = if (onlyProjectFile) {
            reversedFile.filter { it.path.startsWith(basePath) }
        } else {
            reversedFile
        }.take(limit).map { it.path.removePrefix(basePath) }
        return result
    }

    /**
     * 获取当前编辑器内选中的文件
     */
    fun getSelectEditorFile(project: Project): String? {
        val editorFile = FileEditorManager.getInstance(project).selectedEditor?.file ?: return null
        if (editorFile.path.startsWith(VfsUtils.getProjectBaseDir(project))) {
            return VfsUtils.getRelativeFilePath(editorFile, project)
        }
        return null
    }


    data class RecentFileParam(
        /**
         * 获取的文件个数
         */
        val limit: Int = 10,
        /**
         * 是否只包含项目文件
         */
        val onlyProjectFile: Boolean = true,
    )

}