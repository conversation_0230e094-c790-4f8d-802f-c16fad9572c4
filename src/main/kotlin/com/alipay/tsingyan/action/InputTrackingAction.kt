//package com.alipay.tsingyan.action
//
//import com.alipay.tsingyan.services.input.InputTrackingService
//import com.intellij.openapi.actionSystem.AnAction
//import com.intellij.openapi.actionSystem.AnActionEvent
//import com.intellij.openapi.components.ServiceManager
//import com.intellij.openapi.fileChooser.FileChooserDescriptorFactory
//import com.intellij.openapi.fileChooser.FileChooserDialog
//import com.intellij.openapi.fileChooser.FileChooserFactory
//import com.intellij.openapi.project.Project
//import com.intellij.openapi.ui.Messages
//import com.intellij.openapi.vfs.VirtualFile
//import java.io.File
//
///**
// * 输入追踪控制Action
// *
// * <AUTHOR>
// */
//class InputTrackingAction : AnAction("Toggle Input Tracking") {
//
//    override fun actionPerformed(e: AnActionEvent) {
//        val project = e.project ?: return
//        val inputTrackingService = ServiceManager.getService(project, InputTrackingService::class.java)
//
//        if (inputTrackingService.isTrackingEnabled()) {
//            // 如果正在追踪，则停止追踪并导出数据
//            stopTrackingAndExport(project, inputTrackingService)
//        } else {
//            // 如果未追踪，则开始追踪
//            startTracking(project, inputTrackingService)
//        }
//    }
//
//    override fun update(e: AnActionEvent) {
//        val project = e.project
//        if (project == null) {
//            e.presentation.isEnabled = false
//            return
//        }
//
//        val inputTrackingService = ServiceManager.getService(project, InputTrackingService::class.java)
//        val isTracking = inputTrackingService.isTrackingEnabled()
//
//        e.presentation.text = if (isTracking) {
//            "Stop Input Tracking (${inputTrackingService.getActiveListenerCount()} editors)"
//        } else {
//            "Start Input Tracking"
//        }
//
//        e.presentation.description = if (isTracking) {
//            "Stop tracking input changes and export data"
//        } else {
//            "Start tracking input changes in all editors"
//        }
//    }
//
//    private fun startTracking(project: Project, service: InputTrackingService) {
//        // 询问用户是否要指定输出文件
//        val choice = Messages.showYesNoCancelDialog(
//            project,
//            "Do you want to specify an output file for real-time export?\n\n" +
//                    "Yes: Choose output file location\n" +
//                    "No: Save data in memory only (you can export later)\n" +
//                    "Cancel: Don't start tracking",
//            "Input Tracking Configuration",
//            "Choose File",
//            "Memory Only",
//            "Cancel",
//            Messages.getQuestionIcon()
//        )
//
//        when (choice) {
//            Messages.YES -> {
//                // 选择输出文件
//                val outputFile = chooseOutputFile(project)
//                if (outputFile != null) {
//                    service.enableTracking(outputFile)
//                    Messages.showInfoMessage(
//                        project,
//                        "Input tracking started.\nReal-time export to: $outputFile",
//                        "Tracking Started"
//                    )
//                }
//            }
//            Messages.NO -> {
//                // 仅在内存中记录
//                service.enableTracking()
//                Messages.showInfoMessage(
//                    project,
//                    "Input tracking started.\nData will be stored in memory.",
//                    "Tracking Started"
//                )
//            }
//            // Messages.CANCEL or other -> do nothing
//        }
//    }
//
//    private fun stopTrackingAndExport(project: Project, service: InputTrackingService) {
//        val changeCount = service.getAllInputChanges().size
//
//        if (changeCount == 0) {
//            service.disableTracking()
//            Messages.showInfoMessage(
//                project,
//                "Input tracking stopped.\nNo changes were recorded.",
//                "Tracking Stopped"
//            )
//            return
//        }
//
//        // 询问是否导出数据
//        val choice = Messages.showYesNoDialog(
//            project,
//            "Input tracking will be stopped.\n\n" +
//                    "Recorded ${changeCount} input changes.\n" +
//                    "Do you want to export the data to a JSON file?",
//            "Export Input Changes",
//            "Export",
//            "Discard",
//            Messages.getQuestionIcon()
//        )
//
//        service.disableTracking()
//
//        if (choice == Messages.YES) {
//            try {
//                val outputFile = chooseOutputFile(project)
//                if (outputFile != null) {
//                    val exportedPath = service.exportToJson(outputFile)
//                    Messages.showInfoMessage(
//                        project,
//                        "Input tracking stopped.\n\n" +
//                                "Exported ${changeCount} changes to:\n$exportedPath",
//                        "Data Exported"
//                    )
//                }
//            } catch (e: Exception) {
//                Messages.showErrorDialog(
//                    project,
//                    "Failed to export input changes:\n${e.message}",
//                    "Export Failed"
//                )
//            }
//        } else {
//            Messages.showInfoMessage(
//                project,
//                "Input tracking stopped.\n\n" +
//                        "${changeCount} changes were discarded.",
//                "Tracking Stopped"
//            )
//        }
//
//        // 清空数据
//        service.clearInputChanges()
//    }
//
//    private fun chooseOutputFile(project: Project): String? {
//        val descriptor = FileChooserDescriptorFactory.createSingleFileDescriptor("json")
//        descriptor.title = "Choose Output File for Input Changes"
//        descriptor.description = "Select where to save the input changes JSON file"
//        descriptor.withShowHiddenFiles(false)
//
//        val chooser: FileChooserDialog = FileChooserFactory.getInstance().createFileChooser(descriptor, project, null)
//
//        // 设置默认文件名
//        val defaultFile = File(project.basePath ?: System.getProperty("user.home"), "input_changes.json")
//        val defaultVirtualFile = com.intellij.openapi.vfs.LocalFileSystem.getInstance().findFileByIoFile(defaultFile.parentFile)
//
//        val selectedFiles: Array<VirtualFile> = chooser.choose(project, defaultVirtualFile)
//
//        return if (selectedFiles.isNotEmpty()) {
//            var path = selectedFiles[0].path
//            if (!path.endsWith(".json")) {
//                path += ".json"
//            }
//            path
//        } else {
//            null
//        }
//    }
//}