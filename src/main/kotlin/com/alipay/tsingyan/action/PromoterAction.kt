package com.alipay.tsingyan.action

import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.newcompletion.action.LineApplyInlayAction
import com.intellij.openapi.actionSystem.ActionPromoter
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.util.containers.toMutableSmartList

/**
 * 改变时间冲突时的顺序
 * <AUTHOR>
 * @date 2024-07-16 20:02:58
 *
 * 当用户触发操作时，IDE 会调用 ActionPromoter 的 promote 方法。这个方法会收到当前上下文中的所有候选操作，
 * 并允许你返回一个新的操作列表，以指定哪些操作应该被优先考虑。IDE 将会按照你返回的顺序处理这些操作。
 *
 * 通过实现 ActionPromoter，你可以在复杂的上下文中确保你的操作能够被优先处理，从而为用户提供更好的体验。
 *
 * 使用场景
 * 特定文件类型：在特定文件类型（如自定义语言文件）中，优先处理你的操作。
 * 特定编辑器状态：在某种特定编辑器状态（如焦点在特定 UI 元素上）下，优先处理你的操作。
 * 冲突处理：当多个操作有相同的快捷键时，优先处理你定义的操作。
 */
class PromoterAction : ActionPromoter{
    val editorManagerService = service<EditorManagerService>()

    override fun promote(actions: MutableList<out AnAction>, context: DataContext): MutableList<AnAction>? {
        if (actions == null) {
            return super.promote(actions, context)
        }

        if (context == null) {
            return super.promote(actions, context)
        }

        // 保证 TabActiveAction 顺序为第一位
        if (isContainsTabActiveAction(actions)){
            val result = actions.toMutableSmartList()
            result.sortBy { it !is TabActiveAction }
            return result
        }

        if (this.isValidEditor(CommonDataKeys.EDITOR.getData(context) as Editor?)) {
            return null
        } else if (actions.stream().noneMatch { action: AnAction? -> action is LineApplyInlayAction && action is EditorAction }) {
            return null
        } else {
            val result = ArrayList(actions)
            result.sortWith { a, b ->
                val aOk = isApplyByLineAction(a)
                val bOk = isApplyByLineAction(b)
                if (aOk) {
                    return@sortWith -1
                }
                if (bOk) {
                    return@sortWith 1
                }
                0
            }

            LogUtil.debug("promote actions: $result")
            return result
        }
    }

    private fun isValidEditor(editor: Editor?): Boolean {
        return editor == null || !editorManagerService.isAvailable(editor)
    }

    private fun isApplyByLineAction(action: AnAction): Boolean {
        return (action is LineApplyInlayAction) && (action is EditorAction)
    }

    /**
     * 针对唤起侧边栏的快捷键
     */
    private fun isContainsTabActiveAction(actions: MutableList<out AnAction>): Boolean{
        return null !=  actions.firstOrNull { it is TabActiveAction }
    }
}