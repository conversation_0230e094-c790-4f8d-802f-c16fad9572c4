package com.alipay.tsingyan.action

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.TABKey
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.ChatMessageUtil
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ui.UIUtils
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.UpdateInBackground

class TabActiveAction : AnAction(), UpdateInBackground {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        UIUtils.switchToolWindowTab(project, TABKey.AI_PARTNER)
        try {
            val selectedCodeInfo = CommonUtils.getSelectedCodeInfo()
            if (selectedCodeInfo.isNullOrEmpty()){
                return
            }
            val question = selectedCodeInfo.get("code")?.toString() ?: ""
            
            // 使用通用函数发送CHAT_DATA_V2消息
            ChatMessageUtil.sendChatDataV2MessageWithCode(
                project,
                question,
                AppConstant.POSE_AS_SESSION,
                ClickType.SHORT_CUT,
                selectedCodeInfo
            )
        } catch (e: Exception){
            LogUtil.info("OPEN_WINDOW error ${e.message}", false)
        }
    }
}