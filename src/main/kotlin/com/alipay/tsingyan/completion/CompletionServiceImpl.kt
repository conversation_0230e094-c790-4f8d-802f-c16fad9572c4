package com.alipay.tsingyan.completion

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.BuildInfo
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.model.completion.CompletionRequestBean
import com.alipay.tsingyan.model.completion.CompletionResultModel
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.AppConstant
import com.intellij.openapi.components.service

/**
 * 代码补全逻辑
 * <AUTHOR>
 * 2024-03-07 16:01:35
 */
class CompletionServiceImpl :CompletionService{

    private var agentServiceImpl: AgentService = service<AgentService>()
    private var agentCompletion: CompletionStrategy = AgentCompletion()
    private var networkCompletion: CompletionStrategy = NetworkCompletion()
    @Volatile
    private var lastAgentErrorTimeStamp = 0L
    @Volatile
    private var codeModelListEmptyCount = 0

    interface CompletionStrategy {
        fun requestCompletion(completionRequestBean: CompletionRequestBean):CompletionResultModel?
    }

    // 本地Agent的补全逻辑
    class AgentCompletion : CompletionStrategy {
        private var agentServiceImpl: AgentService = service<AgentService>()
        override fun requestCompletion(completionRequestBean: CompletionRequestBean):CompletionResultModel? {
            // 具体的补全逻辑
            return agentServiceImpl.completion(completionRequestBean)
        }
    }

    // 兜底逻辑
    class NetworkCompletion : CompletionStrategy {
        private var tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()

        override fun requestCompletion(completionRequestBean: CompletionRequestBean):CompletionResultModel? {
            return tsingYanProdService.completion(completionRequestBean)
        }
    }

    override fun completion(completionRequestBean: CompletionRequestBean): CompletionResultModel? {
        val shouldUseAgentCompletion = shouldUseAgentCompletion()
        val primaryCompletion = if (shouldUseAgentCompletion) agentCompletion else networkCompletion
        LogUtil.info("CompletionService shouldUseAgentCompletion $shouldUseAgentCompletion", false)

        var completionResultModel = primaryCompletion.requestCompletion(completionRequestBean)
        if (shouldUseAgentCompletion) {
            completionResultModel = handleAgentCompletionResult(completionRequestBean, completionResultModel)
        }
        LogUtil.info("CompletionService final completion ${JSON.toJSONString(completionResultModel)}", false)
        return completionResultModel
    }

    override fun completionCodeEdits(completionRequestBean: CompletionRequestBean): CompletionResultModel? {
        val shouldUseAgentCompletion = shouldUseAgentCompletion()
        if (shouldUseAgentCompletion){
            val completionResult = agentCompletion.requestCompletion(completionRequestBean)
            return completionResult
        }
        return null
    }

    private fun shouldUseAgentCompletion(): Boolean {
//        if (BuildInfo.isAgentDebug){
//            return true
//        }
        val timeSinceLastError = System.currentTimeMillis() - lastAgentErrorTimeStamp
        return agentServiceImpl.isAgentFinishInit() && timeSinceLastError > AppConstant.agentRetryTime && !agentServiceImpl.isAgentClosed()
    }

    private fun handleAgentCompletionResult(
        completionRequestBean: CompletionRequestBean,
        initialResult: CompletionResultModel?
    ): CompletionResultModel? {
        var result = initialResult
        if (result == null) {
            LogUtil.info("CompletionService handleAgentCompletionResult result is null ", false)
            lastAgentErrorTimeStamp = System.currentTimeMillis()
            result = networkCompletion.requestCompletion(completionRequestBean)
            // 去检查是否活着着
            agentServiceImpl.checkIsAgentClosed()
        } else {
            if (result.codeModelList.isNullOrEmpty()) {
                LogUtil.info("CompletionService codeModelListEmptyCount codeModelListEmptyCount $codeModelListEmptyCount", false)
                if (codeModelListEmptyCount++ >= 5) {
                    lastAgentErrorTimeStamp = System.currentTimeMillis()
                    result = networkCompletion.requestCompletion(completionRequestBean)
                }
            } else {
                lastAgentErrorTimeStamp = 0L
                codeModelListEmptyCount = 0
            }
        }
        return result
    }

//    private fun mockData(): CompletionResultModel? {
//        var mockStr =
//            "{\"jsonrpc\":\"2.0\",\"result\":{\"errorCode\":0,\"errorMsg\":null,\"data\":{\"sessionId\":\"57f53704-f9ea-43c9-8b7f-57ec88a58bc7\",\"codeModelList\":[{\"id\":1022936652455989248,\"sessionId\":\"57f53704-f9ea-43c9-8b7f-57ec88a58bc7\",\"content\":\"Cleaning up\\\", \\\"Cleaning up...\\\", true) {\"}]}},\"id\":\"57f53704-f9ea-43c9-8b7f-57ec88a58bc7\"}"
//        val parseObject: JSONObject? = JSON.parseObject(mockStr)
//        val resultStr: String? = parseObject?.getString("result")
//        if (!TextUtils.isEmpty(resultStr)) {
//            val resultObj =
//                JSONObject.parseObject(resultStr, object : TypeReference<BaseResponse<CompletionResultModel>>() {})
//            if (resultObj.errorCode != 0 || resultObj.data == null) {
//                LogUtil.info("AgentService completion error,return: $resultStr")
//                return null
//            }
//            return resultObj.data!!
//        }
//        return null
//    }
}