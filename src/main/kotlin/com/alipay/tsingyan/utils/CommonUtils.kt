package com.alipay.tsingyan.utils

import cn.hutool.core.codec.Base64Encoder
import cn.hutool.core.io.file.FileReader
import cn.hutool.core.net.NetUtil
import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.core.util.StrUtil
import cn.hutool.core.util.SystemPropsUtil
import cn.hutool.crypto.asymmetric.KeyType
import cn.hutool.system.SystemUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.model.completion.CommonRequestBean
import com.alipay.tsingyan.model.enums.*
import com.alipay.tsingyan.model.request.UserInfoRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.UIThemeListener
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.ui.config.UserState
import com.alipay.tsingyan.util.CommandLineUtil
import com.alipay.tsingyan.utils.AppConstant.SECURE_KEY
import com.alipay.tsingyan.view.jcef.common.model.RuntimeInfo
import com.alipay.tsingyan.view.jcef.socket.util.SocketUtil
import com.intellij.diff.util.LineRange
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.ide.DataManager
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.fileEditor.impl.FileEditorManagerImpl
import com.intellij.openapi.keymap.KeymapManager
import com.intellij.openapi.keymap.KeymapUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.util.AsyncResult
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.KeyWithDefaultValue
import com.intellij.openapi.util.SystemInfoRt
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.util.text.StringUtil
import com.intellij.openapi.vcs.changes.Change
import com.intellij.openapi.vcs.changes.ChangeListManager
import com.intellij.openapi.vfs.VirtualFile
import io.ktor.utils.io.preventFreeze
import org.apache.commons.lang3.StringUtils
import org.apache.http.util.TextUtils
import java.awt.Desktop
import java.io.File
import java.net.URI
import java.nio.file.Paths
import kotlin.Pair
import kotlin.math.max
import kotlin.math.min


/**
 * 通用工具函数
 */
object CommonUtils {
    private val LOGGER: Logger = Logger.getInstance(CommonUtils::class.java)
    val isCloudIDE = null != SystemUtil.get("HOSTNAME")
    var ideaVersion: Int = 0
//    val isCloudIDE = true

    // MCP功能支持缓存
    private var mcpFeatureSupportCache: Boolean? = null

    // 版本检查缓存
    private var is222OrAboveCache: Boolean? = null
    
    // Branch缓存数据结构
    private data class BranchCacheData(
        val branch: String,
        val timestamp: Long
    )
    
    // Branch缓存Map，使用project的basePath作为key
    private val branchCache = mutableMapOf<String, BranchCacheData>()

    /**
     * 青燕prod远程服务
     */
    var tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()

    /**
     * 本地存储用户信息服务
     */
    var localUserStore: LocalUserStore = service<LocalUserStore>()

    val settingData = service<TsingYanSettingStore>()

    private val KEY_TIPS: Key<String> = KeyWithDefaultValue.create("Ant.acceptTips", "")

    /**
     * 跳转到{@link url}网页
     */
    fun openUrl(url: String) {
        try {
            val desktop = Desktop.getDesktop()
            if (Desktop.isDesktopSupported() && desktop.isSupported(Desktop.Action.BROWSE)) {
                desktop.browse(URI(url))
            }
        } catch (e: Throwable) {
//            EventTrackManager.addEventTrack(EventTrackDataBuilder.ExceptionReportEvent(e).build())
        }
    }

    /**
     * 查询当前用户token并打开网页
     */
    fun queryTokenAndOpenUrl(url: String) {
        var userState = localUserStore.state
        var userToken = localUserStore.getUserInfoModel()?.userToken
        if (userToken == null) {
            if (userState == null) {
                userState = UserState()
            }
            val userInfoRequestBean = UserInfoRequestBean()
            userInfoRequestBean.ideVersion = getIdeVersion()
            userInfoRequestBean.pluginVersion = getPluginVersion(AppConstant.PLUGIN_VERSION)
            userInfoRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
            val userInfo = tsingYanProdService.queryUserInfo(userInfoRequestBean) ?: return
            localUserStore.setUserInfoModel(userState, userInfo.userInfoModel)
            userState.queueNum = userInfo.queueNum
            localUserStore.setUserState(userState)

        }
//        val url = "https://yuque.antfin.com/roi-doc/codefuse/idea-plugin"
        openUrl(url)
    }

    fun isSelectedEditor(editor: Editor): Boolean {
        var editorManager: FileEditorManager? = null
        if (editor == null) {
            false
        }
        val project = editor.project
        if (project == null || project.isDisposed || FileEditorManager.getInstance(project).also {
                editorManager = it
            } == null) {
            return false
        }
        if (editorManager == null) {
            return false
        }
        if (editorManager is FileEditorManagerImpl) {
            val current = (editorManager as FileEditorManagerImpl).getSelectedTextEditor(true)
            return current != null && current == editor
        }
        val current2 = editorManager!!.selectedEditor
        return current2 is TextEditor && editor == current2.editor
    }

    /**
     * 参数加密
     */
    fun encryptParam(param: String): String {
        return SECURE_KEY.encryptBase64(param, KeyType.PublicKey)
    }

    /**
     * 请求签名
     */
    fun signReq(commonRequestBean: CommonRequestBean): String {
        val stringBuilder = StringBuilder()
        val serviceName = (if (commonRequestBean.serviceName == null) "" else commonRequestBean.serviceName)
        val paramToken = (if (commonRequestBean.paramToken == null) "" else commonRequestBean.paramToken)
        stringBuilder.append(commonRequestBean.requestTime).append(serviceName).append(paramToken).toString()
        return Base64Encoder.encode(stringBuilder.toString())
    }


    /**
     * 获取插件版本
     * 如果过去版本号过程中出现异常，返回默认值
     * @param defaultVersion: 插件默认值
     */
    fun getPluginVersion(defaultVersion: String): String {
        try {
            val version: String

            version = if (isCloudIDE){
                val pluginId = PluginId.getId(AppConstant.PLUGIN_ID)
                PluginManager.getPlugin(pluginId)?.version.toString()
            } else {
                PluginManager.getInstance().findEnabledPlugin(PluginId.findId(AppConstant.PLUGIN_ID)!!)!!.version
            }

            if (version == null || version.trim().length == 0) {
                return defaultVersion
            }
            return version
        } catch (e: NoSuchMethodError) {
            return PluginManager.getInstance().findEnabledPlugin(PluginId.getId(AppConstant.PLUGIN_ID)!!)!!.version
        } catch (e: Throwable) {
            return defaultVersion
        }
    }

    fun getDefaultPluginVersion() : String{
        return getPluginVersion(AppConstant.PLUGIN_VERSION)
    }

    /**
     * 获取idea版本号
     */
    fun getIdeVersion(): String {
        return ApplicationInfo.getInstance().apiVersion
    }

    /**
     * 获取用户token
     */
    fun getUserToken(localUserStore: LocalUserStore, tsingYanProdService: TsingYanProdService): String {
        var userState = localUserStore.state
        if (localUserStore.getUserInfoModel() != null && localUserStore.getUserInfoModel()!!.userToken != null) {
            return localUserStore.getUserInfoModel()!!.userToken!!
        }
        val userInfoRequestBean = UserInfoRequestBean()
        userInfoRequestBean.ideVersion = getIdeVersion()
        userInfoRequestBean.pluginVersion = getPluginVersion(AppConstant.PLUGIN_VERSION)
        if (userState == null) {
            userState = UserState()
        }
        userInfoRequestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        val userInfo = tsingYanProdService.queryUserInfo(userInfoRequestBean) ?: return ""
        localUserStore.setUserInfoModel(userState, userInfo?.userInfoModel)
        if (userInfo != null) {
            userState.queueNum = userInfo.queueNum
        }
        localUserStore.setUserState(userState)
        return userInfo?.userInfoModel?.userUid!!
    }

    /**
     * 获取默认的用户 Token
     */
    fun getDefaultToken() : String{
        return getUserToken(service<LocalUserStore>(), service<TsingYanProdService>())
    }

    val htmlTemplage = "<!DOCTYPE html>\n" +
            "<html lang=\"en\" style=\"width: 100%; height: 100%;\">\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
            "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
            "</head>\n" +
            "<body style=\"margin: 0; overflow: hidden; width: 100%; height: 100%\">\n" +
            "<iframe\n" +
            "        src=\"{url}\"\n" +
            "        allow=\"clipboard-read; clipboard-write \"\n" +
            "        frameborder=\"0\"\n" +
            "        sandbox=\"allow-same-origin allow-pointer-lock allow-scripts allow-downloads allow-forms allow-popups\"\n" +
            "        style=\"display: block; margin: 0px; overflow: hidden; position: absolute; width: 100%; height: 100%; visibility: visible;\">\n" +
            "</iframe>\n" +
            "</body>\n" +
            "</html>"


    /**
     * 包装Url到Iframe中
     */
    fun generateHtml(url: String): String = htmlTemplage.replaceFirst("{url}", url)

    /**
     * 获取URL
     */
    fun generateUrl(
        url: String,
        token: String,
        port: Int? = null,
    ) : String{
        val builder = UrlBuilder
            .of(url)
            .addQuery("tag", getPluginVersion(""))  // 当版本获取失败时，默认使用最新的前端版本
            .addQuery("theme", UIThemeListener.getTheme().theme)
            .addQuery("originName", UIThemeListener.getThemeNameByUrlEncode())
            .addQuery("clientId", service<TsingYanSettingStore>().state.CLIENT_ID)
            .addQuery("token", token)

        // 仅仅 CloudIDE 做判断
        if(isCloudIDE){
            builder.addQuery("honDomain", StrUtil.nullToDefault(SystemPropsUtil.get("HON_DOMAIN",true), AppConstant.HON_DOMAIN))
        }

        port?.let {
            builder.addQuery("port",port)
        }
        builder.addQuery("ip",getIp())
        // 新的消息通道，稳定后，应全切换到这个上
        builder.addQuery("aPort",SocketUtil.getPort())

        val requestURL = builder.build()
        LogUtil.info("requestURL $requestURL")
        return requestURL
    }


    /**
     * 生成一个可用的端口
     * 7000-8000
     *
     * @return
     */
    fun getAvailablePort(): Int {
        return NetUtil.getUsableLocalPort(7000, 13000)
    }

    fun getIp(): String? {
        return if (isCloudIDE) {
            System.getenv("RequestedIP")
//            "127.0.0.1"
        } else {
            "127.0.0.1"
        }
    }

    /**
     * 解析CloudIde环境信息
     *
     * @return
     */
    fun parseRunTimeInfo(): RuntimeInfo? {
        var runtimeInfo: RuntimeInfo? = null
        try {
            val fileReader = FileReader("~/.ide/runtime.json")
            val result: String = fileReader.readString()
            if (StringUtils.isNotBlank(result)) {
                runtimeInfo = JSONObject.parseObject(result, RuntimeInfo::class.java)
            }
        } catch (e: Exception) {
            LOGGER.info("tsingyan cloudIDE环境未查询到 runtime.json 数据")
        }
        return runtimeInfo
    }


    /**
     * 是否显示用户首次的补全提示
     */
    fun isShowAcceptTips(): Boolean {
        LOGGER.debug("showTips accepted Count : ${settingData.state.ACCEPTED_COUNT}")
        return settingData.state.ACCEPTED_COUNT < AppConstant.COMPLETION_CONFIG.codeAcceptTipsMax
    }

    /**
     * 获取提示信息
     * @param editor
     */
    fun getTips(editor: Editor): String{
        if (TextUtils.isEmpty(KEY_TIPS.get(editor))) {
            var cmdStr = "Cmd + Down"
            val acceptStr = "Tab"
            if (isCloudIDE) {
                if (getKeyMapName() == "windows") {
                    cmdStr = "Ctrl + Down"
                }
            } else {
                if (SystemInfoRt.isWindows) {
                    cmdStr = "Ctrl + Down"
                }
            }
            val tipStr = "          " + cmdStr + " " + AppConstant.ACCEPT_LINE_TIPS + "; " + acceptStr + " " + AppConstant.ACCEPT_ALL_TIPS
            KEY_TIPS.set(editor, tipStr)
        }

        return KEY_TIPS.get(editor)
    }


    private fun getShortcutText(actionId: String?, defaultText: String?): String {
        return StringUtil.defaultIfEmpty(
            KeymapUtil.getFirstKeyboardShortcutText(
                ActionManager.getInstance().getAction(
                    actionId!!
                )
            ), defaultText
        )
    }

    private fun replaceSpecialCharacterWithTab(input: String): String {
        return buildString {
            for (char in input) {
                if (char.code == 8677) {
                    append("Tab") // 替换为 Tab
                } else {
                    append(char) // 保持原字符
                }
            }
        }
    }

    private fun getKeyMapName(): String {
        val keymapName = KeymapManager.getInstance().activeKeymap.presentableName
        LogUtil.info("cloudIDE keymapName $keymapName", false)
        if (keymapName != null && keymapName.contains("Win")){
            return "windows"
        }
        return "mac"
    }



    /**
     * 获取当前project的远程git地址
     */
    fun getProjectGitRepository(project: Project): String {
        try {
            val projectBaseDir = project.basePath ?: return ""
            val gitConfigFile = Paths.get(projectBaseDir, ".git", "config").toFile()

            if (!gitConfigFile.exists()) {
                LOGGER.info("repoStr null")
                return ""
            }

            val regex = "\\[remote \"origin\"\\][\\s\\S]*?url = (.+?)\\n".toRegex()
            val configText = gitConfigFile.readText()

            val matchResult = regex.find(configText)
            val repoStr = matchResult?.groups?.get(1)?.value?.trim() ?: return ""
            return repoStr
        }catch (e:Throwable){
            LOGGER.warn("getProjectGitRepository error", e)
        }
        return ""
    }

    fun getBranch(project: Project): String {
        val projectBaseDir = project.basePath ?: return ""
        val currentTime = System.currentTimeMillis()
        
        // 检查缓存是否存在且有效（5秒内）
        val cachedData = branchCache[projectBaseDir]
        if (cachedData != null && (currentTime - cachedData.timestamp) < 3*1000) {
            return cachedData.branch
        }
        
        // 缓存无效或不存在，重新读取文件
        val gitHeadFile = Paths.get(projectBaseDir, ".git", "HEAD").toFile()
        val branch = if (gitHeadFile.exists()) {
            val headContent = gitHeadFile.readText().trim()
            if (headContent.startsWith("ref:")) {
                headContent.split("/").last()
            } else {
                ""
            }
        } else {
            ""
        }
        
        // 更新缓存
        branchCache[projectBaseDir] = BranchCacheData(branch, currentTime)
        
        return branch
    }

    fun getLatestCommitId(project: Project?): String? {
        if (project == null || project.isDisposed) {
            return ""
        }
        val changeListManager = ChangeListManager.getInstance(project!!)
        val changes: MutableCollection<Change> = changeListManager.allChanges
        if (changes.isEmpty()) {
            return ""
        }
        return changes.first().beforeRevision?.revisionNumber?.asString()
    }

    fun detectOsType(): OsTypeEnum {
        val osName = System.getProperty("os.name").toLowerCase()
        LOGGER.info("osName $osName")
        return when {
            osName.contains("win") -> OsTypeEnum.WIN
            osName.contains("mac") -> OsTypeEnum.MAC
            osName.contains("linux") || osName.contains("unix") -> OsTypeEnum.LINUX
            else -> OsTypeEnum.UNKNOWN
        }
    }

    fun detectOsArch(): OsArchEnum {
        var osArch = ""
        if (detectOsType() != OsTypeEnum.WIN) {
            val output = CommandLineUtil.executeCommand(arrayListOf("bash", "-c", "uname -m"), 3 * 1000)
            if (output?.exitCode == 0) {
                LogUtil.info("Successfully uname -m  ${output.stdout}")
                if (!TextUtils.isEmpty(output.stdout)) {
                    osArch = output.stdout.trim().toLowerCase()
                }
            }
        }

        if (TextUtils.isEmpty(osArch)){
            osArch = System.getProperty("os.arch").toLowerCase()
        }

        LOGGER.info("osArch $osArch")
        return when {
            (osArch.contains("x86") && osArch.contains("64")) -> OsArchEnum.X86_64
            (osArch.startsWith("arm") || osArch.contains("aarch")) && osArch.endsWith("64") -> OsArchEnum.ARM64
            else -> OsArchEnum.UNKNOWN
        }
    }

    fun deleteDirectory(directoryToBeDeleted: File): Boolean {
        val allContents = directoryToBeDeleted.listFiles()
        if (allContents != null) {
            for (file in allContents) {
                deleteDirectory(file)
            }
        }
        return directoryToBeDeleted.delete()
    }

    fun is241Version(): Boolean {
        val parts = ApplicationInfo.getInstance().apiVersion.split("-")
        val buildNumberStr = parts.getOrNull(1)?.split(".")?.getOrNull(0)
        val buildNumber = buildNumberStr?.toIntOrNull()
        LogUtil.info("is241Version $buildNumber ", false)

        return buildNumber?.let {
            it >= 241
        } ?: false
    }

    fun is222OrAboveVersion(): Boolean {
        // 如果已经缓存过，直接返回缓存结果
        is222OrAboveCache?.let { return it }
        
        // 首次计算并缓存结果
        val parts = ApplicationInfo.getInstance().apiVersion.split("-")
        val buildNumberStr = parts.getOrNull(1)?.split(".")?.getOrNull(0)
        val buildNumber = buildNumberStr?.toIntOrNull()
        LogUtil.info("is222OrAboveVersion $buildNumber ", false)

        val result = buildNumber?.let {
            it >= 222
        } ?: false
        
        is222OrAboveCache = result
        return result
    }

    /**
     * 检查是否支持MCP功能
     * 仅当IDEA版本大于等于2022.2(222)时才支持
     * 使用缓存机制，避免重复计算
     * @return true 支持MCP功能，false 不支持MCP功能
     */
    fun isSupportMcpFeature(): Boolean {
        // 如果已经缓存过，直接返回缓存结果
        mcpFeatureSupportCache?.let { return it }
        
        // 首次计算并缓存结果
        val result = is222OrAboveVersion()
        mcpFeatureSupportCache = result
        LogUtil.info("isSupportMcpFeature calculated and cached: $result", false)
        return result
    }

    fun is211Version(): Boolean {
        return 211 == getBigVersion()
    }

    /**
     * 获取IDEA的大版本信息
     */
    fun getBigVersion(): Int{
        //允许获取失败，获取失败时，下次再重复获取
        try {
            if (ideaVersion == 0){
                val parts = ApplicationInfo.getInstance().apiVersion.split("-")
                val buildNumberStr = parts.getOrNull(1)?.split(".")?.getOrNull(0)
                ideaVersion = buildNumberStr?.toIntOrNull() ?: 0
                LogUtil.info("isTargetVersion $ideaVersion ")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } catch (t: Throwable) {
            t.printStackTrace()
        }
        return ideaVersion
    }

//    /**
//     * 有冲突的插件
//     */
//    val CONFLICT_PLUGIN = "org.jetbrains.completion.full.line"
//
//    /**
//     * 解决插件冲突(临时方案）
//     * 此方法一定优先运行，防止CODEGENCORE_CORE_SERVICE_CLASS被加载后无法修改
//     */
//    fun disableConflictPlugin() {
////        val contextClassLoader = Thread.currentThread().contextClassLoader
//        PortUtil.setEnv("flcc_enable", "false")
//        PortUtil.setEnv("flcc_evaluating", "true")
//        val flcc_enable = System.getenv("flcc_enable")
//        val flcc_evaluating = System.getenv("flcc_evaluating")
//        LogUtil.info("disableConflictPlugin 1 pluginClassLoader $flcc_enable $flcc_evaluating")
//        try {
//            val pluginId = PluginId.findId(CONFLICT_PLUGIN) ?: return
//            LogUtil.info("disableConflictPlugin 2 pluginClassLoader $pluginId")
//            val linkIdePlugin = PluginManager.getInstance().findEnabledPlugin(pluginId)
//            LogUtil.info("disableConflictPlugin 3 pluginClassLoader $linkIdePlugin")
//            linkIdePlugin?.isEnabled = false
//            val pluginClassLoader = linkIdePlugin?.pluginClassLoader
//            LogUtil.info("disableConflictPlugin 4 pluginClassLoader $pluginClassLoader")
////            //将classloader环境设置为linkIDE插件，用来读取claa信息
////            Thread.currentThread().contextClassLoader = pluginClassLoader
////            val classPool = ClassPool.getDefault()
////            val ctClass = classPool.getCtClass(CODEGENCORE_CORE_SERVICE_CLASS)
////            ctClass.defrost()
////            val methodArr: Array<CtMethod> = ctClass.getDeclaredMethods(CODEGENCORE_CORE_SERVICE_METHOD)
////            if (methodArr.size == 0) {
////                return
////            }
////            ctClass.defrost()
////            for (ctmethod in methodArr) {
////                ctmethod.setBody("return null;")
////            }
////            ctClass.toClass()
//        } catch (e: Throwable) {
//            LOGGER.info("tsingyan handler conflict plugin error", e)
//        } finally {
////            Thread.currentThread().contextClassLoader = contextClassLoader
//        }
//    }

    fun getFileSeparator(pathExample: String): String {
        if ("\\" == File.separator && !pathExample.contains(File.separator) && pathExample.contains("/")) {
            return "/"
        }
        return File.separator
    }

    fun isInTestBundle(editor: Editor?): Boolean {
        if (editor == null) {
            return false
        }

        val file = FileDocumentManager.getInstance().getFile(editor.document) ?: return false

        // 假设 "Test" bundle 的路径包含某个特定标识符，例如 "/test/"
        return file.path.contains("/src/test/")
    }

    fun isInTestBundle(filePath: String): Boolean {
        return filePath.contains("/src/test/")
    }

    fun getSelectedTextWithFullLines(editor: Editor): String? {
        val document: Document = editor.document
        val selectionModel = editor.selectionModel

        // 如果没有选中内容，返回 null
        if (!selectionModel.hasSelection()) return null

        // 获取选中的开始和结束偏移量
        val startOffset = selectionModel.selectionStart
        val endOffset = selectionModel.selectionEnd

        // 根据偏移量计算行号
        val startLine = document.getLineNumber(startOffset)
        var endLine = document.getLineNumber(endOffset)
        //如果是全选一整行，返回的结束偏移值是第二行的开始偏移值，需要特别处理
        if (document.getLineStartOffset(endLine)==endOffset){
            endLine -= 1
        }

        // 获取完整的行文本
        return getLinesContent2(editor, startLine, endLine)
    }

    fun getLinesContent2(editor: Editor, startLine: Int, endLine: Int): String {
        val document: Document = editor.document

        // 确保行号在合法范围内
        val validStartLine = startLine.coerceAtLeast(0)
        val validEndLine = endLine.coerceAtMost(document.lineCount - 1)

        // 获取从 startLine 到 endLine 的文本
        val startOffset = document.getLineStartOffset(validStartLine)
        val endOffset = document.getLineEndOffset(validEndLine)

        return document.text.substring(startOffset, endOffset)
    }

    fun isEnableInlineChat(): Boolean {
        val functionArray = AppConstant.COMPLETION_CONFIG.functionPermissions ?: return false
        return functionArray.contains("function/codeGenerate")
    }

    fun isEnableChatAgent(): Boolean {
        val functionArray = AppConstant.COMPLETION_CONFIG.functionPermissions ?: return false
        return functionArray.contains("localAgent/chatRag")
    }

    fun isEnableComposer(): Boolean {
        val functionArray = AppConstant.COMPLETION_CONFIG.functionPermissions ?: return false
        return functionArray.contains("function/aiPartner")
    }

    fun getProductType() : String {
        return if (isCloudIDE) "CLOUD_IDE_IDEA" else "IDEA"
    }

    /**
     * 是否是安全过滤白名单用户
     */
    fun isEnableSecureWhiteList():Boolean{
        return AppConstant.COMPLETION_CONFIG.enableSecurityFilter
    }

    /**
     * 必须白名单和设置中的开关同时打开
     */
    fun isCodeEditsEnable(): Boolean {
        return settingData.state.codeEditSwitch && AppConstant.COMPLETION_CONFIG.enableCodeEdits
    }

    fun getCodeEditsContext(cartLine: Int, lineCount: Int, cursorUpLineNum: Int, cursorDownLineNum: Int): LineRange {
        // 当前光标所在的行号（从0开始）
        val caretLine = cartLine

        // 文档的总行数
        val totalLines = lineCount

        // 计算起始行：光标向上 `cursorUpLineNum` 行，确保不越界（不小于 0）
        val startLine = maxOf(caretLine - cursorUpLineNum, 0)

        // 计算结束行：光标向下 `cursorDownLineNum` 行，确保不越界（不大于总行数 - 1）
        val endLine = minOf(caretLine + cursorDownLineNum, totalLines - 1)

        // 返回 LineRange，从起始行到结束行
        return LineRange(startLine, endLine)
    }

    /**
     * 基于指定行号获取CodeEdits上下文范围
     * @param editor 编辑器
     * @param targetLineNumber 目标行号（从0开始）
     * @param cursorUpLineNum 向上行数
     * @param cursorDownLineNum 向下行数
     * @return LineRange 上下文行范围
     */
    fun getCodeEditsContextForLine(editor: Editor, targetLineNumber: Int, cursorUpLineNum: Int, cursorDownLineNum: Int): LineRange {
        val document = editor.document

        // 文档的总行数
        val totalLines = document.lineCount

        // 确保目标行号在有效范围内
        val validTargetLine = targetLineNumber.coerceIn(0, totalLines - 1)

        // 计算起始行：目标行向上 `cursorUpLineNum` 行，确保不越界（不小于 0）
        val startLine = maxOf(validTargetLine - cursorUpLineNum, 0)

        // 计算结束行：目标行向下 `cursorDownLineNum` 行，确保不越界（不大于总行数 - 1）
        val endLine = minOf(validTargetLine + cursorDownLineNum, totalLines - 1)

        // 返回 LineRange，从起始行到结束行
        return LineRange(startLine, endLine)
    }

    fun getLinesFromDocument(document: Document, fromLine: Int, toLine: Int): String {
        var fromLine = fromLine
        var toLine = toLine

        // 确保起始行号不小于0
        if ((max(0.0, fromLine.toDouble()).also { fromLine = it.toInt() }) >= document.lineCount) {
            return ""
        }

        // 确保结束行号不超过文档的最大行号
        toLine = min((document.lineCount - 1).toDouble(), toLine.toDouble()).toInt()
        toLine = max(0.0, toLine.toDouble()).toInt()

        // 获取指定行范围的文本内容
        return document.getText(TextRange(document.getLineStartOffset(fromLine), document.getLineEndOffset(toLine)))
    }

    fun getCodeEditsPrefixAndSuffix(document: Document, fromLine: Int, toLine: Int, offset: Int): Pair<String,String>{
        var fromLine = fromLine
        var toLine = toLine

        // 确保起始行号不小于0
        if ((max(0.0, fromLine.toDouble()).also { fromLine = it.toInt() }) >= document.lineCount) {
            return Pair("","")
        }

        // 确保结束行号不超过文档的最大行号
        toLine = min((document.lineCount - 1).toDouble(), toLine.toDouble()).toInt()
        toLine = max(0.0, toLine.toDouble()).toInt()
        
        // 获取行的起始和结束偏移量
        val lineStartOffset = document.getLineStartOffset(fromLine)
        val lineEndOffset = document.getLineEndOffset(toLine)
        
        // 边界检查：确保offset在有效范围内
        val validOffset = offset.coerceIn(lineStartOffset, lineEndOffset)
        
        // 安全地创建TextRange，确保起始位置不大于结束位置
        val prefixStartOffset = lineStartOffset
        val prefixEndOffset = min(validOffset, lineEndOffset)
        val suffixStartOffset = max(validOffset, lineStartOffset)
        val suffixEndOffset = lineEndOffset
        
        val prefix = if (prefixStartOffset <= prefixEndOffset) {
            document.getText(TextRange(prefixStartOffset, prefixEndOffset))
        } else {
            ""
        }
        
        val suffix = if (suffixStartOffset <= suffixEndOffset) {
            document.getText(TextRange(suffixStartOffset, suffixEndOffset))
        } else {
            ""
        }
        
        return Pair(prefix, suffix)
    }

    fun getProjectFromContext(): Project? {
        val dataContextResult: AsyncResult<*> = DataManager.getInstance().getDataContextFromFocus()
        try {
            val dataContext = dataContextResult.getResult() as DataContext?
            if (dataContext != null) {
                return CommonDataKeys.PROJECT.getData(dataContext)
            }
        } catch (exception: java.lang.Exception) {
            // empty catch block
        }
        return ProjectManager.getInstance().getDefaultProject()
    }

    //发送设置变更事件给H5对话页
    fun sendSettingChangeEvent(mutableMap: MutableMap<String, Any?>){
        val project = getProject()
        if (project == null) {
            return
        }

        val webView = project.getService(WebViewService::class.java).getWebView()
        if (webView.getHasLoadedFinished() || isCloudIDE) {
            val messageModel = MessageModel()
            messageModel.actionType = WebActionTypeEnum.TO_JS.name
            messageModel.target = WebTargetEnum.SETTING_CHANED_V2.name
            messageModel.message = JSONObject.toJSONString(mutableMap)
            LOGGER.info("sendSettingChangeEvent sendMsgToBrowser ${JSON.toJSONString(messageModel)}")
            webView.sendMsgToBrowser(messageModel, messageModel.target!!, null)
        }
    }

    fun getProject(): Project? {
        val project = CommonDataKeys.PROJECT.getData(DataManager.getInstance().getDataContext())
        return project
    }

    /**
     * 获取当前选中代码的详细信息
     * @param editor 当前编辑器
     * @return JSON字符串，包含选中代码的详细信息，如果没有选中内容则返回null
     */
    fun getSelectedCodeInfo(editor: Editor): JSONObject? {
        val document: Document = editor.document
        val selectionModel = editor.selectionModel

        // 如果没有选中内容，返回 null
        if (!selectionModel.hasSelection()) return null

        // 获取选中的开始和结束偏移量
        val startOffset = selectionModel.selectionStart
        val endOffset = selectionModel.selectionEnd

        // 根据偏移量计算行号（转换为1-based行号）
        val startLine = document.getLineNumber(startOffset) + 1
        var endLine = document.getLineNumber(endOffset) + 1
        //如果是全选一整行，返回的结束偏移值是第二行的开始偏移值，需要特别处理
        if (document.getLineStartOffset(endLine - 1) == endOffset) {
            endLine -= 1
        }

        // 获取选中的代码内容
        val selectedCode = selectionModel.selectedText ?: ""

        // 获取文件信息
        val virtualFile: VirtualFile? = FileDocumentManager.getInstance().getFile(document)
        val fileName = virtualFile?.name ?: "Unknown"
        val filePath = virtualFile?.path ?: "Unknown"
        
        // 获取项目相对路径
        val project = editor.project
        val relativePath = if (project != null && virtualFile != null) {
            val basePath = project.basePath
            if (basePath != null && filePath.startsWith(basePath)) {
                filePath.substring(basePath.length).trimStart('/', '\\')
            } else {
                filePath
            }
        } else {
            filePath
        }

        // 获取编程语言
        var language: String? = null
        val fileExtension: LanguageEnum = LanguageEnum.getLanguageEnum(fileName)
        language = fileExtension.name.lowercase()

        // 构建JSON对象
        val jsonObject = JSONObject()
        jsonObject.put("code", selectedCode)
        jsonObject.put("startLine", startLine)
        jsonObject.put("endLine", endLine)
        jsonObject.put("fileName", fileName)
        jsonObject.put("filePath", relativePath)
        jsonObject.put("language", language)

        return jsonObject
    }

    /**
     * 获取当前焦点编辑器中选中代码的详细信息
     * @return JSON字符串，包含选中代码的详细信息，如果没有焦点编辑器或没有选中内容则返回null
     */
    fun getSelectedCodeInfo(): JSONObject? {
        val project = getProject() ?: return null
        val editor = FileEditorManager.getInstance(project).selectedTextEditor ?: return null
        return getSelectedCodeInfo(editor)
    }

    fun getDefaultEnvs() : MutableMap<String,String>{
        val commandLine = GeneralCommandLine()
        return commandLine.parentEnvironment
    }


    /**
     * 判断编辑器中的文件是否为代码文件
     * 排除日志文件、临时文件、文档文件等非代码文件
     */
    fun isCodeFile(editor: Editor): Boolean {
        val file = FileDocumentManager.getInstance().getFile(editor.document)
        if (file == null) {
            return false
        }

        val fileName = file.name.lowercase()
        val extension = file.extension?.lowercase()

        // 排除的文件扩展名
        val excludedExtensions = setOf(
            // 日志文件
            "log", "logs", "out",
            // 临时文件
            "tmp", "temp", "bak", "backup",
            // 文档文件
            "md", "txt", "doc", "docx", "pdf", "rtf",
            // 压缩文件
            "zip", "rar", "tar", "gz", "7z",
            // 图片文件
            "png", "jpg", "jpeg", "gif", "bmp", "svg", "ico",
            // 音视频文件
            "mp3", "mp4", "avi", "mkv", "wav", "flac",
            // 其他二进制文件
            "exe", "dll", "so", "dylib", "bin", "class", "jar", "war"
        )

        // 排除的文件名模式（文档类文件）
        val excludedPatterns = listOf(
            "changelog", "readme", "license", "notice", "authors", "contributors",
            "install", "upgrade", "migration", "release", "news"
        )

        // 如果文件扩展名在排除列表中，则不是代码文件
        if (extension != null && excludedExtensions.contains(extension)) {
            return false
        }

        // 如果文件名匹配排除模式，则不是代码文件
        if (excludedPatterns.any { pattern -> fileName.contains(pattern) }) {
            return false
        }

        // 支持的代码文件扩展名
        val codeExtensions = setOf(
            // Java系列
            "java", "kt", "kts", "groovy", "scala",
            // JavaScript/TypeScript
            "js", "jsx", "ts", "tsx", "vue", "svelte",
            // Python
            "py", "pyx", "pyi", "pyw",
            // C/C++
            "c", "cpp", "cxx", "cc", "h", "hpp", "hxx",
            // C#
            "cs", "vb",
            // Web前端
            "html", "htm", "css", "scss", "sass", "less", "stylus",
            // 其他编程语言
            "go", "rs", "swift", "php", "rb", "pl", "sh", "bash", "zsh", "fish",
            "sql", "r", "m", "mm", "dart", "lua", "vim", "el", "clj", "hs",
            // 脚本和配置（编程相关）
            "gradle", "maven", "ant", "cmake", "make",
            // 数据格式（在代码项目中常见）
            "json", "xml", "yaml", "yml", "toml", "properties",
            // 配置文件
            "ini", "conf", "config", "cfg", "env",
            // 其他开发相关文件
            "gitignore", "gitattributes", "gitmodules", "editorconfig",
            "dockerfile", "dockerignore", "jenkinsfile"
        )

        // 如果有扩展名且在代码文件列表中，则是代码文件
        if (extension != null && codeExtensions.contains(extension)) {
            return true
        }

        // 对于没有扩展名的文件，检查是否是常见的脚本文件
        if (extension == null || extension.isEmpty()) {
            val scriptPatterns = listOf(
                "makefile", "dockerfile", "jenkinsfile", "vagrantfile",
                "gemfile", "rakefile", "gradlew", "mvnw"
            )
            if (scriptPatterns.any { pattern -> fileName.contains(pattern) }) {
                return true
            }
        }

        // 默认情况下，如果不在排除列表中，则认为是代码文件
        return true
    }

    /**
     * 将代码文本转换为带行号的格式
     * @param content 代码内容
     * @param startLineNumber 起始行号（从1开始）
     * @return 带行号的代码文本，格式为"行号 代码内容\n"
     * <AUTHOR>
     */
    fun formatCodeWithLineNumbers(content: String, startLineNumber: Int = 1): String {
        if (content.isEmpty()) return ""
        
        val lines = content.split("\n")
        val result = StringBuilder()
        
        lines.forEachIndexed { index, line ->
            val lineNumber = startLineNumber + index
            result.append("$lineNumber $line")
            // 只有不是最后一行时才添加换行符
            if (index < lines.size - 1) {
                result.append("\n")
            }
        }
        
        return result.toString()
    }

    /**
     * 获取带行号的sourceCodePrefix
     * 前一行
     * <AUTHOR>
     */
    fun getSourceCodePrefix(editor: Editor, offset: Int): String {
        val document = editor.document
        val currentLine = document.getLineNumber(offset)
        
        // 获取前一行
        val prevLine = currentLine - 1
        
        // 如果没有前一行，返回空字符串
        if (prevLine < 0) return ""
        
        val prevLineStartOffset = document.getLineStartOffset(prevLine)
        val prevLineEndOffset = document.getLineEndOffset(prevLine)
        val prevLineContent = document.getText(TextRange(prevLineStartOffset, prevLineEndOffset))
        
        return "${prevLine + 1} $prevLineContent"
    }

    /**
     * 获取带行号的sourceCodeSuffix
     * 当前行 + 后三行
     * <AUTHOR>
     */
    fun getSourceCodeSuffix(editor: Editor, offset: Int): String {
        val document = editor.document
        val currentLine = document.getLineNumber(offset)
        
        if (currentLine >= document.lineCount) return ""
        
        val result = StringBuilder()
        
        // 添加完整的当前行
        val currentLineStartOffset = document.getLineStartOffset(currentLine)
        val currentLineEndOffset = document.getLineEndOffset(currentLine)
        val currentLineContent = document.getText(TextRange(currentLineStartOffset, currentLineEndOffset))
        result.append("${currentLine + 1} $currentLineContent")
        
        // 添加后三行
        val endLine = minOf(document.lineCount - 1, currentLine + 3)
        for (lineNum in (currentLine + 1)..endLine) {
            val lineStartOffset = document.getLineStartOffset(lineNum)
            val lineEndOffset = document.getLineEndOffset(lineNum)
            val lineContent = document.getText(TextRange(lineStartOffset, lineEndOffset))
            result.append("\n${lineNum + 1} $lineContent")
        }
        
        return result.toString()
    }

    /**
     * 获取带行号的originalCode
     * 限制在200行以内，超过200行的在当前编辑区域上下截取100行
     * <AUTHOR>
     */
    fun getOriginalCode(editor: Editor, offset: Int): String {
        val document = editor.document
        val totalLines = document.lineCount
        
        if (totalLines == 0) return ""
        
        val currentLine = document.getLineNumber(offset)
        val maxLines = 200
        
        val startLine: Int
        val endLine: Int
        
        if (totalLines <= maxLines) {
            // 文档总行数不超过200行，返回全部内容
            startLine = 0
            endLine = totalLines - 1
        } else {
            // 超过200行，在当前编辑区域上下截取100行
            val contextLines = 100
            startLine = maxOf(0, currentLine - contextLines / 2)
            endLine = minOf(totalLines - 1, startLine + maxLines - 1)
        }
        
        val result = StringBuilder()
        for (lineNum in startLine..endLine) {
            val lineStartOffset = document.getLineStartOffset(lineNum)
            val lineEndOffset = document.getLineEndOffset(lineNum)
            val lineContent = document.getText(TextRange(lineStartOffset, lineEndOffset))
            
            if (lineNum > startLine) {
                result.append("\n")
            }
            result.append("${lineNum + 1} $lineContent")
        }
        
        return result.toString()
    }

    /**
     * 基于文本内容获取带行号的sourceCodePrefix
     * 前一行+光标所在的前半行
     * <AUTHOR>
     */
    fun getSourceCodePrefixFromText(contextText: String, offset: Int): String {
        if (contextText.isEmpty() || offset <= 0) return ""
        
        val beforeCursor = contextText.substring(0, minOf(offset, contextText.length))
        val lines = beforeCursor.split("\n")
        
        if (lines.isEmpty()) return ""
        
        val result = StringBuilder()
        val currentLineIndex = lines.size - 1
        
        // 添加前一行（如果存在）
        if (currentLineIndex > 0) {
            val prevLineNumber = currentLineIndex
            result.append("$prevLineNumber ${lines[currentLineIndex - 1]}")
            result.append("\n")
        }
        
        // 添加当前行的前半部分
        val currentLineNumber = currentLineIndex + 1
        result.append("$currentLineNumber ${lines[currentLineIndex]}")
        
        return result.toString()
    }

    /**
     * 基于文本内容获取带行号的sourceCodeSuffix
     * 光标所在的后半行 + 后三行
     * <AUTHOR>
     */
    fun getSourceCodeSuffixFromText(contextText: String, offset: Int): String {
        if (contextText.isEmpty() || offset >= contextText.length) return ""
        
        val afterCursor = contextText.substring(offset)
        val beforeCursor = contextText.substring(0, offset)
        val beforeLines = beforeCursor.split("\n")
        val currentLineNumber = beforeLines.size
        
        val allLines = contextText.split("\n")
        val currentLineIndex = currentLineNumber - 1
        
        if (currentLineIndex >= allLines.size) return ""
        
        val result = StringBuilder()
        
        // 添加当前行的后半部分
        val currentLine = allLines[currentLineIndex]
        val currentLinePrefix = beforeLines.lastOrNull() ?: ""
        val currentLineSuffix = if (currentLine.length >= currentLinePrefix.length) {
            currentLine.substring(currentLinePrefix.length)
        } else {
            ""
        }
        result.append("$currentLineNumber $currentLineSuffix")
        
        // 添加后三行
        val endLineIndex = minOf(allLines.size - 1, currentLineIndex + 3)
        for (lineIndex in (currentLineIndex + 1)..endLineIndex) {
            val lineNumber = lineIndex + 1
            result.append("\n$lineNumber ${allLines[lineIndex]}")
        }
        
        return result.toString()
    }

    /**
     * 检查行号是否合法
     * @param lineNumber 行号（从1开始）
     * @param totalLines 文件总行数
     * @return true表示行号合法，false表示行号不合法
     * <AUTHOR>
     */
    fun isValidLineNumber(lineNumber: Int, totalLines: Int): Boolean {
        return lineNumber >= 1 && lineNumber <= totalLines
    }

    /**
     * 检查行号是否合法
     * @param lineNumber 行号（从1开始）
     * @param document 文档对象
     * @return true表示行号合法，false表示行号不合法
     * <AUTHOR>
     */
    fun isValidLineNumber(lineNumber: Int, document: Document): Boolean {
        return isValidLineNumber(lineNumber, document.lineCount)
    }

    /**
     * 检查行号是否合法
     * @param lineNumber 行号（从1开始）
     * @param editor 编辑器对象
     * @return true表示行号合法，false表示行号不合法
     * <AUTHOR>
     */
    fun isValidLineNumber(lineNumber: Int, editor: Editor): Boolean {
        return isValidLineNumber(lineNumber, editor.document)
    }
}