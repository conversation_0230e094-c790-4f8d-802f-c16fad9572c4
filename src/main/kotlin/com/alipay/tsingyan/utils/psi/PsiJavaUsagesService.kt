package com.alipay.tsingyan.utils.psi

import com.alipay.tsingyan.utils.psi.model.MethodInfo
import com.alipay.tsingyan.utils.psi.model.MethodReference
import com.alipay.tsingyan.utils.psi.model.SimpleReferenceMethodInfo
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiMethod
import com.intellij.psi.impl.source.PsiMethodImpl
import com.intellij.psi.search.searches.ReferencesSearch


/**
 * 获取Java的使用信息
 */
object PsiJavaUsagesService {

    /**
     * 获取对应方法的使用位置
     */
    fun getUsages(
        project: Project,
        className: String?,
        methodName: String?,
    ): List<MethodInfo> {
        val result = mutableListOf<MethodInfo>()
        val psiMethods = PsiJavaService.getPsiMethod(project, className, methodName)
        inRead {
            psiMethods.forEach {
                result.add(searchUsage(it, project))
            }
        }
        return result
    }


    /**
     * 查询引用关系
     */
    fun searchUsage(psiMethod: PsiMethod, project: Project): MethodInfo {
        val result = MethodInfo(
            className = psiMethod.containingClass?.name,
            qualifiedNameClassName = psiMethod.containingClass?.qualifiedName,
            filePath = VfsUtils.getRelativeFilePath(psiMethod.containingFile.virtualFile, project),
            methodName = psiMethod.name,
            text = psiMethod.text,
        )

        // 需要遍历检索此方法和
        findAndAddUsage(result.methodReferenceList, psiMethod, project)
        psiMethod.findSuperMethods().forEach {
            findAndAddUsage(result.methodReferenceList, it, project, true)
        }
        return result
    }


    /**
     * 查找并且添加使用信息
     */
    fun findAndAddUsage(
        methodReferenceList: MutableList<MethodReference>,
        psiMethod: PsiMethod,
        project: Project,
        isSuperResolve: Boolean = false,
    ) {
        val allReferences = ReferencesSearch
            .search(psiMethod)
            .allowParallelProcessing()
            .mapNotNull {
                // 判断引用的位置是否在方法里
                val psiElement = parentIsMethod(it.element.parent)
                if (psiElement is PsiMethod) {
                    return@mapNotNull psiElement
                } else
                    return@mapNotNull null
            }
            .distinct()
            .take(20)
            .groupBy { it.containingClass }

        allReferences.forEach { currPsiClass, psiMethodList ->
            val reference = MethodReference(
                className = currPsiClass?.name,
                qualifiedNameClassName = currPsiClass?.qualifiedName,
                filePath = currPsiClass?.let {
                    VfsUtils.getRelativeFilePath(currPsiClass.containingFile.virtualFile, project)
                } ?: "Unknown",
                isSuperResolve = isSuperResolve
            ).apply {
                if (isSuperResolve) {
                    this.superClassName = psiMethod.containingClass?.name
                    this.superQualifiedNameClassName = psiMethod.containingClass?.qualifiedName
                }
            }

            for (psiMethod1 in psiMethodList) {
                reference.referencedOfMethodList.add(
                    SimpleReferenceMethodInfo(
                        methodName = psiMethod1.name,
                        text = psiMethod1.text
                    )
                )
            }
            methodReferenceList.add(reference)
        }
    }

    /**
     * 判断使用的地方是方法
     */
    fun parentIsMethod(element: PsiElement, nums: Int = 1): PsiElement {
        if (null == element.parent) {
            return element
        }

        if ((element is PsiMethod || element is PsiMethodImpl) || nums > 10) {
            return element
        } else {
            return parentIsMethod(element.parent, nums + 1)
        }
    }
}