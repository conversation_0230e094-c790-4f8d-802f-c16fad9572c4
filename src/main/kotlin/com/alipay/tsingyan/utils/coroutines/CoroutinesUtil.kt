package com.alipay.tsingyan.utils.coroutines

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors

/**
 * 插件内自定义协程分发器的统一收口
 */
// 双工通信协程控制域
val webSocketDispatcher = Executors.newFixedThreadPool(8).asCoroutineDispatcher()
val webSocketScope = CoroutineScope(webSocketDispatcher)

// MCP 默认使用的协程，理论上
val mcpDispatcher = Executors.newFixedThreadPool(4).asCoroutineDispatcher()
val mcpSocketScope = CoroutineScope(mcpDispatcher)