package com.alipay.tsingyan.utils.completion

import com.alipay.tsingyan.utils.AppConstant
import org.apache.commons.lang3.StringUtils
import java.util.regex.Pattern

/**
 * 代码工具
 */
object CodeUtils {

    private val tokenPattern = Pattern.compile(
        "[0-9A-Za-z]+",
        Pattern.CASE_INSENSITIVE
    )

    /**
     * 多行正则匹配
     */
//    val MULTIPLE_COMMON_REGEX = Pattern.compile("/\\*.+?\\*/", Pattern.DOTALL)

    /**
     * 单行注释正则
     */
//    val SINGLE_COMMON_REGEX = "//.+\\n"

    val SINGLE_COMMON_REGEX = Regex("^[/*].*")

    /**
     * 空行正则
     */
    val BLANK_REGEX = "(?m)^\\s*\$(\\n|\\r\\n)"


    /**
     * 判断是否为有效补全上文
     * 根据{@see TsingyanConfigDataModel#completionConfigModel#completionRegular}对上文最后一个字符过滤，如果不匹配则不进行补全
     */
    fun checkAvaliablePrompt(lastChar: CharSequence): Boolean {
        try {
            //如果有效正则为空，直接通过
            if (StringUtils.isBlank(AppConstant.COMPLETION_CONFIG.completionRegular)) {
                return true
            }
            val regex = AppConstant.COMPLETION_CONFIG.completionRegular.toRegex()
            return regex.matches(lastChar)
        } catch (e: Throwable) {
            return true
        }
    }


    /**
     * 清除注释和空行
     *
     */
    fun cleanCommonAndBlank(content: String): String {
//        return MULTIPLE_COMMON_REGEX.matcher(
//            content.replace(SINGLE_COMMON_REGEX.toRegex(), "").replace(BLANK_REGEX.toRegex(), "")
//        ).replaceAll("")
        return content.replace(BLANK_REGEX.toRegex(), "")
    }

    /**
     * 判断是否为注释或者空行
     */
    fun isNoteOrBlank(content: String?): Boolean {
        if (content == null || content.trim().isEmpty() || content.startsWith("*/")) {
            return false
        }
        return SINGLE_COMMON_REGEX.matches(content)
    }

    /**
     * 获取展示名
     * 1:前面填充非完整token字符，组装成完整token
     * 2:截取补全数据的长度
     *
     * @param content
     * @param length
     * @return
     */
    fun getDisplayName(realLastLine: String?, content: String?, length: Int): String? {
        var content = content
        if (content == null || content.trim().length == 0) {
            return content
        }
        val result = StringBuilder(content)
        //填充前缀
        if (realLastLine != null && realLastLine.trim().length > 0) {
            val charArr = realLastLine.toCharArray()
            for (i in charArr.indices.reversed()) {
                val c = charArr[i]
                if (Character.isUpperCase(c) || Character.isLowerCase(c) || Character.isDigit(c)) {
                    result.insert(0, c)
                } else {
                    break
                }
            }
        }
        content = result.toString().trim().replace(" < ", "<").replace(" > ", ">")
        return if (length > content.length) {
            content
        } else content.substring(0, length) + "..."
    }

    /**
     * 获取token数量
     */
    fun getTokenNum(content: String): Long {
        try {
            if (content == null) {
                return 0
            }
            val matcher = tokenPattern.matcher(content)
            return matcher.results().count()
        } catch (e: Throwable) {
            return 0
        }
    }

    /**
     * 判断是否注释
     */
    fun isNoteLine(line: String): Boolean {
        if (line.trim().startsWith("/") || line.trim().trimStart().startsWith("*")) {
            return true
        }
        return false
    }
}