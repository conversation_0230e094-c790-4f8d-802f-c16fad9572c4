package com.alipay.tsingyan.utils

import com.alipay.tsingyan.agent.bean.ReferenceBean
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import java.util.concurrent.ConcurrentHashMap

/**
 * project保存唯一的值
 */
object ProjectCache {

    // 创建一个唯一的Key来存储和检索与Project关联的数据
    private val MY_PROJECT_SPECIFIC_KEY: Key<YourCustomDataType> = Key.create("codefuse.project.unique.data")
    private val MY_PROJECT_COMMIT_ID_KEY: Key<CommitIdData> = Key.create("codefuse.project.unique.commitId")
    private val SESSION_SELECTED_CODE_KEY: Key<SessionSelectedCodeData> = Key.create("codefuse.project.session.selectedcode")

    // 存储数据到Project中
    private fun cacheProjectData(project: Project, data: YourCustomDataType) {
        project.putUserData(MY_PROJECT_SPECIFIC_KEY, data)
    }

    // 从Project中检索数据
    private fun getProjectData(project: Project): YourCustomDataType? {
        return project.getUserData(MY_PROJECT_SPECIFIC_KEY)
    }

    fun getGitData(project: Project):String{
        val data = getProjectData(project)
        if (data != null && data.GIT_REPO!=""){
            return data.GIT_REPO
        }
        return ""
    }

    fun saveGitData(project: Project, gitRepo: String){
        val data = getProjectData(project)
        if (data == null){
            val newData = YourCustomDataType()
            newData.GIT_REPO = gitRepo
            cacheProjectData(project,newData)
        }else{
            data.GIT_REPO = gitRepo
            cacheProjectData(project,data)
        }
    }

    private fun cacheCommitIdData(project: Project, data: CommitIdData) {
        project.putUserData(MY_PROJECT_COMMIT_ID_KEY, data)
    }

    private fun getCommitIdData(project: Project): CommitIdData? {
        return project.getUserData(MY_PROJECT_COMMIT_ID_KEY)
    }

    fun getCommitId(project: Project):String{
        val data = getCommitIdData(project)
        if (data != null && data.COMMIT_ID!=""){
            return data.COMMIT_ID
        }
        return ""
    }

    fun saveCommitId(project: Project, commitId: String){
        val data = getCommitIdData(project)
        if (data == null){
            val newData = CommitIdData()
            newData.COMMIT_ID = commitId
            cacheCommitIdData(project,newData)
        }else{
            data.COMMIT_ID = commitId
            cacheCommitIdData(project,data)
        }
    }

    // 会话选中代码缓存相关方法
    private fun getSessionSelectedCodeData(project: Project): SessionSelectedCodeData? {
        return project.getUserData(SESSION_SELECTED_CODE_KEY)
    }

    private fun cacheSessionSelectedCodeData(project: Project, data: SessionSelectedCodeData) {
        project.putUserData(SESSION_SELECTED_CODE_KEY, data)
    }

    /**
     * 缓存会话的选中代码
     * @param project 项目实例
     * @param sessionUid 会话ID
     * @param selectedCode 选中的代码信息，可以为null
     */
    fun cacheSessionSelectedCode(project: Project, sessionUid: String, selectedCode: ReferenceBean?) {
        val data = getSessionSelectedCodeData(project) ?: SessionSelectedCodeData()
        val currentTime = System.currentTimeMillis()

        // 清理过期的缓存（超过2小时）
        data.cleanExpiredCache()

        // 缓存新的选中代码
        data.sessionCodeMap[sessionUid] = SessionCodeInfo(selectedCode, currentTime)
        cacheSessionSelectedCodeData(project, data)
    }

    /**
     * 获取会话缓存的选中代码
     * @param project 项目实例
     * @param sessionUid 会话ID
     * @return 缓存的选中代码信息，如果没有缓存或已过期则返回null
     */
    fun getCachedSessionSelectedCode(project: Project, sessionUid: String): ReferenceBean? {
        val data = getSessionSelectedCodeData(project) ?: return null

        // 清理过期的缓存
        data.cleanExpiredCache()

        val sessionCodeInfo = data.sessionCodeMap[sessionUid]
        return if (sessionCodeInfo != null && !sessionCodeInfo.isExpired()) {
            sessionCodeInfo.selectedCode
        } else {
            null
        }
    }

    /**
     * 清除指定会话的选中代码缓存
     * @param project 项目实例
     * @param sessionUid 会话ID
     */
    fun clearSessionSelectedCode(project: Project, sessionUid: String) {
        val data = getSessionSelectedCodeData(project) ?: return
        data.sessionCodeMap.remove(sessionUid)
        cacheSessionSelectedCodeData(project, data)
    }
}

// 你的自定义数据类型
class YourCustomDataType {
    var GIT_REPO:String = ""
}

class CommitIdData {
    var COMMIT_ID:String = ""
}

/**
 * 会话选中代码数据类
 */
class SessionSelectedCodeData {
    val sessionCodeMap: ConcurrentHashMap<String, SessionCodeInfo> = ConcurrentHashMap()

    /**
     * 清理过期的缓存（超过2小时）
     */
    fun cleanExpiredCache() {
        val currentTime = System.currentTimeMillis()
        val expiredSessions = sessionCodeMap.filter { (_, info) ->
            info.isExpired(currentTime)
        }.keys

        expiredSessions.forEach { sessionUid ->
            sessionCodeMap.remove(sessionUid)
        }
    }
}

/**
 * 会话代码信息
 */
data class SessionCodeInfo(
    val selectedCode: ReferenceBean?,
    val cacheTime: Long
) {
    companion object {
        // 2小时的毫秒数
        private const val CACHE_EXPIRE_TIME = 2 * 60 * 60 * 1000L
    }

    /**
     * 检查是否已过期
     */
    fun isExpired(currentTime: Long = System.currentTimeMillis()): Boolean {
        return currentTime - cacheTime > CACHE_EXPIRE_TIME
    }
}