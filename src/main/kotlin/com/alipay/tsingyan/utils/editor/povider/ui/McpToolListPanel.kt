package com.alipay.tsingyan.utils.editor.povider.ui

import com.alipay.tsingyan.mcp.manager.CommandHelp
import com.alipay.tsingyan.mcp.manager.CustomMcpServerManager
import com.alipay.tsingyan.mcp.manager.McpClientExtend
import com.alipay.tsingyan.utils.editor.povider.ui.help.CommandHelpPanel
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.intellij.ui.JBSplitter
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.AsyncProcessIcon
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import io.modelcontextprotocol.spec.McpSchema
import io.modelcontextprotocol.spec.McpSchema.Tool
import kotlinx.coroutines.*
import java.awt.*
import javax.swing.*

class McpToolListPanel(private val project: Project) : JPanel() {
    private val mcpServerManager = project.service<CustomMcpServerManager>()
    private val allMcpTools = mutableMapOf<String, List<Tool>>()
    private var currentFilteredMcpTools = mutableMapOf<String, List<Tool>>()
    private var loadingJob: Job? = null
    private val serverLoadingStatus = mutableMapOf<String, Boolean>()
    private val serverPanels = mutableMapOf<String, JPanel>()

    private val textGray = JBColor(0x6B7280, 0x9DA0A8)
    private val headerColor = JBColor(0xF3F4F6, 0x2B2D30)
    private val mcpServerMap = mutableMapOf<String, McpClientExtend>()

    var isLoading = false

    init {
        layout = BoxLayout(this, BoxLayout.Y_AXIS)
        background = UIUtil.getPanelBackground()
        add(JBLabel("请点击编辑器左上角刷新以进行调试"))
    }

    fun refreshTools(content: String, onToolsLoaded: (MutableMap<String, List<Tool>>) -> Unit = {}) {
        // 首先释放 Client 链接
        cleanClient()

        loadingJob?.cancel()
        serverLoadingStatus.clear()
        serverPanels.clear()
        allMcpTools.clear()
        currentFilteredMcpTools.clear()

        invokeLater {
            removeAll()
            revalidate()
            repaint()
        }

        loadingJob = CoroutineScope(Dispatchers.IO).launch {
            isLoading = true
            val serverConfigs = mcpServerManager.debugGetEnabledServers(content)
            if (serverConfigs.isNullOrEmpty()) {
                SwingUtilities.invokeLater {
                    showNoServersMessage()
                }
                return@launch
            }

            invokeLater {
                serverConfigs.keys.forEach { serverName ->
                    serverLoadingStatus[serverName] = true
                    createServerSection(serverName)
                }
            }

            val jobs = serverConfigs.map { (serverName, serverConfig) ->
                launch {
                    try {
                        val mcpClientExtend = mcpServerManager.debugGetClient(serverConfig)
                        mcpServerMap[serverName] = mcpClientExtend
                        if (null != mcpClientExtend.exception) {
                            invokeLater {
                                showServerError(
                                    serverName,
                                    mcpClientExtend.exception.desc,
                                    mcpClientExtend.help
                                )
                                serverLoadingStatus[serverName] = false
                            }
                        } else {
                            val tools = mcpClientExtend.client!!.listTools().tools
                            // 优雅的关闭链接， TODO 4期可能要做 Client 的调试，到时候再加上释放的逻辑
                            mcpClientExtend.client.closeGracefully()
                            synchronized(allMcpTools) {
                                allMcpTools[serverName] = tools
                                currentFilteredMcpTools[serverName] = tools
                            }
                            invokeLater {
                                updateServerSection(serverName, tools)
                                serverLoadingStatus[serverName] = false
                            }
                        }
                    } catch (e: Exception) {
                        invokeLater {
                            showServerError(serverName, e.message ?: "Unknown error")
                            serverLoadingStatus[serverName] = false
                        }
                    }
                }
            }
            jobs.joinAll()
            isLoading = false
            onToolsLoaded(allMcpTools)
        }
    }

    fun filterTools(searchText: String) {
        currentFilteredMcpTools = if (searchText.isEmpty()) {
            allMcpTools.toMutableMap()
        } else {
            allMcpTools.mapValues { (_, tools) ->
                tools.filter { tool ->
                    tool.name.contains(searchText, ignoreCase = true) ||
                            tool.description?.contains(searchText, ignoreCase = true) == true
                }
            }.toMutableMap()
        }

        SwingUtilities.invokeLater {
            currentFilteredMcpTools.forEach { (serverName, tools) ->
                updateServerSection(serverName, tools)
            }
            revalidate()
            repaint()
        }
    }

    private fun createServerSection(serverName: String) {
        val serverPanel = JPanel(BorderLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty()
        }

        val headerPanel = JPanel(BorderLayout()).apply {
//            background = headerColor
            border = JBUI.Borders.empty(4)
        }

        val serverLabel = JBLabel(serverName).apply {
            font = JBUI.Fonts.label(14.0f).asBold()
            foreground = UIUtil.getLabelForeground()
        }

        headerPanel.add(serverLabel, BorderLayout.WEST)
        serverPanel.add(headerPanel, BorderLayout.NORTH)

        val toolsPanel = JPanel(GridLayout(0, 3, 4, 4)).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty()
        }

        val loadingLabel = AsyncProcessIcon("Loading...")
        toolsPanel.add(loadingLabel)
        serverPanel.add(toolsPanel, BorderLayout.CENTER)

        serverPanels[serverName] = toolsPanel

        add(serverPanel)

        // 添加一个醒目的分隔符
        val customDivider = JPanel().apply {
            background = JBColor.RED
            preferredSize = Dimension(getWidth(), 5)
            minimumSize = Dimension(10, 5)
        }
        add(customDivider)

        add(JBSplitter())
        revalidate()
        repaint()
    }

    private fun updateServerSection(serverName: String, tools: List<McpSchema.Tool>) {
        val toolsPanel = serverPanels[serverName] ?: return
        toolsPanel.removeAll()

        if (tools.isEmpty()) {
            val noToolsLabel = JBLabel("No tools available for $serverName").apply {
                foreground = textGray
                horizontalAlignment = SwingConstants.LEFT
            }
            toolsPanel.add(noToolsLabel)
        } else {
            tools.forEach { tool ->
                val panel = McpToolListCardPanel(project, serverName, tool)
                toolsPanel.add(panel)
            }
        }

        toolsPanel.revalidate()
        toolsPanel.repaint()
    }

    private fun showServerError(
        serverName: String,
        errorMessage: String,
        commandHelp: CommandHelp? = null,
    ) {
        val toolsPanel = serverPanels[serverName] ?: return
        toolsPanel.removeAll()

        val errorPanel = CommandHelpPanel(
            project,
            errorMessage,
            commandHelp
        )
        toolsPanel.add(errorPanel)
        toolsPanel.revalidate()
        toolsPanel.repaint()
    }

    private fun showNoServersMessage() {
        removeAll()

        val noServersPanel = JPanel(BorderLayout()).apply {
            background = UIUtil.getPanelBackground()
            border = JBUI.Borders.empty(16)
        }

        // TODO 处理此处逻辑
        val noServersLabel = JBLabel("No MCP servers configured. Please check your configuration.").apply {
            foreground = textGray
            horizontalAlignment = SwingConstants.CENTER
            isLoading = false
        }

        noServersPanel.add(noServersLabel, BorderLayout.CENTER)
        add(noServersPanel)
        revalidate()
        repaint()
    }

    fun dispose() {
        loadingJob?.cancel()
        cleanClient()
    }

    fun cleanClient() {
        mcpServerMap.values.forEach {
            runCatching { it.client?.closeGracefully() }
        }
        mcpServerMap.clear()
    }
}
