package com.alipay.tsingyan.utils.editor.povider.ui.help

import com.alipay.tsingyan.mcp.manager.CustomMcpServerManager
import com.alipay.tsingyan.mcp.storage.ServerConfig
import com.alipay.tsingyan.mcp.storage.ServerType
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.components.JBLabel
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * MCP JSON 配置文件
 */
object MCPServerVerifyService {
    /**
     * 校验 MCP  JSON 配置文件
     */
    fun verifyJson(
        file: VirtualFile,
        project: Project,
        callBack: (() -> Unit)? = null,
    ): JComponent {
        val utf8Content = VfsUtils.getUtf8Content(file)
        val enabledServers = project.service<CustomMcpServerManager>().debugGetEnabledServers(utf8Content)
        if (enabledServers.isEmpty()) {
            return JBLabel("未能正确识别到相关的McpServer，请确保JSON配置正确")
        }

        // 判断一下是否有可以更新的
        val updateServers = verifyJServer(enabledServers)
        if (updateServers.isEmpty()) {
            return JPanel()
        }
        return CommandSuggestedPanel(project, file, updateServers, callBack)
    }


    /**
     * 校验 MCP JSON, 对内部的 MCP Server 做如下调整
     * 先进行类型判断，如果是 SSE 且为内部域名，即需要登录态的，建议用户切换为 utoo-proxy 的 STIO 方式
     */
    fun verifyJServer(mcpServerMap: MutableMap<String, ServerConfig>): MutableMap<String, MCPUpdateExtend> {
        val result = mutableMapOf<String, MCPUpdateExtend>()
        mcpServerMap.forEach { (serverName, config) ->
            if (isSSEAndCanUpdate(config)) {
                val extend = MCPUpdateExtend(
                    config = updateSSEToStdio(config),
                    reason = "检测到你使用蚂蚁内部的MCP Server，并且可能需要登录态，建议你替换为 utoo-proxy 代理登录"
                )
                result[serverName] = extend
            } else if (isStdioAndCanUpdate(config)) {
                val extend = MCPUpdateExtend(
                    config = updateStdio(config),
                    reason = "检测到你使用了 tnpx 命令来链接内部的MCP Server，为保证兼容性，建议你切换到 utoo-proxy"
                )
                result[serverName] = extend
            }
        }
        return result
    }


    /**
     * 判断是否是内部的 SSE,并且可以更新
     */
    fun isSSEAndCanUpdate(config: ServerConfig): Boolean {
        if (ServerType.SSE == config.getServerType() && null != config.url) {
            return containInnerUrl(config.url!!)
        }
        return false
    }

    /**
     * 更新内部的 SSE 到 STDIO 上
     */
    fun updateSSEToStdio(config: ServerConfig): ServerConfig {
        if (isSSEAndCanUpdate(config)) {
            val stdioServerConfig = ServerConfig(
                type = "stdio",
                command = "utoo-proxy",
                args = listOf(
                    config.url ?: "",
                    "-t",
                    "SSE"
                )
            )
            return stdioServerConfig
        }
        return config
    }

    /**
     * 判断在参数中是否存在内部的URL
     */
    fun isStdioAndCanUpdate(config: ServerConfig): Boolean {
        if (ServerType.STDIO == config.getServerType() && null != config.command) {
            val currArgs = config.args ?: return false
            val urlExist = currArgs.firstOrNull { arg -> containInnerUrl(arg) } ?: return false
            val utooExist = currArgs.firstOrNull { arg -> arg.equals("utoo-proxy") } ?: return false
            return true
        }
        return false
    }

    /**
     * 更新 STDIO
     */
    fun updateStdio(config: ServerConfig): ServerConfig {
        if (isStdioAndCanUpdate(config)) {
            val argList = (config.args ?: listOf()).filter { !it.equals("utoo-proxy") }
            val stdioServerConfig = ServerConfig(
                type = "stdio",
                command = "utoo-proxy",
                args = argList
            )
            return stdioServerConfig
        }
        return config
    }


    /**
     * 包含内部的URL
     */
    private fun containInnerUrl(url: String): Boolean {
        return url.contains("mcpnexus-prod.alipay.com")
                || url.contains("mcpnexus.alipay.com")
    }

    data class MCPUpdateExtend(
        val config: ServerConfig,
        val reason: String,
    )
}