package com.alipay.tsingyan.utils.editor.povider.ui.help

import com.alipay.tsingyan.utils.terminal.CustomProcessUtil
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Component
import java.awt.Dimension
import java.awt.Font
import javax.swing.*

/**
 * 命令环境帮助弹窗
 */
class CommandHelper(
    private val project: Project,
    private val command: String,
) : DialogWrapper(project, true) {

    init {
        title = "CodeFuse-环境检查"
        setSize(450, 250)
        init()
    }

    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout(10, 10))
        mainPanel.border = BorderFactory.createEmptyBorder(10, 10, 10, 10)
        mainPanel.preferredSize = Dimension(450, 250)

        // 警告图标和标题
        val headerPanel = JPanel(BorderLayout())
        val warningIcon = JBLabel("⚠️", SwingConstants.CENTER)
        warningIcon.font = Font(warningIcon.font.name, Font.PLAIN, 24)
        headerPanel.add(warningIcon, BorderLayout.WEST)

        val titleLabel = JBLabel("未找到 '$command' 命令", SwingConstants.CENTER)
        titleLabel.font = Font(titleLabel.font.name, Font.BOLD, 16)
        headerPanel.add(titleLabel, BorderLayout.CENTER)
        mainPanel.add(headerPanel, BorderLayout.NORTH)

        // 中央内容区
        val contentPanel = JPanel()
        contentPanel.layout = BoxLayout(contentPanel, BoxLayout.Y_AXIS)
        contentPanel.border = JBUI.Borders.empty(10)

        val descriptionText =
            JBTextArea("请确认您的系统中是否已安装 \"$command\" 环境。\n" + "如果未安装，您需要先安装该命令才能继续操作。")
        descriptionText.isEditable = false
        descriptionText.wrapStyleWord = true
        descriptionText.lineWrap = true
        descriptionText.background = mainPanel.background
        descriptionText.border = JBUI.Borders.empty(5)
        descriptionText.alignmentX = Component.LEFT_ALIGNMENT
        contentPanel.add(descriptionText)


        addHelpPanel(contentPanel)
        mainPanel.add(JBScrollPane(contentPanel), BorderLayout.CENTER)

        // 底部按钮区域由DialogWrapper自动处理
        if (command == "utoo-proxy") {
            this.setOKButtonText("一键安装")
            this.myOKAction.isEnabled = true
        } else {
            this.setOKButtonText("关闭")
            this.myCancelAction.putValue(DialogWrapper.DEFAULT_ACTION, true)
        }

        return mainPanel
    }

    private fun addHelpPanel(contentPanel: JPanel) {
        var helpDoc = ""
        if (command.equals("tnpm")) {
            helpDoc = "https://yuque.antfin.com/frontbase/tnpm/how-to-install"
        } else if (command.equals("utoo-proxy")) {
            helpDoc = "https://yuque.antfin.com/frontbase/unio-ai/vifnpcuvottv69uf"
        } else {
            return
        }

        // 安装指南链接
        val helpPanel = JPanel()
        helpPanel.layout = BoxLayout(helpPanel, BoxLayout.X_AXIS)
        helpPanel.alignmentX = Component.LEFT_ALIGNMENT
        helpPanel.border = JBUI.Borders.empty(10, 0, 5, 0)

        val helpLink = JButton("查看安装指南")
        helpLink.addActionListener {
            try {
                BrowserUtil.browse(helpDoc)
            } catch (ex: Exception) {
                // 处理异常
            }
        }
        helpPanel.add(helpLink)
        contentPanel.add(helpPanel)
    }

    /**
     * 确定按钮操作
     */
    override fun doOKAction() {
        if (command == "utoo-proxy") {
            CustomProcessUtil.runCommandInTerminal(
                "curl -L -o- https://registry.antgroup-inc.cn/@alipay/utoo-proxy/latest/files/setup.sh | bash",
                project
            )
        }
        super.doOKAction()
    }
}