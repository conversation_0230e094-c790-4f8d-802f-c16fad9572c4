package com.alipay.tsingyan.utils.editor.povider

import com.alipay.tsingyan.utils.AppConstant
import com.intellij.openapi.fileEditor.*
import com.intellij.openapi.fileEditor.impl.text.TextEditorProvider
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile

class McpEditorProvider : WeighedFileEditorProvider() {
    override fun getEditorTypeId() = "mcp-split-editor"
    override fun accept(project: Project, file: VirtualFile) = file.name.contains(".mcp.json")|| file.name == AppConstant.mcpConfName
    override fun createEditor(project: Project, file: VirtualFile): FileEditor {
        val editor = TextEditorProvider.getInstance().createEditor(project, file)
        if (editor.file is LightVirtualFile &&
            !file.name.equals(AppConstant.mcpConfName)
        ) {
            return editor
        }

        // 创建自定义的编辑器
        val mainProvider: TextEditorProvider = TextEditorProvider.getInstance()
        val previewProvider: FileEditorProvider = McpPreviewEditorProvider()
        val mainEditor = mainProvider.createEditor(project, file) as TextEditor
        val preview = previewProvider.createEditor(project, file) as McpPreviewEditor
        return McpFileEditorWithPreview(mainEditor, preview, project)
    }

    override fun getPolicy() = FileEditorPolicy.HIDE_OTHER_EDITORS
}
