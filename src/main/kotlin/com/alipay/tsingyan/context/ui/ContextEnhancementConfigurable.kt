package com.alipay.tsingyan.context.ui

import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.icons.AllIcons
import com.intellij.openapi.options.Configurable
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import java.awt.event.ActionEvent
import java.awt.event.ActionListener
import java.awt.event.FocusEvent
import java.awt.event.FocusListener
import javax.swing.*
import com.intellij.ui.components.OnOffButton
import com.intellij.openapi.application.ApplicationManager
import com.alipay.tsingyan.utils.WikiUtils
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Paths
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.project.Project
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.intellij.openapi.components.service
import com.intellij.ide.BrowserUtil
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent

class ContextEnhancementConfigurable : Configurable {
    private var mainPanel: JPanel? = null
    private var globalRulesTextArea: JBTextArea? = null
    private var projectRulesPanel: JPanel? = null
    
    // 配置相关的私有变量
    private var initialGlobalRules: String = ""
    private val placeholderText = "例如：\"用中文交流\"；\"回答时总是先输出思路，再生成代码\"；"
    private val placeholderColor = JBColor(Color.LIGHT_GRAY, Color.GRAY)
    private val normalColor = JBColor(Color.BLACK, Color.WHITE)
    
    // 控制公共规则库的加载状态
    private var loaded: Boolean = false
    private var projectRules: MutableMap<String, String> = mutableMapOf()
    

    
    // 项目规则缓存数据结构
    data class ProjectRuleInfo(
        val fileName: String,
        val enabled: Boolean,
        val fromPublicLibrary: Boolean,
        val ruleType: String? = null // 公共规则库的ruleType
    )

    companion object {
        private const val CONFIG_DIR_NAME = ".codefuse"
        private const val GLOBAL_RULES_FILE_NAME = "global_rules.txt"
        private const val PROJECT_RULES_DIR_NAME = ".codefuserules"
        private const val PROJECT_RULES_STATE_PREFIX = "project.rules.state."
        private const val PROJECT_RULES_CACHE_PREFIX = "project.rules.cache."
        

        
        /**
         * 获取全局规则内容（静态方法）
         */
        @JvmStatic
        fun getGlobalRulesContent(): String {
            return try {
                val globalRulesFile = getGlobalRulesFile()
                if (globalRulesFile.exists()) {
                    Files.readAllLines(Paths.get(globalRulesFile.absolutePath), Charsets.UTF_8).joinToString("\n")
                } else {
                    ""
                }
            } catch (e: IOException) {
                e.printStackTrace()
                ""
            }
        }

        /**
         * 判断仓库问答功能是否真正开启（静态方法）
         * @return true 如果服务端开关开启 && 用户勾选了
         */
        @JvmStatic
        fun isRepoQAEnabledStatic(): Boolean {
            return AppConstant.COMPLETION_CONFIG.enableRepoDeepSearch &&
                   service<TsingYanSettingStore>().state.enableRepoQASearch
        }

        /**
         * 判断代码解释功能是否真正开启（静态方法）
         * @return true 如果服务端开关开启 && 用户勾选了
         */
        @JvmStatic
        fun isExplainCodeEnabledStatic(): Boolean {
            return AppConstant.COMPLETION_CONFIG.enableExplainDeepSearch &&
                   service<TsingYanSettingStore>().state.enableExplainCodeSearch
        }

        /**
         * 判断A Par Agent功能是否真正开启（静态方法）
         * @return true 如果服务端开关开启 && 用户勾选了
         */
        @JvmStatic
        fun isAParAgentEnabledStatic(): Boolean {
            return AppConstant.COMPLETION_CONFIG.enableAPDeepSearch &&
                   service<TsingYanSettingStore>().state.enableAParAgentSearch
        }

        /**
         * 获取启用的项目规则列表（静态方法）
         */
        @JvmStatic
        fun getEnabledProjectRulesContent(project: Project): List<Triple<String, String, Boolean>> {
            val projectRulesDir = project.basePath?.let { basePath ->
                File(basePath, PROJECT_RULES_DIR_NAME)
            } ?: return emptyList()
            
            if (!projectRulesDir.exists()) {
                return emptyList()
            }
            
            // 获取缓存的规则状态
            val cacheKey = PROJECT_RULES_CACHE_PREFIX + project.basePath.hashCode()
            val cachedRulesJson = PropertiesComponent.getInstance(project).getValue(cacheKey)
            val cachedRules = if (!cachedRulesJson.isNullOrEmpty()) {
                try {
                    JSONObject.parseArray(cachedRulesJson, ProjectRuleInfo::class.java) ?: emptyList()
                } catch (e: Exception) {
                    emptyList()
                }
            } else {
                emptyList()
            }
            
            val enabledRules = mutableListOf<Triple<String, String, Boolean>>()
            
            try {
                projectRulesDir.listFiles()?.forEach { file ->
                    if (file.isFile && (file.extension == "txt" || file.extension == "md")) {
                        val fileName = file.name
                        
                        // 检查规则是否启用
                        val cachedRule = cachedRules.find { it.fileName == fileName }
                        val isEnabled = cachedRule?.enabled ?: true // 默认启用
                        val fromPublicLibrary = cachedRule?.fromPublicLibrary ?: false // 获取是否来自公共库
                        
                        if (isEnabled) {
                            val content = Files.readAllLines(Paths.get(file.absolutePath), Charsets.UTF_8).joinToString("\n")
                            enabledRules.add(Triple(fileName, content, fromPublicLibrary))
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            
            return enabledRules
        }

        private fun getConfigDir(): File {
            val userHome = System.getProperty("user.home")
            return File(userHome, CONFIG_DIR_NAME)
        }

        private fun getGlobalRulesFile(): File {
            return File(getConfigDir(), GLOBAL_RULES_FILE_NAME)
        }

        private fun getProjectRulesDir(): File? {
            val project = ProjectManager.getInstance().openProjects.firstOrNull()
            return project?.basePath?.let { basePath ->
                File(basePath, PROJECT_RULES_DIR_NAME)
            }
        }

        private fun getProjectCacheKey(): String? {
            val project = ProjectManager.getInstance().openProjects.firstOrNull()
            return project?.basePath?.let { basePath ->
                PROJECT_RULES_CACHE_PREFIX + basePath.hashCode()
            }
        }
    }

    override fun getDisplayName(): String = "规则"

    override fun createComponent(): JComponent {
        loaded = false
        projectRules.clear()
        mainPanel = JPanel(BorderLayout())
        mainPanel!!.border = JBUI.Borders.empty(20)

        // 创建主内容面板
        val contentPanel = JPanel()
        contentPanel.layout = BoxLayout(contentPanel, BoxLayout.Y_AXIS)
        //contentPanel.background = Color.RED

//        // 创建标题
//        val titleLabel = JBLabel("规则")
//        titleLabel.font = UIUtil.getLabelFont().deriveFont(Font.BOLD, 18f)
//        titleLabel.alignmentX = Component.LEFT_ALIGNMENT
//        contentPanel.add(titleLabel)
//        contentPanel.add(Box.createVerticalStrut(10))

        // 创建规则部分
        val rulesLabel = JBLabel("规则")
        rulesLabel.font = UIUtil.getLabelFont().deriveFont(Font.BOLD, 16f)
        rulesLabel.alignmentX = Component.LEFT_ALIGNMENT
        // 添加问号图标
        val rulesPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        rulesPanel.add(rulesLabel)
        val helpIcon = JLabel(AllIcons.General.ContextHelp)
        helpIcon.border = JBUI.Borders.empty(0, 5, 0, 0)
        
        // 添加点击事件监听器，点击后跳转到文档链接
        helpIcon.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent?) {
                val documentUrl = "https://yuque.antfin.com/codefuse/efficient/engfkaw0wvudvg92"
                BrowserUtil.browse(documentUrl)
            }
        })
        
        rulesPanel.add(helpIcon)
        rulesPanel.alignmentX = Component.LEFT_ALIGNMENT
//        rulesPanel.background = Color.WHITE

        // 固定高度
        val fixedHeight = 30
        rulesPanel.preferredSize = Dimension(rulesPanel.preferredSize.width, fixedHeight)
        rulesPanel.maximumSize = Dimension(Int.MAX_VALUE, fixedHeight)
        rulesPanel.minimumSize = Dimension(0, fixedHeight)

        contentPanel.add(rulesPanel)
        contentPanel.add(Box.createVerticalStrut(1))

        // 1. 全局规则部分
        contentPanel.add(createGlobalRulesSection())
        contentPanel.add(Box.createVerticalStrut(20))

        // 2. 项目规则部分
        contentPanel.add(createProjectRulesSection())
        contentPanel.add(Box.createVerticalStrut(1))
        
        // 将内容面板放在滚动面板中
        val scrollPane = JBScrollPane(contentPanel)
        scrollPane.border = BorderFactory.createEmptyBorder()
        scrollPane.verticalScrollBarPolicy = ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED
        scrollPane.horizontalScrollBarPolicy = ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER

        mainPanel!!.add(scrollPane, BorderLayout.CENTER)

        return mainPanel!!
    }

    private fun createGlobalRulesSection(): JPanel {
        val section = JPanel()
        section.layout = BoxLayout(section, BoxLayout.Y_AXIS)
        section.alignmentX = Component.LEFT_ALIGNMENT

        // 全局规则标题
        val titleLabel = JBLabel("用户规则：适用于全部A Par Agent对话；仅限填写纯文本规则，不超过5000字符")
        titleLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 14f)
        titleLabel.alignmentX = Component.LEFT_ALIGNMENT
        section.add(titleLabel)
        section.add(Box.createVerticalStrut(10))

        // 全局规则文本框
        globalRulesTextArea = JBTextArea(5, 50)
        globalRulesTextArea!!.font = UIUtil.getFont(UIUtil.FontSize.NORMAL, null)
        globalRulesTextArea!!.background = JBColor(Color(248, 248, 248), Color(75, 75, 75))
        globalRulesTextArea!!.border = JBUI.Borders.empty(10)
        globalRulesTextArea!!.lineWrap = true
        globalRulesTextArea!!.wrapStyleWord = true

        // 加载已保存的全局规则
        val savedGlobalRules = loadGlobalRules()
        initialGlobalRules = savedGlobalRules

        if (savedGlobalRules.isNotEmpty()) {
            // 如果有保存的内容，直接显示内容，不设置占位符
            globalRulesTextArea!!.text = savedGlobalRules
            globalRulesTextArea!!.foreground = normalColor
        } else {
            // 如果没有保存的内容，设置占位符
            setupPlaceholder(globalRulesTextArea!!, placeholderText)
        }

        val textScrollPane = JBScrollPane(globalRulesTextArea)
        textScrollPane.border = BorderFactory.createLineBorder(JBColor(Color.GRAY, Color.GRAY))
        textScrollPane.alignmentX = Component.LEFT_ALIGNMENT

        // 固定高度
        val fixedTextHeight = 120
        textScrollPane.preferredSize = Dimension(600, fixedTextHeight)
        textScrollPane.maximumSize = Dimension(Int.MAX_VALUE, fixedTextHeight)
        textScrollPane.minimumSize = Dimension(200, fixedTextHeight)

        section.add(textScrollPane)

        // 为整个全局规则部分设置固定高度
        val sectionFixedHeight = 160  // 标题 + 间距 + 文本框 + 边距
        section.preferredSize = Dimension(section.preferredSize.width, sectionFixedHeight)
        section.maximumSize = Dimension(Int.MAX_VALUE, sectionFixedHeight)
        section.minimumSize = Dimension(0, sectionFixedHeight)

        return section
    }

    private fun createProjectRulesSection(): JPanel {
        val section = JPanel()
        section.layout = BoxLayout(section, BoxLayout.Y_AXIS)
        section.alignmentX = Component.LEFT_ALIGNMENT

        // 项目规则标题
        val titleLabel = JBLabel("项目规则：适用于当前项目A Par Agent对话；仅限填写纯文本规则，不超过5000字符。")
        titleLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 14f)
        titleLabel.alignmentX = Component.LEFT_ALIGNMENT
        section.add(titleLabel)
        section.add(Box.createVerticalStrut(10))

        // 添加规则按钮
        val addButton = JButton("新建规则")
        addButton.foreground = JBColor(Color.BLACK, Color.WHITE) // 适配主题：深色主题用白色文字，浅色主题用黑色文字
        addButton.isBorderPainted = false
        addButton.isFocusPainted = false
        addButton.preferredSize = Dimension(120, 30)
        addButton.alignmentX = Component.LEFT_ALIGNMENT
        addButton.addActionListener {
            showAddRuleDialog()
        }

        // 公共规则库 ComboBox
        val comboBox = JComboBox<String>()
        comboBox.addItem("从公共规则库选择")
        comboBox.isEnabled = true
        comboBox.maximumSize = Dimension(200, 30)
        comboBox.preferredSize = Dimension(200, 30)
        comboBox.alignmentX = Component.LEFT_ALIGNMENT
        
        // 存储 ActionListener 引用，用于临时禁用/启用
        var comboBoxActionListener: ActionListener? = null
        
        // 添加弹出菜单监听器，用于控制展开时的选项显示
        comboBox.addPopupMenuListener(object : javax.swing.event.PopupMenuListener {
            override fun popupMenuWillBecomeVisible(e: javax.swing.event.PopupMenuEvent?) {
                LogUtil.info("Popup menu will become visible")
                // 如果下拉列表中有"从公共规则库选择"这一项，就删除掉，不显示
                for (i in 0 until comboBox.itemCount) {
                    if (comboBox.getItemAt(i) == "从公共规则库选择") {
                        // 临时移除 ActionListener 以防止自动选择触发事件
                        if (comboBoxActionListener != null) {
                            comboBox.removeActionListener(comboBoxActionListener)
                        }
                        comboBox.removeItemAt(i)
                        // 设置为不选中任何项
                        comboBox.selectedIndex = -1
                        // 重新添加 ActionListener
                        if (comboBoxActionListener != null) {
                            comboBox.addActionListener(comboBoxActionListener)
                        }
                        break
                    }
                }
            }

            override fun popupMenuWillBecomeInvisible(e: javax.swing.event.PopupMenuEvent?) {
                // 当下拉菜单即将收起时，如果当前没有选中有效项，则重新添加默认提示文本
                if (comboBox.getItemAt(0) != "从公共规则库选择") {
                    comboBox.insertItemAt("从公共规则库选择", 0)
                    comboBox.selectedIndex = 0
                }
                LogUtil.info("Popup menu will become invisible")
            }

            override fun popupMenuCanceled(e: javax.swing.event.PopupMenuEvent?) {
                LogUtil.info("Popup menu canceled")
            }
        })

        comboBox.addMouseListener(object : java.awt.event.MouseAdapter() {
            override fun mousePressed(e: java.awt.event.MouseEvent?) {
                if (!loaded) {
                    e?.consume() // 阻止下拉弹出
                    comboBox.setPopupVisible(false)
                    comboBox.removeAllItems()
                    comboBox.addItem("加载中...")
                    ApplicationManager.getApplication().executeOnPooledThread {
                        val userToken = WikiUtils.getDefaultUserToken()
                        val result = WikiUtils.queryRepoGlobalRuleType(userToken)
                        val ruleTypes = result?.ruleTypes
                        javax.swing.SwingUtilities.invokeLater {
                            comboBox.removeAllItems()
                            if (ruleTypes == null || ruleTypes.isEmpty()) {
                                comboBox.addItem("无可用公共规则")
                                loaded = false
                            } else {
                                ruleTypes.forEach { (_, value) -> comboBox.addItem(value) }
                                projectRules = ruleTypes.toMutableMap()
                                comboBox.selectedIndex = -1
                                loaded = true
                            }
                            comboBox.revalidate()
                            comboBox.repaint()
                            comboBox.showPopup() // 数据加载完后再弹出
                        }
                    }
                }
            }
        })

        // 创建 ActionListener 并存储引用
        comboBoxActionListener = ActionListener {
            val selected = comboBox.selectedItem as? String
            if (selected != null && selected != "从公共规则库选择" && selected != "无可用公共规则" && selected != "加载中...") {
                // 通过selected的name，获取projectRules中的type，然后下载并保存规则内容
                val ruleType = projectRules.entries.find { it.value == selected }?.key
                downloadAndSaveProjectRule(selected, ruleType)
            }
        }
        comboBox.addActionListener(comboBoxActionListener)

        // 横向面板放按钮和下拉框
        val topPanel = JPanel()
        topPanel.layout = BoxLayout(topPanel, BoxLayout.X_AXIS)
        topPanel.isOpaque = false
        topPanel.add(addButton)
//        topPanel.add(Box.createHorizontalStrut(5))
        topPanel.add(comboBox)
        topPanel.alignmentX = Component.LEFT_ALIGNMENT
        val fixedHeight = 30
        topPanel.preferredSize = Dimension(topPanel.preferredSize.width, fixedHeight)
        topPanel.maximumSize = Dimension(Int.MAX_VALUE, fixedHeight)
        topPanel.minimumSize = Dimension(0, fixedHeight)
        section.add(topPanel)
        section.add(Box.createVerticalStrut(10))

        // 项目规则列表容器
        projectRulesPanel = JPanel()
        projectRulesPanel!!.layout = BoxLayout(projectRulesPanel, BoxLayout.Y_AXIS)
        projectRulesPanel!!.alignmentX = Component.LEFT_ALIGNMENT
        
        // 从项目根目录加载已有的规则文件
        val existingRules = loadProjectRulesFromDirectory()
        existingRules.forEach { (fileName, enabled) ->
            addRuleItem(fileName, enabled, false, true) // 第四个参数表示是来自文件系统的规则
        }
        
        // 添加垂直间距填充
        projectRulesPanel!!.add(Box.createVerticalGlue())
        
        // 创建滚动面板，使用更明显的列表背景色
        val scrollPane = JBScrollPane(projectRulesPanel)
        val areaBg = JBColor(Color(248, 248, 248), Color(60, 63, 65)) // 更明显的列表背景色
        scrollPane.background = areaBg
        scrollPane.viewport.background = areaBg
        scrollPane.border = BorderFactory.createLineBorder(JBColor(Color(200, 200, 200), Color(85, 85, 85)), 1) // 添加边框
        scrollPane.verticalScrollBarPolicy = ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED
        scrollPane.horizontalScrollBarPolicy = ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER
        
        // 设置projectRulesPanel的背景色
        projectRulesPanel!!.background = areaBg
        projectRulesPanel!!.isOpaque = true
        
        // 设置固定高度
        val scrollPaneFixedHeight = 200
        val scrollPaneFixedWith = 200
        scrollPane.preferredSize = Dimension(scrollPaneFixedWith, scrollPaneFixedHeight)
        scrollPane.maximumSize = Dimension(Int.MAX_VALUE, scrollPaneFixedHeight)
        scrollPane.minimumSize = Dimension(0, scrollPaneFixedHeight)
        
        section.add(scrollPane)
        
        // 为整个项目规则section设置固定高度，避免高度变化
        val projectSectionFixedHeight = 280 // 标题 + 按钮 + 滚动区域 + 间距
        section.preferredSize = Dimension(section.preferredSize.width, projectSectionFixedHeight)
        section.maximumSize = Dimension(Int.MAX_VALUE, projectSectionFixedHeight)
        section.minimumSize = Dimension(0, projectSectionFixedHeight)

        return section
    }



    private fun addRuleItem(name: String, enabled: Boolean, autoUpdate: Boolean, isFromFileSystem: Boolean = false) {
        val itemPanel = JPanel(BorderLayout())
        itemPanel.isOpaque = false
        itemPanel.border = JBUI.Borders.empty(8, 10, 8, 10) // 上下左右内边距
        itemPanel.maximumSize = Dimension(Int.MAX_VALUE, 44)
        itemPanel.alignmentX = Component.LEFT_ALIGNMENT

        // 左侧：规则名称、自动更新标签和token估算
        val leftPanel = JPanel(FlowLayout(FlowLayout.LEFT, 0, 0))
        leftPanel.isOpaque = false
        val nameLabel = JBLabel(name)
        nameLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 14f)
        leftPanel.add(nameLabel)

        // 检查是否为公共规则库规则，显示"自动更新"标签
        val cachedRule = if (isFromFileSystem) getProjectRuleByFileName(name) else null
        val shouldShowAutoUpdate = autoUpdate || (cachedRule?.fromPublicLibrary == true)

        if (shouldShowAutoUpdate) {
            val autoUpdateLabel = JBLabel("自动更新")
            autoUpdateLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 12f)
            autoUpdateLabel.foreground = JBColor(Color(0, 150, 0), Color(0, 200, 0))
            autoUpdateLabel.border = JBUI.Borders.empty(0, 10, 0, 0)
            leftPanel.add(autoUpdateLabel)
        }

        // 如果是文件系统规则，显示token估算
        if (isFromFileSystem) {
            val tokenEstimate = estimateTokens(name)
            if (tokenEstimate.isNotEmpty()) {
                val tokenLabel = JBLabel(tokenEstimate)
                tokenLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 11f)
                tokenLabel.foreground = JBColor(Color(128, 128, 128), Color(160, 160, 160))
                tokenLabel.border = JBUI.Borders.empty(0, 10, 0, 0)
                leftPanel.add(tokenLabel)
            }
        }

        itemPanel.add(leftPanel, BorderLayout.WEST)

        // 右侧：OnOffButton、编辑、删除按钮
        val rightPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 5, 0))
        rightPanel.isOpaque = false

        val onOffButton = OnOffButton()
        onOffButton.isSelected = enabled
        onOffButton.addActionListener {
            val newState = onOffButton.isSelected
            // 更新缓存状态
            if (isFromFileSystem) {
                val existingRule = getProjectRuleByFileName(name)
                updateProjectRuleCache(
                    fileName = name,
                    enabled = newState,
                    isFromPublicLibrary = existingRule?.fromPublicLibrary ?: false,
                    ruleType = existingRule?.ruleType
                )
                // 保持旧的状态保存逻辑（兼容性）
                saveProjectRuleState(name, newState)
            }
        }
        rightPanel.add(onOffButton)

        val editButton = JButton()
        editButton.icon = AllIcons.Actions.Edit
        editButton.preferredSize = Dimension(30, 30)
        editButton.isBorderPainted = false
        editButton.isContentAreaFilled = false
        editButton.addActionListener {
            // 只支持文件系统规则的编辑
            if (isFromFileSystem) {
                val project = ProjectManager.getInstance().openProjects.firstOrNull()
                val projectRulesDir = getProjectRulesDir()
                if (project != null && projectRulesDir != null) {
                    val ruleFile = File(projectRulesDir, name)
                    if (ruleFile.exists()) {
                        val virtualFile = com.intellij.openapi.vfs.LocalFileSystem.getInstance().refreshAndFindFileByIoFile(ruleFile)
                        if (virtualFile != null) {
                            // 打开文件进行编辑
                            com.intellij.openapi.fileEditor.FileEditorManager.getInstance(project).openFile(virtualFile, true)
                            // 关闭设置页面，让用户专注于编辑文件
                            ApplicationManager.getApplication().invokeLater {
                                closeSettingsDialog()
                            }
                        } else {
                            JOptionPane.showMessageDialog(null, "无法找到文件: ${ruleFile.absolutePath}", "错误", JOptionPane.ERROR_MESSAGE)
                        }
                    } else {
                        JOptionPane.showMessageDialog(null, "文件不存在: ${ruleFile.absolutePath}", "错误", JOptionPane.ERROR_MESSAGE)
                    }
                }
            } else {
                JOptionPane.showMessageDialog(null, "仅支持本地文件规则的编辑", "提示", JOptionPane.INFORMATION_MESSAGE)
            }
        }
        rightPanel.add(editButton)
        val deleteButton = JButton()
        deleteButton.icon = AllIcons.Actions.GC
        deleteButton.preferredSize = Dimension(30, 30)
        deleteButton.isBorderPainted = false
        deleteButton.isContentAreaFilled = false
        deleteButton.addActionListener {
            removeRuleItem(itemPanel, name, isFromFileSystem)
        }
        rightPanel.add(deleteButton)
        itemPanel.add(rightPanel, BorderLayout.EAST)

        // 包装item和分割线
        val wrapper = JPanel()
        wrapper.layout = BoxLayout(wrapper, BoxLayout.Y_AXIS)
        wrapper.isOpaque = false
        wrapper.add(itemPanel)

        // 新规则插入到最前面（最新的在最上面）
        val insertIndex = 0

        // 在第一个item后面加分割线（如果不是列表中唯一的item）
        val hasOtherItems = projectRulesPanel!!.componentCount > 1 // 考虑到还有垂直填充组件
        if (hasOtherItems) {
            val separator = JSeparator()
            separator.maximumSize = Dimension(Int.MAX_VALUE, 1)
            wrapper.add(separator) // 分割线加在底部
        }

        projectRulesPanel!!.add(wrapper, insertIndex)
    }

    private fun removeRuleItem(itemPanel: JPanel, fileName: String, isFromFileSystem: Boolean) {
        // 从UI中移除
        val parentWrapper = itemPanel.parent as? JPanel
        if (parentWrapper != null) {
            projectRulesPanel!!.remove(parentWrapper)
        } else {
            projectRulesPanel!!.remove(itemPanel)
        }

        // 如果是来自文件系统的规则，删除对应的文件和缓存
        if (isFromFileSystem) {
            try {
                val projectRulesDir = getProjectRulesDir()
                if (projectRulesDir != null) {
                    val ruleFile = File(projectRulesDir, fileName)
                    if (ruleFile.exists()) {
                        ruleFile.delete()
                    }
                }

                // 从新的缓存系统中移除
                removeProjectRuleFromCache(fileName)

                // 清除旧缓存中的状态（兼容性）
                val project = ProjectManager.getInstance().openProjects.firstOrNull()
                if (project != null) {
                    val key = PROJECT_RULES_STATE_PREFIX + fileName
                    PropertiesComponent.getInstance(project).unsetValue(key)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        projectRulesPanel!!.revalidate()
        projectRulesPanel!!.repaint()
    }

    private fun showAddRuleDialog() {
        val dialog = JDialog()
        dialog.title = "添加规则"
        dialog.setLocationRelativeTo(mainPanel)
        dialog.isModal = true

        val content = JPanel(BorderLayout())
        content.border = JBUI.Borders.empty(20, 20, 15, 20)

        // 输入区域面板
        val inputPanel = JPanel()
        inputPanel.layout = BoxLayout(inputPanel, BoxLayout.Y_AXIS)
        inputPanel.border = JBUI.Borders.empty(0, 0, 15, 0) // 给底部留出空间

        val label = JBLabel("规则名称:")
        label.alignmentX = Component.LEFT_ALIGNMENT
        inputPanel.add(label)
        inputPanel.add(Box.createVerticalStrut(8))

        val textField = JTextField(20)
        textField.preferredSize = Dimension(320, 30)
        textField.maximumSize = Dimension(Int.MAX_VALUE, 30)
        textField.alignmentX = Component.LEFT_ALIGNMENT
        inputPanel.add(textField)
        
        // 添加垂直填充，确保按钮面板不会挤压输入区域
        inputPanel.add(Box.createVerticalStrut(10))

        val buttonPanel = JPanel(FlowLayout(FlowLayout.CENTER, 10, 10))
        buttonPanel.border = JBUI.Borders.empty(5, 0, 0, 0)
        val okButton = JButton("确定")
        val cancelButton = JButton("取消")

        // 优化按钮样式
        okButton.preferredSize = Dimension(80, 32)
        cancelButton.preferredSize = Dimension(80, 32)

        okButton.addActionListener {
            val ruleName = textField.text.trim()
            if (ruleName.isNotEmpty()) {
                val projectRulesDir = getProjectRulesDir()
                if (projectRulesDir != null) {
                    // 确保规则名称有扩展名
                    val fileName = if (ruleName.endsWith(".md") || ruleName.endsWith(".txt")) {
                        ruleName
                    } else {
                        "$ruleName.md" // 默认使用.md扩展名
                    }

                    val ruleFile = File(projectRulesDir, fileName)

                    // 检查文件是否已存在
                    if (ruleFile.exists()) {
                        JOptionPane.showMessageDialog(dialog, "规则名称重复，请重试", "错误", JOptionPane.ERROR_MESSAGE)
                        return@addActionListener
                    }

                    try {
                        // 确保目录存在
                        if (!projectRulesDir.exists()) {
                            projectRulesDir.mkdirs()
                        }

                        // 创建新文件并写入初始内容
                        val initialContent = "#### **$ruleName**\n\n规则内容:"
                        Files.write(Paths.get(ruleFile.absolutePath), initialContent.toByteArray(Charsets.UTF_8))

                        // 添加到缓存（标记为用户自定义规则）
                        updateProjectRuleCache(fileName, true, false, null)

                        // 添加到UI
                        addRuleItem(fileName, true, false, true) // 新建的文件规则，标记为文件系统规则
                        projectRulesPanel!!.revalidate()
                        projectRulesPanel!!.repaint()
                        dialog.dispose()

                        // 自动打开新创建的文件进行编辑
                        val project = ProjectManager.getInstance().openProjects.firstOrNull()
                        if (project != null) {
                            val virtualFile = com.intellij.openapi.vfs.LocalFileSystem.getInstance().refreshAndFindFileByIoFile(ruleFile)
                            if (virtualFile != null) {
                                com.intellij.openapi.fileEditor.FileEditorManager.getInstance(project).openFile(virtualFile, true)
                            }
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                        JOptionPane.showMessageDialog(dialog, "创建文件失败: ${e.message}", "错误", JOptionPane.ERROR_MESSAGE)
                    }
                } else {
                    JOptionPane.showMessageDialog(dialog, "无法获取项目路径", "错误", JOptionPane.ERROR_MESSAGE)
                }
            }
        }

        cancelButton.addActionListener {
            dialog.dispose()
        }

        buttonPanel.add(okButton)
        buttonPanel.add(cancelButton)

        content.add(inputPanel, BorderLayout.CENTER)
        content.add(buttonPanel, BorderLayout.SOUTH)

        dialog.add(content)
        
        // 让对话框根据内容自动调整大小
        dialog.pack()
        
        // 设置最小尺寸，确保对话框有足够的空间
        val minSize = Dimension(420, 180)
        dialog.minimumSize = minSize
        dialog.preferredSize = Dimension(Math.max(dialog.preferredSize.width, minSize.width), 
                                        Math.max(dialog.preferredSize.height, minSize.height))
        
        // 重新设置位置（pack后需要重新居中）
        dialog.setLocationRelativeTo(mainPanel)
        dialog.isVisible = true
    }

    private fun setupPlaceholder(textArea: JBTextArea, placeholderText: String) {
        // 初始显示占位符
        textArea.text = placeholderText
        textArea.foreground = placeholderColor

        textArea.addFocusListener(object : FocusListener {
            override fun focusGained(e: FocusEvent?) {
                // 只有当前显示的是占位符文本时才清空
                if (textArea.text == placeholderText && textArea.foreground == placeholderColor) {
                    textArea.text = ""
                    textArea.foreground = normalColor
                }
            }

            override fun focusLost(e: FocusEvent?) {
                // 只有当文本为空时才显示占位符
                if (textArea.text.trim().isEmpty()) {
                    textArea.text = placeholderText
                    textArea.foreground = placeholderColor
                }
            }
        })
    }

    override fun isModified(): Boolean {
        val currentGlobalRules = getRealTextAreaContent(globalRulesTextArea)
        return currentGlobalRules != initialGlobalRules
    }

    override fun apply() {
        val globalRules = getRealTextAreaContent(globalRulesTextArea)
        saveGlobalRules(globalRules)
    }

    override fun reset() {
        globalRulesTextArea?.let { textArea ->
            if (initialGlobalRules.isNotEmpty()) {
                textArea.text = initialGlobalRules
                textArea.foreground = normalColor
            } else {
                // 重置为占位符状态，需要重新设置占位符行为
                resetToPlaceholderState(textArea)
            }
        }
    }

    private fun getRealTextAreaContent(textArea: JBTextArea?): String {
        if (textArea == null) return ""
        val currentText = textArea.text
        // 同时检查文本内容和颜色来判断是否是占位符
        return if (currentText == placeholderText && textArea.foreground == placeholderColor) "" else currentText
    }

    /**
     * 重置文本框为占位符状态
     */
    private fun resetToPlaceholderState(textArea: JBTextArea) {
        textArea.text = placeholderText
        textArea.foreground = placeholderColor
        // 确保文本框失去焦点，这样当用户再次点击时会触发focusGained事件
        textArea.transferFocus()
    }

    /**
     * 关闭设置对话框
     */
    private fun closeSettingsDialog() {
        try {
            // 通过组件层次结构找到包含当前面板的顶层窗口
            var component: Component? = mainPanel
            while (component != null) {
                if (component is Window) {
                    // 找到顶层窗口，关闭它
                    if (component is JDialog) {
                        component.dispose()
                    } else if (component is JFrame) {
                        component.dispose()
                    }
                    break
                }
                component = component.parent
            }
        } catch (e: Exception) {
            // 如果关闭失败，也不要影响用户体验
            LogUtil.info("Failed to close settings dialog: ${e.message}")
        }
    }

    // 添加文件操作工具方法
    private fun saveGlobalRules(content: String) {
        try {
            val configDir = getConfigDir()
            if (!configDir.exists()) {
                configDir.mkdirs()
            }

            val globalRulesFile = getGlobalRulesFile()
            Files.write(Paths.get(globalRulesFile.absolutePath), content.toByteArray(Charsets.UTF_8))
            initialGlobalRules = content
        } catch (e: IOException) {
            e.printStackTrace()
            // 可以添加错误提示给用户
        }
    }

    private fun loadGlobalRules(): String {
        return try {
            val globalRulesFile = getGlobalRulesFile()
            if (globalRulesFile.exists()) {
                Files.readAllLines(Paths.get(globalRulesFile.absolutePath), Charsets.UTF_8).joinToString("\n")
            } else {
                ""
            }
        } catch (e: IOException) {
            e.printStackTrace()
            ""
        }
    }

    // 项目规则和缓存管理方法
    private fun loadProjectRulesFromDirectory(): List<Pair<String, Boolean>> {
        val projectRulesDir = getProjectRulesDir()
        if (projectRulesDir == null || !projectRulesDir.exists()) {
            return emptyList()
        }

        val cachedRules = loadProjectRulesCache()
        val ruleFiles = mutableListOf<Pair<String, Boolean>>()

        try {
            projectRulesDir.listFiles()?.forEach { file ->
                if (file.isFile && (file.extension == "txt" || file.extension == "md")) {
                    val fileName = file.name

                    // 优先从缓存获取状态，如果缓存中没有则默认为true
                    val cachedRule = cachedRules.find { it.fileName == fileName }
                    val isEnabled = cachedRule?.enabled ?: true // 默认开启

                    // 如果缓存中没有该规则，添加到缓存（标记为用户自定义）
                    if (cachedRule == null) {
                        updateProjectRuleCache(fileName, isEnabled, false, null)
                    }

                    ruleFiles.add(Pair(fileName, isEnabled))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return ruleFiles
    }

    private fun getProjectRuleState(fileName: String): Boolean {
        val project = ProjectManager.getInstance().openProjects.firstOrNull()
        return if (project != null) {
            val key = PROJECT_RULES_STATE_PREFIX + fileName
            PropertiesComponent.getInstance(project).getBoolean(key, true) // 默认开启
        } else {
            true
        }
    }

    private fun saveProjectRuleState(fileName: String, enabled: Boolean) {
        val project = ProjectManager.getInstance().openProjects.firstOrNull()
        if (project != null) {
            val key = PROJECT_RULES_STATE_PREFIX + fileName
            PropertiesComponent.getInstance(project).setValue(key, enabled)
        }
    }

    private fun downloadAndSaveProjectRule(ruleName: String, ruleType: String?) {
        if (ruleType == null) return

        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                val userToken = WikiUtils.getDefaultUserToken()
                val result = WikiUtils.queryRepoGlobalRuleContent(ruleType, userToken)
                val content = result?.content
                if (content != null && content.isNotEmpty()) {
                    val projectRulesDir = getProjectRulesDir()
                    if (projectRulesDir != null) {
                        // 确保目录存在
                        if (!projectRulesDir.exists()) {
                            projectRulesDir.mkdirs()
                        }

                        // 保存文件，使用.md扩展名
                        val fileName = if (ruleName.endsWith(".md") || ruleName.endsWith(".txt")) {
                            ruleName
                        } else {
                            "$ruleName.md"
                        }

                        val ruleFile = File(projectRulesDir, fileName)
                        Files.write(Paths.get(ruleFile.absolutePath), content.toByteArray(Charsets.UTF_8))

                        // 在UI线程中更新界面
                        javax.swing.SwingUtilities.invokeLater {
                            // 检查是否已经存在该规则项，如果不存在则添加
                            if (!isRuleItemExists(fileName)) {
                                // 添加到缓存（标记为公共规则库规则）
                                updateProjectRuleCache(fileName, true, true, ruleType)

                                addRuleItem(fileName, true, true, true) // 公共规则库下载的规则设置autoUpdate为true
                                projectRulesPanel?.revalidate()
                                projectRulesPanel?.repaint()
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun isRuleItemExists(fileName: String): Boolean {
        // 检查当前UI中是否已经存在该规则项
        try {
            projectRulesPanel?.components?.forEach { wrapper ->
                if (wrapper is JPanel) {
                    wrapper.components.forEach { itemPanel ->
                        if (itemPanel is JPanel && itemPanel.layout is BorderLayout) {
                            val westComponent = itemPanel.components.firstOrNull()
                            if (westComponent is JPanel) {
                                westComponent.components.forEach { comp ->
                                    if (comp is JBLabel && comp.text == fileName) {
                                        return true
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    private fun estimateTokens(fileName: String): String {
        return try {
            val projectRulesDir = getProjectRulesDir()
            if (projectRulesDir != null) {
                val ruleFile = File(projectRulesDir, fileName)
                if (ruleFile.exists()) {
                    val content = Files.readAllLines(Paths.get(ruleFile.absolutePath), Charsets.UTF_8).joinToString("\n")
                    val charCount = content.length
                    // 简单估算：平均3个字符对应1个token（适用于中英文混合）
                    val estimatedTokens = (charCount / 2.5).toInt()
                    when {
                        estimatedTokens < 100 -> "大约多消耗${estimatedTokens}tokens"
                        estimatedTokens < 1000 -> "大约多消耗${(estimatedTokens / 100.0).let { "%.1f".format(it) }}k tokens"
                        else -> "大约多消耗${(estimatedTokens / 1000.0).let { "%.1f".format(it) }}k tokens"
                    }
                } else {
                    ""
                }
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }

    // 项目规则缓存管理方法
    private fun saveProjectRulesCache(rules: List<ProjectRuleInfo>) {
        try {
            val project = ProjectManager.getInstance().openProjects.firstOrNull()
            val cacheKey = getProjectCacheKey()
            if (project != null && cacheKey != null) {
                val jsonString = JSONObject.toJSONString(rules)
                PropertiesComponent.getInstance(project).setValue(cacheKey, jsonString)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun loadProjectRulesCache(): List<ProjectRuleInfo> {
        return try {
            val project = ProjectManager.getInstance().openProjects.firstOrNull()
            val cacheKey = getProjectCacheKey()
            if (project != null && cacheKey != null) {
                val jsonString = PropertiesComponent.getInstance(project).getValue(cacheKey)
                if (!jsonString.isNullOrEmpty()) {
                    JSONObject.parseArray(jsonString, ProjectRuleInfo::class.java) ?: emptyList()
                } else {
                    emptyList()
                }
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }

    private fun updateProjectRuleCache(fileName: String, enabled: Boolean, isFromPublicLibrary: Boolean, ruleType: String? = null) {
        val currentCache = loadProjectRulesCache().toMutableList()
        val existingIndex = currentCache.indexOfFirst { it.fileName == fileName }

        val updatedRule = ProjectRuleInfo(fileName, enabled, isFromPublicLibrary, ruleType)

        if (existingIndex >= 0) {
            currentCache[existingIndex] = updatedRule
        } else {
            currentCache.add(updatedRule)
        }

        saveProjectRulesCache(currentCache)
    }

    private fun removeProjectRuleFromCache(fileName: String) {
        val currentCache = loadProjectRulesCache().toMutableList()
        currentCache.removeAll { it.fileName == fileName }
        saveProjectRulesCache(currentCache)
    }

    // 便于外部获取的API方法
    fun getEnabledProjectRules(): List<ProjectRuleInfo> {
        return loadProjectRulesCache().filter { it.enabled }
    }

    fun getProjectRulesByType(isFromPublicLibrary: Boolean): List<ProjectRuleInfo> {
        return loadProjectRulesCache().filter { it.fromPublicLibrary == isFromPublicLibrary }
    }
    
    fun getProjectRuleByFileName(fileName: String): ProjectRuleInfo? {
        return loadProjectRulesCache().find { it.fileName == fileName }
    }
    

}