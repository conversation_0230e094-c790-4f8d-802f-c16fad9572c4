package com.alipay.tsingyan.linemarker

import com.alipay.tsingyan.utils.AppConstant
import com.intellij.psi.PsiElement

/**
 * <AUTHOR>
 *
 * 2023-12-11 19:10:46
 *
 */
class LineMarkerUtils {
    val funName1:String = "解释代码"
    var funIsEnable:Boolean = false

    fun getLineMarkerData(psiElement: PsiElement): LineMarkerData? {
        val realPsiElement: PsiElement?
        realPsiElement = if (AppConstant.IDEA_FULL_NAME.contains("IntelliJ IDEA") ||  AppConstant.IDEA_FULL_NAME.contains("Android Studio")) { //java
            psiElement.parent
        } else if (AppConstant.IDEA_FULL_NAME.contains("Android Studio")) { //js
            psiElement.parent
        } else if (AppConstant.IDEA_FULL_NAME.contains("PyCharm")) { //python
            psiElement.parent
        } else if (AppConstant.IDEA_FULL_NAME.contains("Go")) { //go
            psiElement
        } else if (AppConstant.IDEA_FULL_NAME.contains("CLion")) {  // c++
            psiElement.parent
        } else {
            return null
        }
        return if (realPsiElement == null) {
            null
        } else {
            val textRange = realPsiElement.textRange
            val file = psiElement.containingFile
            if (textRange != null) {
                val endOffSet = textRange.endOffset
                val startOffSet = textRange.startOffset
                val document = file.viewProvider.document
                val startLineNumber = document.getLineNumber(startOffSet) + 1
                val endLineNumber = document.getLineNumber(endOffSet) + 1
                LineMarkerData(endOffSet, startOffSet, startLineNumber, endLineNumber)
            } else {
                null
            }
        }
    }
}