package com.alipay.tsingyan.linemarker

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.IconConstant
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.navigation.GotoRelatedItem
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.util.NotNullFactory
import com.intellij.psi.PsiElement

/**
 * author:jianzhi
 *
 * 2024-01-11 17:14:45
 *
 */
class TSLineMarker : RelatedItemLineMarkerProvider() {

    val list: List<String> =
        mutableListOf("JS:FUNCTION_DECLARATION", "JS:TYPESCRIPT_FUNCTION", "JS:EXPORT_DEFAULT_ASSIGNMENT")
    val supportFileSuffix: List<String> = mutableListOf(
        "ts",
        "js",
        "tsx",
        "jsx",
        "javascript",
        "_js",
        "es",
        "es6",
        "gs",
        "jake",
        "jslib",
        "jsm",
        "jss",
        "mjs",
        "njs",
        "sjs"
    )
    private val settingData = service<TsingYanSettingStore>()

    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!settingData.state.enableLineMarkerSwitch){
            return
        }
        val fileSuffix = fileSuffix(element.getContainingFile().getName())
        if (supportFileSuffix.contains(fileSuffix)) {
            if (isShowFounction(element) || isShowClass(element)) {
                val lineMarkerData = getLineMarkerData(element) ?: return
                LogUtil.debug(
                    "TSLineMarker collectNavigationMarkers ${element.text} ${
                        JSON.toJSONString(
                            lineMarkerData
                        )
                    }"
                )
                val targetsFactory: NotNullFactory<Collection<GotoRelatedItem>> =
                    NotNullFactory<Collection<GotoRelatedItem>> { // 返回一个空的不可变集合
                        emptyList()
                    }
                val lineMarkerInfo = RelatedItemLineMarkerInfo(
                    element, element.textRange, IconConstant.LINE_MARKER_ICON,
                    { var0: Any? -> null },
                    GutterHandler(lineMarkerData, false),
                    GutterIconRenderer.Alignment.RIGHT,
                    targetsFactory
                )
                result.add(lineMarkerInfo)
            }
        }
    }

    private fun getLineMarkerData(psiElement: PsiElement): LineMarkerData? {
        val realPsiElement: PsiElement? = psiElement.parent
        val textRange = realPsiElement?.textRange
        val file = psiElement.containingFile
        if (textRange != null) {
            val endOffSet = textRange.endOffset
            val startOffSet = textRange.startOffset
            val document = file.viewProvider.document
            val startLineNumber = document.getLineNumber(startOffSet)
            val endLineNumber = document.getLineNumber(endOffSet) + 1
            return LineMarkerData(endOffSet, startOffSet, startLineNumber, endLineNumber)
        }
        return null
    }

    private fun isShowFounction(element: PsiElement): Boolean {
        if (element.isValid && element.parent != null && element.parent.node != null) {
            LogUtil.debug(element.text)
            LogUtil.debug("element.parent.node.elementType.toString() ${element.parent.node.elementType} ${element.node.elementType}")
        }

        return element.isValid && element.parent.node != null && list.contains(element.parent.node.elementType.toString()) && (element.node.elementType.toString()
            .equals("JS:FUNCTION_KEYWORD", ignoreCase = true) || element.node.elementType.toString()
            .equals("JS:FUNCTION_EXPRESSION", ignoreCase = true))
    }

    private fun isShowClass(element: PsiElement): Boolean {
        return element.isValid && element.parent != null && element.parent.node != null && "JS:TYPESCRIPT_CLASS" == element.parent.node.elementType.toString() && "JS:IDENTIFIER" == element.node.elementType.toString()
    }

    private fun fileSuffix(fileName: String): String {
        val index = fileName.lastIndexOf(".")
        return if (index == -1) "" else fileName.substring(index + 1)
    }

}