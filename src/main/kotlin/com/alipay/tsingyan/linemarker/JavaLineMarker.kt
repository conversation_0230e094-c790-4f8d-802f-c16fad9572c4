package com.alipay.tsingyan.linemarker

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.IconConstant
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.navigation.GotoRelatedItem
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.util.NotNullFactory
import com.intellij.psi.*

/**
 * <AUTHOR>
 *
 * 2023-12-11 19:10:46
 *
 */
class JavaLineMarker : RelatedItemLineMarkerProvider() {

    val TAG = "LineMarker"
    private val settingData = service<TsingYanSettingStore>()

    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!settingData.state.enableLineMarkerSwitch){
            return
        }
        val fileName: String = element.getContainingFile().getFileType().getName()
        if ("JAVA".equals(fileName, ignoreCase = true)){
            if (!isElementValid(element)){
                return
            }

            //判断是否为方法名前面
            if (isElementIsMethod(element)){
                val produceLineMarkerInfo = produceLineMarkerInfo(element, true)
                if (produceLineMarkerInfo != null){
                    result.add(produceLineMarkerInfo)
                    return
                }
            }

            //判断是否为类
            if (isElementIsClass(element)){
                //排除泛型<T>这种类型的
                if (isTypeClass(element)){
                    return
                }
                val produceLineMarkerInfo = produceLineMarkerInfo(element, false)
                if (produceLineMarkerInfo != null){
                    result.add(produceLineMarkerInfo)
                    return
                }
            }
        }
    }

    private fun produceLineMarkerInfo(element: PsiElement, isMethod: Boolean):RelatedItemLineMarkerInfo<PsiElement>?{
        val lineMarkerData = getLineMarkerData(element) ?: return null
        val targetsFactory: NotNullFactory<Collection<GotoRelatedItem>> = NotNullFactory<Collection<GotoRelatedItem>> { // 返回一个空的不可变集合
            emptyList()
        }
        val lineMarkerInfo = RelatedItemLineMarkerInfo(
            element, element.textRange, IconConstant.LINE_MARKER_ICON,
            { var0: Any? -> null },
            GutterHandler(lineMarkerData, hasTestAnnotation(element), isMethod),
            GutterIconRenderer.Alignment.RIGHT,
            targetsFactory
        )
        return lineMarkerInfo
    }

    private fun isClassName(psiMethod: PsiMethod): Boolean {
        return if (psiMethod.containingClass != null) psiMethod.name == psiMethod.containingClass!!.name else false
    }

    private fun isShow(element: PsiElement): Boolean {
        return isElementValid(element) && (isElementIsMethod(element) || isElementIsClass(element))
    }

    private fun isElementValid(element: PsiElement?): Boolean {
        return element != null &&
                element.isValid && element.project != null && element.containingFile != null &&
                element.containingFile.isPhysical &&
                ApplicationManager.getApplication().isReadAccessAllowed
    }

    private fun isElementIsMethod(element: PsiElement?): Boolean {
        return element is PsiIdentifier && element.getParent() != null && element.getParent() is PsiMethod && !isClassName((element.getParent() as PsiMethod)!!)
    }

    private fun isElementIsClass(element: PsiElement?): Boolean {
        if (element is PsiIdentifier  && element.getParent() != null && element.getParent() is PsiClass && !(element.getParent() is PsiMethod)) {
            // 可能需要进一步的检查，比如是否是类的名称
            val psiClass = element.getParent() as PsiClass;
            if (psiClass.nameIdentifier == element) {
                return true
            }
        }
        return false
    }

    private fun supportIdeaType() : Boolean {
        return AppConstant.IDEA_FULL_NAME.contains("IntelliJ IDEA") || AppConstant.IDEA_FULL_NAME.contains("Android Studio")
    }

    fun getLineMarkerData(psiElement: PsiElement): LineMarkerData? {
        val realPsiElement: PsiElement? = psiElement.parent
        val textRange = realPsiElement?.textRange
        val file = psiElement.containingFile
        if (textRange != null) {
            val endOffSet = textRange.endOffset
            val startOffSet = textRange.startOffset
            val document = file.viewProvider.document
            val startLineNumber = document.getLineNumber(startOffSet)
            val endLineNumber = document.getLineNumber(endOffSet) + 1
            return LineMarkerData(endOffSet, startOffSet, startLineNumber, endLineNumber)
        }
        return null
    }

    fun isTypeClass(psiElement: PsiElement): Boolean {
        val realPsiElement: PsiElement? = psiElement.parent
        val textRange = realPsiElement?.textRange
        val file = psiElement.containingFile
        try {
            if (textRange != null) {
                val endOffSet = textRange.endOffset
                val startOffSet = textRange.startOffset
                val document = file.viewProvider.document
                val prevChar: Char? = if (startOffSet > 0) document.charsSequence[startOffSet - 1] else null
                if ('<'==prevChar){
                    return true
                }
                val nextChar: Char? = if (endOffSet < document.textLength) document.charsSequence[endOffSet] else null
                if ('>'==nextChar) {
                    return true
                }
            }
        } catch (e:Throwable){
            LogUtil.error("isTypeClass error", e)
        }

        return false
    }

    /**
     * 这段代码通过使用 IntelliJ IDEA 的 PSI 体系来实现对注解的检查。具体而言，
     * 它检查当前 PsiElement 的父元素，判断父元素是否是可以包含修饰符的类型，
     * 并从中查找是否包含 @Test 注解。
     */
    private fun hasTestAnnotation(element: PsiElement): Boolean {
        val parent = element.parent
        if (parent is PsiModifierListOwner) {
            val annotations = parent.modifierList?.annotations
            if (annotations != null) {
                for (annotation in annotations) {
                    if (annotation.qualifiedName == "org.junit.Test") {
                        return true
                    }
                }
            }
        }
        return false
    }

}