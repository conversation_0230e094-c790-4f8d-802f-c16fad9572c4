package com.alipay.tsingyan.linemarker

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.IconConstant
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerInfo
import com.intellij.codeInsight.daemon.RelatedItemLineMarkerProvider
import com.intellij.navigation.GotoRelatedItem
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.util.NotNullFactory
import com.intellij.psi.PsiElement
import org.apache.http.util.TextUtils
import java.util.regex.Pattern

/**
 * <AUTHOR>
 *
 * 2023-12-26 12:08:21
 * @version PythonLineMarker.kt
 */
class PythonLineMarker: RelatedItemLineMarkerProvider() {

    private val settingData = service<TsingYanSettingStore>()
    override fun collectNavigationMarkers(
        element: PsiElement,
        result: MutableCollection<in RelatedItemLineMarkerInfo<*>>
    ) {
        if (!settingData.state.enableLineMarkerSwitch){
            return
        }
        val fileName: String = element.getContainingFile().getFileType().getName()
        if ("PYTHON".equals(fileName, ignoreCase = true)){
            if(isShow(element) && isShow2(element)){
                val lineMarkerData = getLineMarkerData(element) ?: return
                LogUtil.debug("PythonLineMarker collectNavigationMarkers ${element.text} ${JSON.toJSONString(lineMarkerData)}")
                val targetsFactory: NotNullFactory<Collection<GotoRelatedItem>> = NotNullFactory<Collection<GotoRelatedItem>> { // 返回一个空的不可变集合
                    emptyList()
                }
                val lineMarkerInfo = RelatedItemLineMarkerInfo(
                    element, element.textRange, IconConstant.LINE_MARKER_ICON,
                    { var0: Any? -> null },
                    GutterHandler(lineMarkerData, false),
                    GutterIconRenderer.Alignment.RIGHT,
                    targetsFactory
                )
                result.add(lineMarkerInfo)
            }
        }
    }

    fun getLineMarkerData(psiElement: PsiElement): LineMarkerData? {
        val realPsiElement: PsiElement? = psiElement.parent
        val textRange = realPsiElement?.textRange
        val file = psiElement.containingFile
        if (textRange != null) {
            val endOffSet = textRange.endOffset
            val startOffSet = textRange.startOffset
            val document = file.viewProvider.document
            val startLineNumber = document.getLineNumber(startOffSet)
            val endLineNumber = document.getLineNumber(endOffSet) + 1
            return LineMarkerData(endOffSet, startOffSet, startLineNumber, endLineNumber)
        }
        return null
    }

    /**
     * 过滤出函数定义是DEF_KEYWORD且为FUNCTION_DECLARATION的element
     */
    fun isShow(element: PsiElement): Boolean {
        return element.isValid && element.node.elementType.toString()
            .equals(
                "Py:DEF_KEYWORD",
                ignoreCase = true
            ) && element.parent != null && element.parent.node.elementType.toString()
            .equals("Py:FUNCTION_DECLARATION", ignoreCase = true)
    }

    /**
     * 过滤掉以_和__开头的函数
     */
    fun isShow2(element: PsiElement): Boolean {
        if (TextUtils.isEmpty(element.parent?.node?.text)){
            return false
        }
        val pattern = Pattern.compile("def\\s+([a-zA-Z_][a-zA-Z_0-9]*)\\s*\\(.*?\\):")
        val matcher = pattern.matcher(element.parent.node.text)

        var methodName: String
        do {
            if (!matcher.find()) {
                return true
            }

            methodName = matcher.group(1)
        } while (!methodName.startsWith("_") && !methodName.startsWith("__"))

        LogUtil.debug("python method start with _ : $methodName")
        return false
    }

}