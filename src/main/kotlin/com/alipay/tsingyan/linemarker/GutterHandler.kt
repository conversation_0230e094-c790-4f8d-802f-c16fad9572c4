package com.alipay.tsingyan.linemarker

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.comment.AddCommentRequestBean
import com.alipay.tsingyan.model.completion.IntentionType
import com.alipay.tsingyan.model.enums.PermissionsEnum
import com.alipay.tsingyan.model.enums.TABKey
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.chat.service.AnalysisCodeService
import com.alipay.tsingyan.services.codesuggestion.service.CodeSuggestionService
import com.alipay.tsingyan.services.comment.service.AddCommentService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.file.RecentFilesService
import com.alipay.tsingyan.services.test.action.TestService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.COMPLETION_CONFIG
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_ANALYSIS
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_CODE_SUGGESTION
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_COMMENT
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_INLINECHAT
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_OPEN_AP
import com.alipay.tsingyan.utils.AppConstant.TEXT_STATIC_OPEN_WINDOW
import com.alipay.tsingyan.utils.ChatMessageUtil
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ui.UIUtils
import com.intellij.codeInsight.daemon.GutterIconNavigationHandler
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.SelectionModel
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.psi.PsiElement
import org.apache.http.util.TextUtils
import java.awt.Point
import java.awt.event.MouseEvent


/**
 * <AUTHOR>
 *
 * 2023-12-11 19:10:46
 *
 */
class GutterHandler(private val lineMarkerData: LineMarkerData, private val isTestAnnotation: Boolean, private val isMethod: Boolean = true): GutterIconNavigationHandler<PsiElement> {
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    val addCommentService = service<AddCommentService>()
    val tracerService = service<CodeFuseTracerService>()

    override fun navigate(e: MouseEvent, element: PsiElement) {
        LogUtil.debug("navigate 1...")

        val dataContext: DataContext = DataManager.getInstance().getDataContext(e.component)
        val editor: Editor? = CommonDataKeys.EDITOR.getData(dataContext)
        val isTestFile = CommonUtils.isInTestBundle(editor)
        val isTestFileBoolean : Boolean
        val testName: String
        if (isTestFile || isTestAnnotation){
            isTestFileBoolean = true
            testName = AppConstant.MORE_UNIT_TEST
        } else {
            isTestFileBoolean = false
            testName = if (COMPLETION_CONFIG.intentionType == IntentionType.UNIT_TEST.name) AppConstant.TEXT_UNIT_TEST else AppConstant.TEXT_AUTO
        }

        val listActions: MutableList<AnAction> = mutableListOf<AnAction>()
        if (isMethod && AppConstant.INLINE_CHAT_ENABLE && !AppConstant.IS_CLOUD_IDE_211){
            listActions.add(object : AnAction(TEXT_STATIC_INLINECHAT, TEXT_STATIC_INLINECHAT, null) {
                override fun actionPerformed(e: AnActionEvent) {
                    val project: Project = e.project ?: return
                    if (project.service<FileAITagService>().isTextToCodeEnableWithWarning(e)) {
                        return
                    }

                    val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                    if (!TextUtils.isEmpty(selectedText)){
                        if (lineMarkerData.endLineNumber - lineMarkerData.startLineNumber > 700){
                            ApplicationManager.getApplication().invokeLater {
                                NotificationUtils.notifyMessage("CodeFuse", AppConstant.INLINE_CHAT_NUMS_TIPS, e.project, null)
                            }
                            return
                        }
                        LogUtil.info("actionGroup 1.5... $TEXT_STATIC_INLINECHAT")
                        val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
                        editor ?: return// 如果编辑器为空，则直接返回
                        project.service<InlineChatService>().showInlineChat(project, editor, ClickType.ICON)
                    }
                }
            })
        }
        listActions.add(object : AnAction(TEXT_STATIC_COMMENT, TEXT_STATIC_COMMENT, null) {
            override fun actionPerformed(e: AnActionEvent) {
                val project: Project = e.project ?: return
                if (project.service<FileAITagService>().isTextToCodeEnableWithWarning(e)){
                    return
                }

                val selectCode = selectCodeByLineNumber(e, lineMarkerData)
                if (!TextUtils.isEmpty(selectCode)){
                    LogUtil.info("actionGroup 1... $TEXT_STATIC_COMMENT")
                    val requestBean = AddCommentRequestBean()
                    requestBean.question = selectCode
                    requestBean.enterType = ClickType.ICON.name
                    val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
                    editor ?: return// 如果编辑器为空，则直接返回
                    addCommentService.requestComment(requestBean, editor)
//                    tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_COMMENT, null)
                }
            }
        })
        listActions.add(object : AnAction(TEXT_STATIC_ANALYSIS, TEXT_STATIC_ANALYSIS, null) {
            override fun actionPerformed(e: AnActionEvent) {
                val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                if (!TextUtils.isEmpty(selectedText)){
                    LogUtil.info("actionGroup 2... $TEXT_STATIC_ANALYSIS")
                    val project: Project? = e.project
                    project ?: return// 如果项目为空，则直接返回
                    val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
                    editor ?: return// 如果编辑器为空，则直接返回
                    val fileContent = editor.document.text
                    val analysisText = "解释下代码意图：\n"+"```\n$selectedText\n```"
                    project.service<AnalysisCodeService>().analysisyToWebView(project, analysisText, fileContent, ClickType.ICON)
//                    tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_ANALYSIS, null)
                }
            }
        })
        listActions.add(object : AnAction(TEXT_STATIC_CODE_SUGGESTION, TEXT_STATIC_CODE_SUGGESTION, null) {
            override fun actionPerformed(e: AnActionEvent) {
                val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                if (!TextUtils.isEmpty(selectedText)){
                    LogUtil.info("actionGroup 5... $TEXT_STATIC_CODE_SUGGESTION")
                    val project: Project? = e.project
                    project ?: return// 如果项目为空，则直接返回
                    val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
                    editor ?: return// 如果编辑器为空，则直接返回
                    val analysisText = "优化以下代码：\n"+"```\n$selectedText\n```"
                    e.project?.service<CodeSuggestionService>()?.codeSuggestion(e.project!!, analysisText, ClickType.ICON)
//                    tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_CODE_SUGGESTION, null)
                }
            }
        })
        listActions.add(object : AnAction(testName, testName, null) {
            override fun actionPerformed(e: AnActionEvent) {
                val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                if (!TextUtils.isEmpty(selectedText)){
                    LogUtil.info("actionGroup 3... $testName")
                    val project: Project? = e.project
                    project ?: return// 如果项目为空，则直接返回
                    val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
                    editor ?: return// 如果编辑器为空，则直接返回
                    val selectionModel = editor.selectionModel
                    val fileContent = editor.document.text
                    val startOffset = selectionModel.selectionStart
                    val endOffset = selectionModel.selectionEnd
                    val document = editor.document
                    val startLine = document.getLineNumber(startOffset)
                    val endLine = document.getLineNumber(endOffset)
                    val language = AntEditorUtil.getLanguage(editor)
                    if (COMPLETION_CONFIG.intentionType == IntentionType.UNIT_TEST.name){
                        e.project?.service<TestService>()?.generateUnitTest(e.project, selectedText!!, fileContent, language, ClickType.ICON, IntentionType.UNIT_TEST, startOffset, endOffset, startLine, endLine, isTestFileBoolean)
                    } else {
                        e.project?.service<TestService>()?.generateUnitTest(e.project, selectedText!!, fileContent, language, ClickType.ICON, IntentionType.AUTO, startOffset, endOffset, startLine, endLine, isTestFileBoolean)
                    }
//                    tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_TEST, null)
                }

            }
        })
        if (CommonUtils.isSupportMcpFeature()){
            listActions.add(object : AnAction(TEXT_STATIC_OPEN_AP, TEXT_STATIC_OPEN_AP, null) {
                override fun actionPerformed(e: AnActionEvent) {
                    val project: Project? = e.project
                    project ?: return   // 如果项目为空，则直接返回
                    val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                    if (!TextUtils.isEmpty(selectedText) && e.project != null){
                        LogUtil.info("actionGroup 6... $TEXT_STATIC_OPEN_WINDOW")
                        val toolWindow = ToolWindowManager.getInstance(e.project!!)
                            .getToolWindow("CodeFuse")
                            ?: return
                        if (!toolWindow.isVisible) {
                            toolWindow.show {}
                        }
                        toolWindow.activate(null)
                        UIUtils.switchToolWindowTab(project, TABKey.AI_PARTNER)
                        // 使用通用函数发送CHAT_DATA_V2消息
                        ChatMessageUtil.sendChatDataV2MessageWithCode(
                            project,
                            selectedText!!,
                            AppConstant.POSE_AS_SESSION,
                            ClickType.ICON,
                            CommonUtils.getSelectedCodeInfo()
                        )
                    }
                }
            })
        } else {
            listActions.add(object : AnAction(TEXT_STATIC_OPEN_WINDOW, TEXT_STATIC_OPEN_WINDOW, null) {
                override fun actionPerformed(e: AnActionEvent) {
                    val project: Project? = e.project
                    project ?: return   // 如果项目为空，则直接返回
                    val selectedText = selectCodeByLineNumber(e, lineMarkerData)
                    if (!TextUtils.isEmpty(selectedText) && e.project != null){
                        LogUtil.info("actionGroup 6... $TEXT_STATIC_OPEN_WINDOW")
                        val toolWindow = ToolWindowManager.getInstance(e.project!!)
                            .getToolWindow("CodeFuse")
                            ?: return
                        if (!toolWindow.isVisible) {
                            toolWindow.show {}
                        }
                        toolWindow.activate(null)
                        val webView = e.project?.getService(WebViewService::class.java)?.getWebView()
                        if (webView != null){
                            val messageModel = MessageModel()
                            messageModel.actionType = WebActionTypeEnum.TO_JS.name
                            messageModel.target = WebTargetEnum.CHAT_DATA.name
                            val jsonObject = JSONObject()
                            jsonObject.put("question", selectedText)
                            jsonObject.put("intention", AppConstant.POSE_AS_SESSION)
                            jsonObject.put("enterType", ClickType.ICON.toString())
                            jsonObject.put("recentFilesInfo", e.project?.getService(RecentFilesService::class.java)?.getRecentFiles(project))
                            messageModel.message = jsonObject.toString()
                            LogUtil.info("OPEN_WINDOW sendMsgToBrowser ${JSON.toJSONString(messageModel)}", false)
                            webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
//                        tracerService.submitTypeAndData(project, TraceTypeEnum.CLICK_ASK, null)
                        }
                    }
                }
            })
        }


//        if (isTestAnnotation){
//            listActions.add(object : AnAction("这是一个测试", "这是一个测试", null) {
//                override fun actionPerformed(e: AnActionEvent) {
//                    val selectedText = selectCodeByLineNumber(e, lineMarkerData)
//                }
//            })
//        }

        val actionGroup: ActionGroup = DefaultActionGroup(listActions)

        val popup = JBPopupFactory.getInstance().createActionGroupPopup(
            "",  // Title of the popup
            actionGroup,  // The action group for the popup
            dataContext,  // Data context for the actions
            JBPopupFactory.ActionSelectionAid.SPEEDSEARCH,  // Fast search support
            false // Whether to show disabled actions
        )

        // Show the popup at the location of the mouse event
        if (e.component.isShowing()) {
            // If the component is showing (visible), show the popup
            val location: Point = e.component.locationOnScreen
            popup.showInScreenCoordinates(e.component, Point(location.x + e.x, location.y + e.y))
        }
    }

    fun selectCodeByLineNumber(e: AnActionEvent, lineMarkerData: LineMarkerData): String?{
        val project: Project? = e.project
        project ?: return "" // 如果项目为空，则直接返回
        val editor: Editor? = FileEditorManager.getInstance(project).selectedTextEditor
        editor ?: return ""// 如果编辑器为空，则直接返回
        if (!checkLogin()){
            NotificationUtils.showBeginLoginMessage(project)
            return ""
        }
        val document: Document = editor.document
        //check 数据合理性
        if (lineMarkerData.startLineNumber <= 0){
            lineMarkerData.startLineNumber = 0
        }
        if (lineMarkerData.startOffSet <= 0){
            lineMarkerData.startOffSet = 0
        }
        if (lineMarkerData.endOffSet>document.textLength){
            lineMarkerData.endOffSet = document.textLength
        }
        lineMarkerData.startOffSet = document.getLineStartOffset(lineMarkerData.startLineNumber)
        val selectionModel: SelectionModel = editor.selectionModel
        selectionModel.setSelection(lineMarkerData.startOffSet, lineMarkerData.endOffSet)

        return selectionModel.selectedText
    }

    /**
     * 检查登录状态
     */
    fun checkLogin(): Boolean {
        val permission = localUserStore.getUserInfoModel()?.permissionsGpu
        permission?.let {
            if (permission == PermissionsEnum.ACCESS.name) {
                return true
            }
        }

        return false
    }


}