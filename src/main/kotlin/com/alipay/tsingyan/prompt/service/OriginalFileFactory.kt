package com.alipay.tsingyan.prompt.service

import com.alibaba.fastjson.JSONArray
import com.alipay.tsingyan.prompt.bean.PromptResult
import com.alipay.tsingyan.prompt.bean.WindowBean
import com.alipay.tsingyan.utils.AppConstant
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.knuddels.jtokkit.Encodings
import com.knuddels.jtokkit.api.Encoding
import com.knuddels.jtokkit.api.EncodingRegistry
import com.knuddels.jtokkit.api.EncodingType
import java.util.stream.Collectors
import kotlin.math.ceil


/**
 * author jianzhi
 * date 2024-01-18 14:10:13
 */
object OriginalFileFactory {
    // 单个文件最大的滑块数量
    var singleFileMaxWindowSize = 200
    // 滑动窗口的大小
    var linesPerBlock = 20
    // 滑动窗口步长
    var step = 10
    // 最终上传的topK个滑块
    var topKCount = 20
    // 允许的单个文件的最大字符长度
    var maxFileLength = 10000
    var enc: Encoding? = null
    var calacCount = 0

    init {
        val registry: EncodingRegistry = Encodings.newDefaultEncodingRegistry()
        enc = registry.getEncoding(EncodingType.CL100K_BASE)
        singleFileMaxWindowSize = AppConstant.COMPLETION_CONFIG.singleFileMaxWindowSize
        linesPerBlock = AppConstant.COMPLETION_CONFIG.linesPerBlock
        step = AppConstant.COMPLETION_CONFIG.step
        topKCount = AppConstant.COMPLETION_CONFIG.topKCount
        maxFileLength = AppConstant.COMPLETION_CONFIG.maxFileLength
    }

    /**
     * 将文件内容分割成多个块，每个块包含指定数量的行，
     * 同时返回每个滑块之后的代码片段列表，其长度为滑块长度的一半。
     */
    fun splitIntoSlidingBlocks2(
        content: String,
        filePath: String,
        linesPerBlock: Int = OriginalFileFactory.linesPerBlock,
        step: Int = OriginalFileFactory.step,
        singleFileMaxWindowSize: Int = OriginalFileFactory.singleFileMaxWindowSize // 假设这是之前代码中的参数
    ): MutableList<WindowBean> {
        val windowlist = mutableListOf<WindowBean>()

        val lines = content.split("\n")
        var i = 0
        while (i + linesPerBlock <= lines.size) {
            val block = lines.subList(i, i + linesPerBlock).joinToString(separator = "\n")
            val encoded: MutableList<Int>? = enc?.encode(block)
            // 计算截取长度为滑块长度一半的代码片段
            val fragmentEndIndex = minOf(i + linesPerBlock + linesPerBlock / 2, lines.size)
            val codeFragment = lines.subList(i + linesPerBlock, fragmentEndIndex).joinToString(separator = "\n")
            val windowBean = WindowBean()
            windowBean.encodedCode = encoded
            windowBean.originalCode = block
            windowBean.suffixCode = codeFragment
            windowBean.filePath = filePath
            windowlist.add(windowBean)
            i += step
            if (windowlist.size >= singleFileMaxWindowSize){
                break
            }
        }
        // 处理剩余的不足linesPerBlock行的部分，如果有的话
        if (i < lines.size) {
            val block = lines.subList(i, lines.size).joinToString(separator = "\n")
            val encoded: MutableList<Int>? = enc?.encode(block)
            // 添加一个空字符串到 followingCodeFragments，因为没有更多的代码片段
            val windowBean = WindowBean()
            windowBean.encodedCode = encoded
            windowBean.originalCode = block
            windowBean.suffixCode = ""
            windowBean.filePath = filePath
            windowlist.add(windowBean)
        }

        return windowlist
    }

    fun calculateJaccard(s1: MutableList<Int>, s2: MutableList<Int>): Double {
        // 转换为集合以去除重复元素
        val set1 = s1.toSet()
        val set2 = s2.toSet()

        // 计算交集
        val intersection = set1.intersect(set2)

        // 计算并集
//        val union = set1.union(set2)
        val doubleUnion = set1.size + set2.size - intersection.size

        // 计算Jaccard相似度指数
        return if (doubleUnion > 0) {
            intersection.size.toDouble() / doubleUnion
        } else {
            0.0 // 如果并集为空，则相似度为0
        }
    }

    fun calculateJaccardParallel(s1: MutableList<Int>, s2: MutableList<Int>): Double {
        val set1 = s1.parallelStream().collect(Collectors.toSet())
        val set2 = s2.parallelStream().collect(Collectors.toSet())

        val intersectionSize = set1.parallelStream().filter { it in set2 }.count()
        val unionSize = set1.size + set2.size - intersectionSize

        return if (unionSize > 0) {
            intersectionSize.toDouble() / unionSize.toDouble()
        } else {
            0.0
        }
    }

    fun calculateTopKResult(project: Project, encodedCode:MutableList<Int>?): JSONArray? {
        if (encodedCode.isNullOrEmpty()) {
            return null
        }
        val service = project.service<PromptCacheService>()
        val codeCache = service.getCodeCache()

        // 存储Jaccard相似度及对应的FileBean
        val jaccardResults = mutableListOf<Pair<Double, WindowBean>>()

        calacCount = 0
        // 对codeCache中的每个FileBean计算Jaccard相似度
        codeCache.forEach { (_, fileBean) ->
            fileBean.windowList.forEach { windowBean ->
                windowBean.encodedCode?.let {
                    val jaccardIndex = calculateJaccard(encodedCode, it) // 假设calculateJaccard已经被正确实现
                    jaccardResults.add(jaccardIndex to windowBean)
                    calacCount++
                }
            }
        }

        // 根据Jaccard相似度从高到低排序
        jaccardResults.sortByDescending { it.first }
        // 选取相似度最高的top 20
        val topK = jaccardResults.take(topKCount)
        // 将结果转换成JSONArray
        val result:JSONArray = JSONArray()
        for ((similarity, windowBean) in topK) {
            // 按照\n分割代码为行
            val lines = windowBean.originalCode.split("\n")
            // 如果suffix不为空，就选择lines的一半
            var halfLines = lines
            if (windowBean.suffixCode.isNotEmpty()) {
                halfLines = lines.subList(ceil(lines.size / 2.0).toInt(), lines.size)
            }
            // 把后半部分的行重新组合成一个字符串
            val halfOriginalCode = halfLines.joinToString(separator = "\n")
            // 添加到结果列表中
            result.add(PromptResult(similarity, windowBean.filePath, halfOriginalCode + "\n" + windowBean.suffixCode))
        }
        return result
    }

}