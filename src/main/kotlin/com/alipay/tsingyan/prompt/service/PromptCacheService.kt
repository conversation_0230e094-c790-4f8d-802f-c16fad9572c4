package com.alipay.tsingyan.prompt.service

import com.alipay.tsingyan.prompt.bean.FileBean
import com.alipay.tsingyan.prompt.bean.WindowBean
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.LRUCache
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.fileTypes.FileTypes
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.*
import java.util.concurrent.Executors

/**
 *
 * author jianzhi
 * data 2024-01-17 17:01:05
 *
 */
class PromptCacheService(project: Project) : Disposable {
    private val myProject: Project = project

    val dispatcher = Executors.newFixedThreadPool(2).asCoroutineDispatcher()

    val cachedCodeFile = LRUCache<String, FileBean>(20)

    override fun dispose() {
        dispatcher.close()
    }

    fun jobRun(runnable: java.lang.Runnable){
        dispatcher.dispatch(dispatcher, runnable)
    }

    private fun createFileBean(filePath: String, content: String, psiFileName: String, psiFileTypeName: String, psiFileTextLength: Int, psiModificationStamp: Long,): FileBean?{
        val startTime1 = System.currentTimeMillis()
        if (content.isEmpty()){
            return null
        }
        val listWindow:MutableList<WindowBean> = OriginalFileFactory.splitIntoSlidingBlocks2(content, filePath)
        val endTime1 = System.currentTimeMillis()
        LogUtil.info("createFileBean costTime ${endTime1 - startTime1}, file $psiFileName")
        return FileBean(filePath, psiFileName, listWindow, psiFileTypeName, psiFileTextLength, psiModificationStamp)
    }

    fun getCodeCache(): LRUCache<String,FileBean>{
        return cachedCodeFile
    }

    /**
     * 判断是否是有效的文件
     */
    private fun isValidFile(file: PsiFile):Boolean{
        if (!file.isValid) return false
        if (!file.isWritable) return false
        if (file.fileType.isBinary) return false
        if (file.name.endsWith(".log") ||
            file.name.endsWith(".class")) return false
        // 使用VFS访问文件类型
        val fileType: FileType = file.fileType
        if (fileType == FileTypes.PLAIN_TEXT || fileType == FileTypes.ARCHIVE || fileType == FileTypes.UNKNOWN) return false

        return true
    }

    private fun isValidVirtualFile(file: VirtualFile):Boolean{
        if (!file.isValid) return false
        if (!file.isWritable) return false
        if (file.fileType.isBinary) return false
        if (file.name.endsWith(".log") ||
            file.name.endsWith(".class")) return false
        // 使用VFS访问文件类型
        val fileType: FileType = file.fileType
        if (fileType == FileTypes.PLAIN_TEXT || fileType == FileTypes.ARCHIVE || fileType == FileTypes.UNKNOWN) return false

        return true
    }

    /**
     * 切换文件时，旧的文件就是隐藏的那个文件
     */
    fun updateCodeSnipCache(){
        if (!AppConstant.COMPLETION_CONFIG.openSlidingWindows || CommonUtils.isCloudIDE){
            return
        }
        if (!myProject.isInitialized || myProject.isDisposed){
            return
        }
        val fileEditorManager = FileEditorManager.getInstance(myProject)
        val openFiles = fileEditorManager.openFiles
        for (file in openFiles) {
            if (!isValidVirtualFile(file)){
                continue
            }
            ApplicationManager.getApplication().invokeLater {
                if (myProject.isDisposed){
                    return@invokeLater
                }
                WriteCommandAction.runWriteCommandAction(myProject) {
                    if (myProject.isDisposed){
                        return@runWriteCommandAction
                    }
                    val psiFile: PsiFile? = PsiManager.getInstance(myProject).findFile(file)
                    // 太长也过滤掉，max长度待定5000
                    if (psiFile == null || psiFile.virtualFile == null || psiFile.textLength > OriginalFileFactory.maxFileLength){
                        return@runWriteCommandAction
                    }


                    val psiFileTextLength =  psiFile.textLength
                    val psiModificationStamp = psiFile.modificationStamp
                    val content = psiFile.text
                    val psiFileName = psiFile.name
                    val psiFileTypeName = psiFile.fileType.name
                    val filePath = file.path
                    val fileName = file.name
                    jobRun(Runnable{
                        //检查是否有缓存,如果文件大小没有变那就不做任何操作
                        val startTime:Long = System.currentTimeMillis()
                        val cacheFileBean = cachedCodeFile.get(filePath)
                        if (cacheFileBean != null && cacheFileBean.fileLength == psiFileTextLength && cacheFileBean.fileTimeStamp == psiModificationStamp) {
                            return@Runnable
                        }
                        val newFileBean = createFileBean(filePath,content,psiFileName,psiFileTypeName,psiFileTextLength,psiModificationStamp)
                        if (newFileBean != null) {
                            LogUtil.info("updateCache successful $fileName ${Thread.currentThread().name} $psiFileTextLength ${System.currentTimeMillis() - startTime}")
                            cachedCodeFile.put(newFileBean.filePath, newFileBean)
                        }
                    })
                }
            }
        }
    }

    private fun getCurrentFile(project: Project?): VirtualFile? {
        if (project == null || project.isDisposed) {
            return null
        }
        return Optional.of(FileEditorManager.getInstance(project).selectedFiles)
            .filter { f: Array<VirtualFile?> -> f.size > 0 }
            .map { f2: Array<VirtualFile?> ->
                f2[0]
            }.orElse(null)
    }


}
