package com.alipay.tsingyan.codeedits

import java.awt.*
import kotlin.math.max

class StagePopupVerticalLayout : LayoutManager {
    override fun addLayoutComponent(name: String, comp: Component) {
    }

    override fun removeLayoutComponent(comp: Component) {
    }

    override fun preferredLayoutSize(parent: Container): Dimension {
        return this.computeLayoutSize(
            parent,
            SizeFunction { obj: Component -> obj.preferredSize })
    }

    override fun minimumLayoutSize(parent: Container): Dimension {
        return this.computeLayoutSize(
            parent,
            SizeFunction { obj: Component -> obj.minimumSize })
    }

    private fun computeLayoutSize(parent: Container, sizeFunction: SizeFunction): Dimension {
        var width = 0
        var height = 0
        for (component in parent.components) {
            val size = sizeFunction.getSize(component)
            width = max(width.toDouble(), size.width.toDouble()).toInt()
            height += size.height
        }
        return Dimension(width, height)
    }

    override fun layoutContainer(parent: Container) {
        val height2: Int
        val height1: Int
        val isBig2: Bo<PERSON><PERSON>
        assert(parent.componentCount == 2)
        val size = parent.size
        val panel1 = parent.getComponent(0)
        val panel2 = parent.getComponent(1)
        val height = size.height
        val prefSize1 = panel1.preferredSize
        val prefSize2 = panel2.preferredSize
        val prefHeight1 = prefSize1.height
        val prefHeight2 = prefSize2.height
        val isBig1 = prefHeight1 > height / 2
        isBig2 = prefHeight2 > height / 2
        val bl = isBig2
        if (isBig1 && isBig2) {
            height1 = height / 2
            height2 = height - height1
        } else if (isBig1) {
            height2 = prefHeight2
            height1 = height - height2
        } else if (isBig2) {
            height1 = prefHeight1
            height2 = height - height1
        } else {
            height1 = prefHeight1
            height2 = prefHeight2
        }
        panel1.bounds = Rectangle(0, 0, size.width, height1)
        panel2.bounds = Rectangle(0, height1, size.width, height2)
    }

    internal fun interface SizeFunction {
        fun getSize(var1: Component): Dimension
    }
}

