package com.alipay.tsingyan.codeedits.editor

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.DefaultLanguageHighlighterColors
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.ui.JBColor
import kotlinx.coroutines.Runnable
import java.awt.Color
import java.awt.Font
import java.awt.Graphics
import java.awt.Rectangle

/**
 * 导航Tab服务
 * 提供在指定行显示/隐藏"tab to here"文字的功能
 * 
 * <AUTHOR>
 */
@Service
class NavigationTabService {
    
    private var currentEditor: Editor? = null
    private var currentLineNumber: Int = -1
    private var inlayHint: Inlay<*>? = null
    
    /**
     * 在指定行显示自定义文字
     * @param lineNumber 行号（0-based）
     * @param editor 编辑器实例
     * @param text 要显示的文字
     */
    fun show(lineNumber: Int, editor: Editor, text: String) {
        // 先隐藏之前的显示
        hide()
        
        if (lineNumber < 0 || lineNumber >= editor.document.lineCount) {
            return
        }
        
        currentEditor = editor
        currentLineNumber = lineNumber
        
        // 显示行尾的文字
        showInlayHint(lineNumber, editor, text)
    }
    
    /**
     * 隐藏当前显示的文字
     */
    fun hide() {
        // 移除inlay文字
        inlayHint?.let { inlay ->
            inlay.dispose()
            inlayHint = null
        }
        
        currentEditor = null
        currentLineNumber = -1
    }
    
    /**
     * 跳转到指定行并将光标定位到行尾
     * @param editor 编辑器实例
     * @param lineNumber 行号（1-based）
     */
    fun gotoLine(editor: Editor, lineNumber: Int, runnable: Runnable) {
        if (lineNumber <= 0 || editor.isDisposed) {
            return
        }
        
        val document = editor.document
        // idea中显示的行号 比  api中获取到的行号大1. 模型返回的是显示行号，从1开始的，但是要跳转过去的需要-1
        val zeroBasedLineNumber = lineNumber - 1
        
        // 检查行号是否有效
        if (zeroBasedLineNumber >= document.lineCount) {
            return
        }
        
        ApplicationManager.getApplication().invokeLater {
            try {
                // 获取指定行的行尾偏移量
                val lineEndOffset = document.getLineEndOffset(zeroBasedLineNumber)
                
                // 将光标移动到行尾
                editor.caretModel.moveToOffset(lineEndOffset)
                
                // 滚动到光标位置，使其在屏幕中央显示
                editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
                editor.scrollingModel.runActionOnScrollingFinished({
                    runnable.run()
                })

                // 请求焦点
//                editor.contentComponent.requestFocusInWindow()
                
            } catch (e: Exception) {
                // 记录错误但不中断程序执行
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 获取当前显示的行号
     */
    fun getCurrentLineNumber(): Int = currentLineNumber
    
    /**
     * 获取当前编辑器
     */
    fun getCurrentEditor(): Editor? = currentEditor
    
    /**
     * 是否正在显示
     */
    fun isShowing(): Boolean = currentLineNumber >= 0 && inlayHint != null
    
    /**
     * 显示行尾的文字
     */
    private fun showInlayHint(lineNumber: Int, editor: Editor, text: String) {
        val document = editor.document
        val lineEndOffset = document.getLineEndOffset(lineNumber)
        
        val renderer = NavigationInlayRenderer(text, editor)
        inlayHint = editor.inlayModel.addInlineElement(lineEndOffset, renderer)
    }
    
    /**
     * 行内文字渲染器
     */
    private class NavigationInlayRenderer(
        private val text: String,
        private val editor: Editor
    ) : EditorCustomElementRenderer {
        
        private val displayText = text
        private val horizontalPadding = 8
        private val verticalPadding = 2
        private val cornerRadius = 4
        private val leftLineWidth = 2  // 左边竖线宽度
        private val leftLineGap = 4    // 竖线与文字间的间隙
        
        override fun calcWidthInPixels(inlay: Inlay<*>): Int {
            val fontMetrics = editor.contentComponent.getFontMetrics(getFont())
            return fontMetrics.stringWidth(displayText) + horizontalPadding * 2 + leftLineWidth + leftLineGap
        }
        
        override fun paint(
            inlay: Inlay<*>,
            g: Graphics,
            targetRegion: Rectangle,
            textAttributes: TextAttributes
        ) {
            val oldFont = g.font
            val oldColor = g.color
            
            try {
                // 启用抗锯齿
                val g2d = g.create() as java.awt.Graphics2D
                g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON)
                g2d.setRenderingHint(java.awt.RenderingHints.KEY_TEXT_ANTIALIASING, java.awt.RenderingHints.VALUE_TEXT_ANTIALIAS_ON)
                
                // 设置字体
                g2d.font = getFont()
                val fontMetrics = g2d.fontMetrics
                
                // 计算背景矩形（不包括左边竖线）
                val backgroundRect = Rectangle(
                    targetRegion.x + 4 + leftLineWidth + leftLineGap, // 左边距 + 竖线宽度 + 间隙
                    targetRegion.y + verticalPadding,
                    fontMetrics.stringWidth(displayText) + horizontalPadding * 2,
                    fontMetrics.height + verticalPadding * 2
                )
                
                // 绘制左边的浅蓝色竖线
                drawLeftLine(g2d, targetRegion.x + 4, targetRegion.y, fontMetrics.height + verticalPadding * 2)
                
                // 绘制背景和边框
                drawBackground(g2d, backgroundRect)
                drawBorder(g2d, backgroundRect)
                
                // 绘制文字
                g2d.color = getTextColor()
                val textX = backgroundRect.x + horizontalPadding
                val textY = backgroundRect.y + verticalPadding + fontMetrics.ascent
                g2d.drawString(displayText, textX, textY)
                
                g2d.dispose()
                
            } finally {
                g.font = oldFont
                g.color = oldColor
            }
        }
        
        private fun drawLeftLine(g2d: java.awt.Graphics2D, x: Int, y: Int, height: Int) {
            g2d.color = getLeftLineColor()
            g2d.fillRect(x, y, leftLineWidth, height)
        }
        
        private fun drawBackground(g2d: java.awt.Graphics2D, rect: Rectangle) {
            g2d.color = getBackgroundColor()
            g2d.fillRoundRect(rect.x, rect.y, rect.width, rect.height, cornerRadius, cornerRadius)
        }
        
        private fun drawBorder(g2d: java.awt.Graphics2D, rect: Rectangle) {
            g2d.color = getBorderColor()
            g2d.drawRoundRect(rect.x, rect.y, rect.width, rect.height, cornerRadius, cornerRadius)
        }
        
        private fun getFont(): Font {
            val editorFont = editor.colorsScheme.getFont(EditorFontType.PLAIN)
            // 使用较小的字体，但不是斜体
            return Font(editorFont.name, Font.PLAIN, editorFont.size - 1)
        }
        
        private fun getLeftLineColor(): Color {
            // 浅蓝色竖线
            return Color(0x52, 0xA8, 0xFF) // #52A8FF
        }
        
        private fun getBackgroundColor(): Color {
            return if (editor.colorsScheme.defaultBackground.rgb and 0xFFFFFF > 0x808080) {
                // 亮色主题 - #F6FCFF
                Color(0xF6, 0xFC, 0xFF)
            } else {
                // 暗色主题 - #262F2E
                Color(0x26, 0x2F, 0x2E)
            }
        }
        
        private fun getBorderColor(): Color {
            // 边框颜色稍微亮一些
            return if (editor.colorsScheme.defaultBackground.rgb and 0xFFFFFF > 0x808080) {
                // 亮色主题
                Color(200, 200, 200, 150)
            } else {
                // 暗色主题
                Color(100, 100, 100, 150)
            }
        }
        
        private fun getTextColor(): Color {
            return if (editor.colorsScheme.defaultBackground.rgb and 0xFFFFFF > 0x808080) {
                // 亮色主题 - 黑色
                Color.BLACK
            } else {
                // 暗色主题 - 白色
                Color.WHITE
            }
        }
    }
    
    companion object {
        /**
         * 获取NavigationTabService实例
         */
        fun getInstance(): NavigationTabService {
            return ApplicationManager.getApplication().getService(NavigationTabService::class.java)
        }
    }
} 