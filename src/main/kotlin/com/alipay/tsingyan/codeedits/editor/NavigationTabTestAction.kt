package com.alipay.tsingyan.codeedits.editor

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.ui.Messages

/**
 * 测试NavigationTabService功能的Action
 */
class NavigationTabTestAction : AnAction("测试导航Tab"), DumbAware {
    
    private val service = NavigationTabService.getInstance()
    
    override fun actionPerformed(e: AnActionEvent) {
        val editor = e.getData(CommonDataKeys.EDITOR) ?: return
        val project = e.project ?: return
        
        if (service.isShowing()) {
            // 如果正在显示，则隐藏
            service.hide()
            Messages.showInfoMessage(project, "已隐藏导航Tab", "导航Tab测试")
        } else {
            // 如果没有显示，则在当前行显示
            val currentLine = getCurrentLine(editor)
            service.show(currentLine, editor, "tab to here")
            Messages.showInfoMessage(
                project, 
                "在第${currentLine + 1}行显示导航Tab\n显示文字: \"tab to here\"", 
                "导航Tab测试"
            )
        }
    }
    
    override fun update(e: AnActionEvent) {
        val editor = e.getData(CommonDataKeys.EDITOR)
        e.presentation.isEnabled = editor != null
        
        // 更新文字显示
        if (service.isShowing()) {
            e.presentation.text = "隐藏导航Tab"
            e.presentation.description = "隐藏当前显示的导航Tab"
        } else {
            e.presentation.text = "显示导航Tab"
            e.presentation.description = "在当前行显示导航Tab"
        }
    }
    
    private fun getCurrentLine(editor: Editor): Int {
        val offset = editor.caretModel.offset
        return editor.document.getLineNumber(offset)
    }
} 