package com.alipay.tsingyan.codeedits

import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.diff.util.LineRange
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.util.TextRange
import kotlin.math.max
import kotlin.math.min

class CodeEditsAction : AnAction() {
    val codeEditsService = service<CodeEditsService>()

    override fun actionPerformed(actionEvent: AnActionEvent) {
        LogUtil.info("dyf CodeEditsAction")
        //todo 忽略本次codeedits的判断逻辑
//        if (ignoreThisCompletion(stagedRange, completionText, config, editor)) {
//            return false
//        }
        actionEvent.project ?: return
        val editor = actionEvent.dataContext.getData("editor") as? Editor ?: return
        if (editor.isDisposed) {
            return
        }



        val completionText = "        val rangeStartLine = if (caretLine > 0) caretLine - 1 else caretLine\n" +
                "        val rangeEndLine = if (caretLine + 3 < document.lineCount) caretLine + 3 else document.lineCount - 1\n" +
                "        val lineRange = LineRange(rangeStartLine, rangeEndLine)"

//        val completionText = "    @Test\n" +
//                "    public void testTest2() {\n" +
//                "        // 由于test2方法没有返回值和副作用，所以不需要mock\n" +
//                "        // 直接调用方法\n" +
//                "        AUtils.test2(); 123"
//        val completionText = "123"
        val lineRange = getCodeEditsContext(editor, 1, 3)
        val blockCount = lineRange.end - lineRange.start + 1
        val sourceText = getLinesFromDocument(editor.document, lineRange.start, lineRange.end)
        LogUtil.info("sourceText: $sourceText")
        val stageRange = StagedRange(lineRange, sourceText, completionText)
//        codeEditsService.showCodeEditsView(editor, stageRange, completionText)
    }

//    fun getCodeEditsContext(editor: Editor): LineRange {
//        // 获取光标前1行 + 光标所在行 + 光标下3行，LineRange
//        val caretModel = editor.caretModel
//        val document = editor.document
//        val caretLine = caretModel.logicalPosition.line
//
//        val rangeStartLine = if (caretLine > 0) caretLine - 1 else caretLine
//        val rangeEndLine = if (caretLine + 3 < document.lineCount) caretLine + 3 else document.lineCount
//        return LineRange(rangeStartLine, rangeEndLine)
//    }

    fun getCodeEditsContext(editor: Editor, cursorUpLineNum: Int, cursorDownLineNum: Int): LineRange {
        val caretModel = editor.caretModel
        val document = editor.document

        // 当前光标所在的行号（从0开始）
        val caretLine = caretModel.logicalPosition.line

        // 文档的总行数
        val totalLines = document.lineCount

        // 计算起始行：光标向上 `cursorUpLineNum` 行，确保不越界（不小于 0）
        val startLine = maxOf(caretLine - cursorUpLineNum, 0)

        // 计算结束行：光标向下 `cursorDownLineNum` 行，确保不越界（不大于总行数 - 1）
        val endLine = minOf(caretLine + cursorDownLineNum, totalLines - 1)

        // 返回 LineRange，从起始行到结束行
        return LineRange(startLine, endLine)
    }


    fun getLinesFromDocument(document: Document, fromLine: Int, toLine: Int): String {
        var fromLine = fromLine
        var toLine = toLine

        // 确保起始行号不小于0
        if ((max(0.0, fromLine.toDouble()).also { fromLine = it.toInt() }) >= document.lineCount) {
            return ""
        }

        // 确保结束行号不超过文档的最大行号
        toLine = min((document.lineCount - 1).toDouble(), toLine.toDouble()).toInt()
        toLine = max(0.0, toLine.toDouble()).toInt()

        // 获取指定行范围的文本内容
        return document.getText(TextRange(document.getLineStartOffset(fromLine), document.getLineEndOffset(toLine)))
    }
}