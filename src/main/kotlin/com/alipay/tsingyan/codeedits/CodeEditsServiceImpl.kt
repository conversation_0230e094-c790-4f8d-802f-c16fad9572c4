package com.alipay.tsingyan.codeedits

import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.completion.CompletionService
import com.alipay.tsingyan.completion.CompletionType
import com.alipay.tsingyan.model.completion.CompletionRequestBean
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.ApplyInlayType
import com.alipay.tsingyan.services.completion.edit.model.BasicEditorRequest
import com.alipay.tsingyan.services.completion.edit.model.EditorRequest
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.*
import com.alipay.tsingyan.utils.CommonUtils.settingData
import com.alipay.tsingyan.view.dialog.LineStatusMarkerPopupPanel
import com.alipay.tsingyan.view.status.CodeFuseStatus
import com.alipay.tsingyan.view.status.CodeFuseStatusService
import com.intellij.diff.comparison.ByWord
import com.intellij.diff.comparison.ComparisonPolicy
import com.intellij.diff.fragments.DiffFragment
import com.intellij.diff.util.LineRange
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.markup.*
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.util.BackgroundTaskUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.SystemInfoRt
import com.intellij.openapi.util.TextRange
import com.intellij.psi.PsiDocumentManager
import com.intellij.ui.EditorTextField
import com.intellij.ui.border.CustomLineBorder
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.apache.http.util.TextUtils
import java.awt.Color
import java.awt.Component
import java.awt.Font
import java.util.*
import java.util.function.Consumer
import java.util.stream.Collectors
import javax.swing.*
import javax.swing.border.Border
import kotlin.math.max
import kotlin.math.min

class CodeEditsServiceImpl : CodeEditsService {
    private val LOGGER: Logger = Logger.getInstance(CodeEditsServiceImpl::class.java)
    private val TAG = "codeEdits"

    val completionService: CompletionService = service<CompletionService>()
    val localUserStore: LocalUserStore = service<LocalUserStore>()
    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    private val REQUEST_BEFORE = 1
    private val REQUEST_AFTER = 2

    override fun requestCodeEdits(editor: Editor, startOffset: Int, request: EditorRequest, delayTime: Int, onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?): Boolean {
        LogUtil.info("${TAG} requestCodeEdits: ${request.requestId} startOffset: $startOffset editor: $editor")
        //1、前置检查，检查是否需要取消
        if (checkIsNeedCancel(request,editor, REQUEST_BEFORE)){
            return false
        }

        //2、处理上文
        request.runtimeData.beginRequestTime = System.currentTimeMillis()
        var prompt: String = ""
        var suffix: String = ""
        var requestOffset = request.offset
        val contextText = editor.document.text
        if (contextText.isNotEmpty()) {
            if (requestOffset > contextText.length) {
                requestOffset = contextText.length
            }
            prompt = contextText.substring(0, requestOffset)
            suffix = contextText.substring(requestOffset)
        }
        val lineArr = prompt.split(AppConstant.ENTER_SYMBOL)
        val linePrefix = if (lineArr.isNotEmpty()) lineArr.last() else ""
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lineArr.size > AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize) {
            prompt =
                lineArr.subList(
                    lineArr.size - AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize,
                    lineArr.size
                ).joinToString(AppConstant.ENTER_SYMBOL)
        }

        val lastlineArr = suffix.split(AppConstant.ENTER_SYMBOL)
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lastlineArr.size > AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize) {
            suffix = lastlineArr.subList(
                lastlineArr.size - AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize,
                lastlineArr.size
            ).joinToString(AppConstant.ENTER_SYMBOL)
        }
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.CompletionInProgress)

        //3、拼参数，发起请求
        val completionRequestBean = CompletionRequestBean()
        completionRequestBean.language = request.languageStr
        completionRequestBean.prompt = prompt
        completionRequestBean.suffix = suffix
        completionRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        completionRequestBean.ideVersion = CommonUtils.getIdeVersion()
        completionRequestBean.userToken = CommonUtils.getUserToken(localUserStore, tsingYanProdService)
        completionRequestBean.fileUrl = FileDocumentManager.getInstance().getFile(editor.document)?.path
        completionRequestBean.recordInfo["debounceTime"] = delayTime?.toString() ?: "0"
        completionRequestBean.recordInfo["preCommitId"] = ProjectCache.getCommitId(editor.project!!)
        completionRequestBean.recordInfo["branch"] = CommonUtils.getBranch(editor.project!!)
        completionRequestBean.completionType = CompletionType.CODEEDIT.name
        completionRequestBean.codeEdit = true
        editor.project?.let { completionRequestBean.repo = ProjectCache.getGitData(it) }
        completionRequestBean.projectUrl = request.project.basePath ?: ""
        completionRequestBean.openFixSafety = settingData.state.codegenSecureSwitch
        completionRequestBean.sourceCodePrefix = request.sourceCodePrefix
        completionRequestBean.sourceCodeSuffix = request.sourceCodeSuffix
//        LOGGER.info("${TAG} codeEdits completions completionRequestBean ${JSON.toJSONString(completionRequestBean)}")
        request.runtimeData.beginAlgTime = System.currentTimeMillis()
        val completionResultModel =completionService.completionCodeEdits(completionRequestBean)
//        LogUtil.info(TAG,
//            "request id : ${request.requestId}, request network 前缀 :${request.lineInfo.linePrefix} , 补全结果: ${
//                JSONObject.toJSONString(
//                    completionResultModel
//                )
//            }", false
//        )
        val codeModelResult = completionResultModel?.codeModelList
        val sessionId = completionResultModel?.sessionId
        if (completionResultModel == null || codeModelResult == null) {
            CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:0")
            return false
        }
        if (completionResultModel.completionType != "CODEEDIT"){
            CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:0")
            LOGGER.info("${TAG} cancel completionType is not CODEEDIT")
            return false
        }
        //5、codeModelResult是空的，展示nextTab
        if (codeModelResult.isEmpty()){
            LogUtil.info("${TAG} completionResultModel codeModelResult is empty show nextTab")
            val tripleResult = Triple(request, null, "showNextTab")
            onFirstCompletion?.accept(tripleResult)
            return false
        }
        request.runtimeData.sessionId = sessionId
        request.runtimeData.endAlgTime = System.currentTimeMillis()
        request.runtimeData.completionNum = codeModelResult.size
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:" + codeModelResult.size)
        //4、需要再检查一遍，根据结果显示View
        if (checkIsNeedCancel(request,editor, REQUEST_AFTER)){
            return false
        }
        //5、展示View
        if (codeModelResult.isNotEmpty() && !TextUtils.isEmpty(codeModelResult[0].content)){
            if (codeModelResult[0].id != null && codeModelResult[0].id != 0L){
                request.runtimeData.acceptCodeId = codeModelResult[0].id!!
            }
            ApplicationManager.getApplication().invokeLater {
                val currentLineNum = editor.document.getLineNumber(editor.caretModel.offset)
                if (request.lineInfo.lineNumber != currentLineNum) {
                    LOGGER.info("${TAG} cancel 不是有效补全触发条件，行数发生变化, request lineNumber ${request.lineInfo.lineNumber} currentLineNum:$currentLineNum")
                    return@invokeLater
                }
                request.runtimeData.beginRenderingTime = System.currentTimeMillis()
                codeModelResult[0].content = covertWindowsLineSplitToLinuxLineSplit(codeModelResult[0].content!!)
                val caretLine = editor.caretModel.logicalPosition.line
                val lineRange = CommonUtils.getCodeEditsContext(caretLine, editor.document.lineCount,AppConstant.COMPLETION_CONFIG.editsCursorBefore, AppConstant.COMPLETION_CONFIG.editsCursorAfter)
                val sourceText = CommonUtils.getLinesFromDocument(editor.document, lineRange.start, lineRange.end)
                val stageRange = StagedRange(lineRange, sourceText, codeModelResult[0].content!!)
                val tripleResult = Triple(request, stageRange, codeModelResult[0].content!!)
                onFirstCompletion?.accept(tripleResult)
            }
        } else {
            LogUtil.info("${TAG} completionResultModel is null or codeModelResult is empty")
        }
        return true
    }


    override fun requestPreCodeEdits(project: Project, fileUrl: String, contextText : String, startOffset: Int, request: EditorRequest, delayTime: Int, onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?): Boolean {
        LogUtil.info("${TAG} requestCodeEdits: ${request.requestId} startOffset: $startOffset ")
        //2、处理上文
        request.runtimeData.beginRequestTime = System.currentTimeMillis()
        var prompt: String = ""
        var suffix: String = ""
        var requestOffset = request.offset
        if (contextText.isNotEmpty()) {
            if (requestOffset > contextText.length) {
                requestOffset = contextText.length
            }
            prompt = contextText.substring(0, requestOffset)
            suffix = contextText.substring(requestOffset)
        }
        val lineArr = prompt.split(AppConstant.ENTER_SYMBOL)
        val linePrefix = if (lineArr.isNotEmpty()) lineArr.last() else ""
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lineArr.size > AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize) {
            prompt =
                lineArr.subList(
                    lineArr.size - AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize,
                    lineArr.size
                ).joinToString(AppConstant.ENTER_SYMBOL)
        }

        val lastlineArr = suffix.split(AppConstant.ENTER_SYMBOL)
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lastlineArr.size > AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize) {
            suffix = lastlineArr.subList(
                lastlineArr.size - AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize,
                lastlineArr.size
            ).joinToString(AppConstant.ENTER_SYMBOL)
        }
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.CompletionInProgress)

        //3、拼参数，发起请求
        val completionRequestBean = CompletionRequestBean()
        completionRequestBean.language = request.languageStr
        completionRequestBean.prompt = prompt
        completionRequestBean.suffix = suffix
        completionRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        completionRequestBean.ideVersion = CommonUtils.getIdeVersion()
        completionRequestBean.userToken = CommonUtils.getUserToken(localUserStore, tsingYanProdService)
        completionRequestBean.fileUrl = fileUrl
        completionRequestBean.recordInfo["debounceTime"] = delayTime?.toString() ?: "0"
        completionRequestBean.recordInfo["preCommitId"] = ProjectCache.getCommitId(project)
        completionRequestBean.recordInfo["branch"] = CommonUtils.getBranch(project)
        completionRequestBean.completionType = CompletionType.CODEEDIT.name
        completionRequestBean.codeEdit = true
        completionRequestBean.repo = ProjectCache.getGitData(project)
        completionRequestBean.projectUrl = request.project.basePath ?: ""
        completionRequestBean.openFixSafety = settingData.state.codegenSecureSwitch
        completionRequestBean.sourceCodePrefix = request.sourceCodePrefix
        completionRequestBean.sourceCodeSuffix = request.sourceCodeSuffix
//        LOGGER.info("${TAG} codeEdits completions completionRequestBean ${JSON.toJSONString(completionRequestBean)}")
        request.runtimeData.beginAlgTime = System.currentTimeMillis()
        val completionResultModel =completionService.completionCodeEdits(completionRequestBean)
//        LogUtil.info(TAG,
//            "request id : ${request.requestId}, request network 前缀 :${request.lineInfo.linePrefix} , 补全结果: ${
//                JSONObject.toJSONString(
//                    completionResultModel
//                )
//            }", false
//        )
        val codeModelResult = completionResultModel?.codeModelList
        val sessionId = completionResultModel?.sessionId
        if (completionResultModel == null || codeModelResult == null) {
            CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:0")
            return false
        }
        request.runtimeData.sessionId = sessionId
        request.runtimeData.endAlgTime = System.currentTimeMillis()
        request.runtimeData.completionNum = codeModelResult.size
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:" + codeModelResult.size)
        //codeModelResult不为空的，展示codeEdits
        if (codeModelResult.isNotEmpty() && !TextUtils.isEmpty(codeModelResult[0].content)){
            if (codeModelResult[0].id != null && codeModelResult[0].id != 0L){
                request.runtimeData.acceptCodeId = codeModelResult[0].id!!
            }
            ApplicationManager.getApplication().invokeLater {
                request.runtimeData.beginRenderingTime = System.currentTimeMillis()
                codeModelResult[0].content = covertWindowsLineSplitToLinuxLineSplit(codeModelResult[0].content!!)
                val tripleResult = Triple(request, null, codeModelResult[0].content!!)
                onFirstCompletion?.accept(tripleResult)
            }
        } else {
            LogUtil.info("${TAG} completionResultModel is null or codeModelResult is empty")
        }
        return true
    }

    override fun requestNextTab(
        editor: Editor,
        startOffset: Int,
        request: EditorRequest,
        delayTime: Int,
        onFirstCompletion: Consumer<Triple<EditorRequest, String?, Int>>?
    ): Boolean {
        LogUtil.info("${TAG} requestNextTab: ${request.requestId} startOffset: $startOffset editor: $editor")
        //1、前置检查，检查是否需要取消
        if (checkIsNeedCancel(request,editor, REQUEST_BEFORE)){
            return false
        }

        //2、处理上文
        request.runtimeData.beginRequestTime = System.currentTimeMillis()
        var prompt: String = ""
        var suffix: String = ""
        var requestOffset = request.offset
        val contextText = editor.document.text
        if (contextText.isNotEmpty()) {
            if (requestOffset > contextText.length) {
                requestOffset = contextText.length
            }
            prompt = contextText.substring(0, requestOffset)
            suffix = contextText.substring(requestOffset)
        }
        val lineArr = prompt.split(AppConstant.ENTER_SYMBOL)
        val linePrefix = if (lineArr.isNotEmpty()) lineArr.last() else ""
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lineArr.size > AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize) {
            prompt =
                lineArr.subList(
                    lineArr.size - AppConstant.COMPLETION_CONFIG.completionPromptMaxLineSize,
                    lineArr.size
                ).joinToString(AppConstant.ENTER_SYMBOL)
        }

        val lastlineArr = suffix.split(AppConstant.ENTER_SYMBOL)
        //如果当前代码特别长，截取前{@code AppConstant.PROMPT_LINE_MAX_NUM}行
        if (lastlineArr.size > AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize) {
            suffix = lastlineArr.subList(
                lastlineArr.size - AppConstant.COMPLETION_CONFIG.completionSuffixMaxLineSize,
                lastlineArr.size
            ).joinToString(AppConstant.ENTER_SYMBOL)
        }
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.CompletionInProgress)

        //3、拼参数，发起请求
        val completionRequestBean = CompletionRequestBean()
        completionRequestBean.language = request.languageStr
        completionRequestBean.prompt = prompt
        completionRequestBean.suffix = suffix
        completionRequestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        completionRequestBean.ideVersion = CommonUtils.getIdeVersion()
        completionRequestBean.userToken = CommonUtils.getUserToken(localUserStore, tsingYanProdService)
        completionRequestBean.fileUrl = FileDocumentManager.getInstance().getFile(editor.document)?.path
        completionRequestBean.recordInfo["debounceTime"] = delayTime?.toString() ?: "0"
        completionRequestBean.recordInfo["preCommitId"] = ProjectCache.getCommitId(editor.project!!)
        completionRequestBean.recordInfo["branch"] = CommonUtils.getBranch(editor.project!!)
        completionRequestBean.completionType = CompletionType.NEXT_TAB.name
        completionRequestBean.codeEdit = true
        editor.project?.let { completionRequestBean.repo = ProjectCache.getGitData(it) }
        completionRequestBean.projectUrl = request.project.basePath ?: ""
        completionRequestBean.openFixSafety = settingData.state.codegenSecureSwitch
        
        // nextTab专用参数的特殊处理
        completionRequestBean.sourceCodePrefix = CommonUtils.getSourceCodePrefix(editor, request.offset)
        completionRequestBean.sourceCodeSuffix = CommonUtils.getSourceCodeSuffix(editor, request.offset)
        completionRequestBean.originalCode = CommonUtils.getOriginalCode(editor, request.offset)
//        LOGGER.info("${TAG} nextTab completions completionRequestBean ${JSON.toJSONString(completionRequestBean)}")
        request.runtimeData.beginAlgTime = System.currentTimeMillis()
        val completionResultModel = completionService.completionCodeEdits(completionRequestBean)
        LogUtil.info(TAG,
            "request id : ${request.requestId}, request network 前缀 :${request.lineInfo.linePrefix} , requestNextTab结果: ${
                JSONObject.toJSONString(
                    completionResultModel
                )
            }", false
        )
        val sessionId = completionResultModel?.sessionId
        request.runtimeData.sessionId = sessionId
        request.runtimeData.endAlgTime = System.currentTimeMillis()
        request.runtimeData.completionNum = 1
        CodeFuseStatusService.notifyApplication(CodeFuseStatus.Ready, "result:" + 1)
        //4、需要再检查一遍，根据结果显示View
        if (checkIsNeedCancel(request,editor, REQUEST_AFTER)){
            return false
        }
        completionResultModel?.lineNumber?.let {
                val tripleResult = Triple(request, "", completionResultModel.lineNumber)
                onFirstCompletion?.accept(tripleResult)
        }
        return true
    }

    override fun isCodeEditsViewShowing(): Boolean {
        return LineStatusMarkerPopupPanel.popUpViewIsShowing()
    }

    override fun applyCodeEditsCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean {
        TODO("Not yet implemented")
    }

    private fun checkIsNeedCancel(request: EditorRequest, editor: Editor, type: Int): Boolean{
        try {
            if (request.requestTimestamp != AppConstant.LAST_REQUEST_TIME.get()) {
                LOGGER.info("${TAG} cancel ${type} 有发起新请求,补全请求被过滤:$${request.lineInfo.linePrefix}")
                return true
            }

            if (request !is BasicEditorRequest || null == editor.project) {
                LOGGER.info("${TAG} cancel ${type} request != BasicEditorRequest 或者 没找到project, 补全请求被过滤:$${request.lineInfo.linePrefix}")
                return true
            }

            if (request.isCancelled) {
                LOGGER.info("${TAG} cancel ${type} 补全请求被cancel:$${request.lineInfo.linePrefix}")
                return true
            }

            //当光标变化后,如果有内容改变就过滤本次请求
            if (editor.document.lineCount <= request.lineInfo.lineNumber) {
                LOGGER.info("${TAG} cancel ${type} 补全请求被cancel, 请求的lineNumber大于当前文本行数")
                return true
            }

            val realLineEndOffset = editor.document.getLineEndOffset(request.lineInfo.lineNumber)
            val lineEndOffset = request.lineInfo.lineEndOffset
            if (lineEndOffset != realLineEndOffset) {
                if (realLineEndOffset <= editor.document.text.length) {
                    val substring = if (lineEndOffset <= realLineEndOffset) {
                        editor.document.text.substring(lineEndOffset, realLineEndOffset)
                    } else {
                        if (lineEndOffset > editor.document.text.length) {
                            LOGGER.info("${TAG} cancel ${type} 不是有效补全触发条件, 补全请求被过滤: lineEndOffset>length请求时位置比现在所有长度还小")
                            return true
                        }
                        editor.document.text.substring(realLineEndOffset, lineEndOffset)
                    }
                    if (substring.trim().isNotBlank()) {
                        LOGGER.info("${TAG} cancel  ${type} 不是有效补全触发条件, 补全请求被过滤: lineEndOffset != realLineEndOffset $lineEndOffset $realLineEndOffset substring:$substring")
                        return true
                    }
                }
            }
        } catch (e: Throwable){
            LogUtil.info("${TAG} cancel ${type} 异常", e)
            return true
        }
        return false
    }

    override fun showCodeEditsView(editor: Editor, stagedRange: StagedRange, completionText: String, request: EditorRequest, runnable: Runnable):Boolean {
        return showHintAt(editor, stagedRange, completionText, request, runnable)
    }

    private fun showHintAt(
        editor: Editor,
        stagedRange: StagedRange,
        completionText: String,
        request: EditorRequest,
        runnable: Runnable
    ): Boolean {
        if (editor.isDisposed) {
            return false
        }

        if (request.isCancelled){
            LogUtil.info("${TAG} showCodeEditsView request id ${request.requestId}: ${request.isCancelled}")
            return false
        }
        val linesFromDocument: String = stagedRange.sourceCodeText
        if (linesFromDocument == completionText || isSameLines(linesFromDocument, completionText)) {
            LogUtil.info("${TAG} showCodeEditsView request id ${request.requestId} isSameLines")
            return false
        }

        //1、获取差异
        val vcsWordDiff: List<DiffFragment> = BackgroundTaskUtil.tryComputeFast<List<DiffFragment>>(
            { i: ProgressIndicator? ->
                ByWord.compare(
                    completionText as CharSequence,
                    linesFromDocument as CharSequence,
                    ComparisonPolicy.IGNORE_WHITESPACES,
                    i!!
                )
            }, 200L
        ) as List<DiffFragment>
//        LogUtil.info("${TAG} vcsWordDiff: $vcsWordDiff")
        if (vcsWordDiff.isEmpty()){
            LogUtil.info("${TAG} showCodeEditsView showHintAt vcsWordDiff empty")
            return false
        }

        //3、show CodeEditsView
//        if (TextUtils.isEmpty(completionText)){
//            //删除逻辑
//            this.showPopupPanel(editor, completionText, stagedRange, CheckedDisposableImpl(), vcsWordDiff, true)
//            return true
//        }
        var isShowSuccessFull = false
        try {
            isShowSuccessFull = this.showPopupPanel(editor, completionText, stagedRange, CheckedDisposableImpl(), vcsWordDiff, false, true, runnable)
        } catch (e: Throwable){
            LogUtil.info("${TAG} showCodeEditsView showHintAt error", e)
        }
        return isShowSuccessFull
    }

    private fun showPopupPanel(
        editor: Editor,
        completionText: String,
        stagedRange: StagedRange,
        disposable: CheckedDisposableImpl,
        vcsWordDiff: List<DiffFragment>,
        isDelete: Boolean,
        isAddition: Boolean = false,
        runnable: Runnable
    ): Boolean {
        var codeEditsContent = completionText
        if (isDelete){
            codeEditsContent = stagedRange.sourceCodeText
        }
        val codeEditsDocument = EditorFactory.getInstance().createDocument((codeEditsContent as CharSequence))
        val codeEditsTextField: EditorTextField = this.createTextField(editor, codeEditsDocument)
        val diffFragments: List<DiffFragment> = this.installWordDiff(editor, stagedRange, vcsWordDiff)
//        LogUtil.info("${TAG} diffFragments: ${JSON.toJSONString(diffFragments)}")

        Disposer.register(disposable) { ApplicationManager.getApplication().invokeAndWait { editor.markupModel.removeAllHighlighters() } }
        val editorsPanel: JComponent = this.createEditorComponent(editor, codeEditsTextField)
        val lineStatusMarkerPopupPanel: LineStatusMarkerPopupPanel = LineStatusMarkerPopupPanel.create(editor, editorsPanel)

        val triple = getAdditionDiffShowPos(editor, diffFragments, stagedRange)
        val isShowSuccess =  LineStatusMarkerPopupPanel.showPopupAt(
            editor,
            lineStatusMarkerPopupPanel,
            triple.first,
            triple.second,
            triple.third,
            disposable,
            runnable
        )

        SwingUtilities.invokeLater {
            if (isShowSuccess && !disposable.isDisposed) {
                // popupPanel中增加背景色
                if (isDelete) {
                    applyStrikeThroughEffect(codeEditsTextField, disposable)
                } else {
                    addBackgroundHighlight(codeEditsTextField, diffFragments, disposable)
                }

                // editor编辑器中增加红色背景
                this.installMasterEditorWordHighlighters(editor, stagedRange, diffFragments, disposable)
            }
        }
        if (disposable.isDisposed) {
            ApplicationManager.getApplication().invokeAndWait { editor.markupModel.removeAllHighlighters() }
        }

        return isShowSuccess
    }

    private fun getDiffShowPos(editor: Editor, fragments: List<DiffFragment>): Pair<LogicalPosition, Int> {
        val document = editor.document
        val minOffset =
            fragments.stream().map { obj: DiffFragment -> obj.startOffset2 }.min { obj: Int, anotherInteger: Int? ->
                obj.compareTo(
                    anotherInteger!!
                )
            }.orElse(0)
        val maxOffset =
            fragments.stream().map { obj: DiffFragment -> obj.endOffset2 }.max { obj: Int, anotherInteger: Int? ->
                obj.compareTo(
                    anotherInteger!!
                )
            }.orElse(0)
        val diffStartLine = document.getLineNumber(minOffset)
        val diffEndLine = document.getLineNumber(maxOffset)
        LogUtil.info("${TAG} diffStartLine: $diffStartLine, diffEndLine: $diffEndLine")
        var maxColumn = 0
        var maxLine = diffStartLine
        for (i in diffStartLine..diffEndLine) {
            val line: TextRange = getLineStartAndEndOffset(document, i, i)
            if (line.length <= maxColumn) continue
            maxColumn = line.length
            maxLine = i
        }
        var showDiffLine = diffStartLine
        if (showDiffLine >= document.lineCount) {
            showDiffLine = document.lineCount - 1
        }
        if (showDiffLine < 0) {
            showDiffLine = 0
        }
        LogUtil.info("${TAG} showDiffLine: $showDiffLine, maxColumn: $maxColumn")

        //idea中线最长是110，所以将fragments的diff中最大的maxColumn +  completionText中最大行的字符数 > 110
        return Pair(LogicalPosition(showDiffLine, maxColumn), maxLine)
    }

    private fun getAdditionDiffShowPos(editor: Editor, fragments: List<DiffFragment>, stagedRange:StagedRange): Triple<LogicalPosition, Int, Boolean>{
        val document = editor.document
        // 我想知道sourceDocumentStartLine，sourceDocumentEndLine行数中间最长的maxColumn
        var maxColumn = 0
        var maxLine = stagedRange.sourceDocumentStartLine
        for (i in stagedRange.sourceDocumentStartLine..stagedRange.sourceDocumentEndLine) {
            val line: TextRange = getTextRangeForLine(document, i) ?: continue
            //val code = document.getText(line)
            //LogUtil.info("line: $code")
            if (line.length <= maxColumn) continue
            maxColumn = line.length
            maxLine = i
        }
        if (maxColumn < 100){
            return Triple(LogicalPosition(stagedRange.sourceDocumentStartLine, maxColumn), maxLine, false)
        }
        // 如果最大列数 >= 100 行时的逻辑处理,默认显示到 sourceDocumentEndLine 的下面一行
        var targetStartLine = stagedRange.sourceDocumentEndLine
        // 检查文件末尾的长度限制
        if (targetStartLine >= document.lineCount) {
            // 如果 targetLine 超过文件总行数，尝试调整显示到 sourceDocumentStartLine 的上面。
            targetStartLine = stagedRange.sourceDocumentStartLine - (stagedRange.completionEndLine - stagedRange.completionStartLine)
            if (targetStartLine < 0) {
                // 如果调整后仍在文件范围外，则将显示位置设置为文件起始位置
                targetStartLine = 0
            }
        }
        // 检查文件开头的长度限制
        if (targetStartLine < 0) {
            // 确保显示位置不会超出文件开头
            targetStartLine = 0
        }
        // 返回 LogicalPosition 和计算的显示行数
        return Triple(LogicalPosition(targetStartLine, 0), targetStartLine, true)
    }

    private fun getTextRangeForLine(document: Document, lineNum: Int): TextRange? {
        // 检查是否在文档的有效行号范围内
        if (lineNum < 0 || lineNum >= document.lineCount) {
            return null // 行号越界，返回 null
        }

        // 获取该行的起始偏移量和结束偏移量
        val startOffset = document.getLineStartOffset(lineNum)
        val endOffset = document.getLineEndOffset(lineNum)

        // 返回对应的 TextRange
        return TextRange(startOffset, endOffset)
    }

    private fun createEditorComponent(editor: Editor, vcsTextField: EditorTextField): JComponent {
        val vcsEditorPane: JComponent = this.createEditorPane(editor, vcsTextField)
        val editorsPanel = JPanel(StagePopupVerticalLayout())
        val groupLayout = GroupLayout(editorsPanel)
        editorsPanel.layout = groupLayout
        groupLayout.setHorizontalGroup(groupLayout.createParallelGroup(GroupLayout.Alignment.LEADING).addComponent(vcsEditorPane))
        groupLayout.setVerticalGroup(groupLayout.createSequentialGroup().addComponent(vcsEditorPane).addPreferredGap(
                LayoutStyle.ComponentPlacement.RELATED
            )
        )
        editorsPanel.background = com.intellij.openapi.vcs.ex.LineStatusMarkerPopupPanel.getEditorBackgroundColor((editor as Editor?)!!)
        return editorsPanel
    }

    private fun createEditorPane(editor: Editor, textField: EditorTextField): JComponent {
        val label = JBLabel("")
        label.border = JBUI.Borders.emptyBottom(2)
        label.font = UIUtil.getLabelFont(UIUtil.FontSize.SMALL)
        label.foreground = UIUtil.getLabelDisabledForeground()
        val borderColor = LineStatusMarkerPopupPanel.getBorderColor()
        val outerLineBorder = CustomLineBorder(borderColor, 0, 1, 1, 1)
        val emptyBorder = JBUI.Borders.empty(2)
        val compoundBorder = BorderFactory.createCompoundBorder(outerLineBorder as Border, emptyBorder)
        val panel = JBUI.Panels.simplePanel(textField as Component)
        panel.addToTop(label as Component)
        panel.withBackground(com.intellij.openapi.vcs.ex.LineStatusMarkerPopupPanel.getEditorBackgroundColor(editor))
        panel.withBorder(compoundBorder as Border)
        return panel
    }

    private fun installWordDiff(
        editor: Editor,
        range: StagedRange,
        vcsWordDiff: List<DiffFragment?>,
    ): List<DiffFragment> {
        val lineStartOffset = editor.document.getLineStartOffset(range.sourceDocumentStartLine)
        val fixedDiffFragment = vcsWordDiff.stream().map { codeDiffFragment: DiffFragment? ->
            object : DiffFragment {
                var diffFragment: DiffFragment? = null
                var deletedChars: Int = 0
                var lineStartOffSet: Int = 0

                init {
                    this.diffFragment = codeDiffFragment
                    this.deletedChars = deletedChars
                    this.lineStartOffSet = lineStartOffset
                }

                override fun getStartOffset1(): Int {
                    return diffFragment!!.startOffset1 - this.deletedChars
                }

                override fun getEndOffset1(): Int {
                    return diffFragment!!.endOffset1 - this.deletedChars
                }

                override fun getStartOffset2(): Int {
                    return diffFragment!!.startOffset2 + this.lineStartOffSet
                }

                override fun getEndOffset2(): Int {
                    return diffFragment!!.endOffset2 + this.lineStartOffSet
                }
            }
        }.collect(Collectors.toList<DiffFragment>())
        return fixedDiffFragment
    }

    private fun installMasterEditorWordHighlighters(
        editor: Editor,
        stagedRange: StagedRange,
        wordDiff: List<DiffFragment>,
        parentDisposable: Disposable
    ) {
        WordDiffMerger(editor, wordDiff, stagedRange).run()
    }

    private fun isSameLines(s1: String, s2: String): Boolean {
        if (StringUtils.isAllBlank(*arrayOf<CharSequence>(s1, s2))) {
            return true
        }
        if (StringUtils.isBlank(s1 as CharSequence) || StringUtils.isBlank(s2 as CharSequence)) {
            return false
        }
        return CollectionUtils.isEqualCollection(
            removeEmptyLinesAndTrim(s1),
            removeEmptyLinesAndTrim(s2)
        )
    }

    private fun removeEmptyLinesAndTrim(lines: String): List<String> {
        if (StringUtils.isBlank(lines as CharSequence)) {
            return emptyList()
        }
        return Arrays.stream(lines.split("\n".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray())
            .filter { cs: String? -> StringUtils.isNotBlank(cs) }.map { str: String? -> StringUtils.trim(str) }.collect(
                Collectors.toList()
            )
    }

    private fun createTextField(editor: Editor, completionDocument: Document): EditorTextField {
        val textField = com.intellij.openapi.vcs.ex.LineStatusMarkerPopupPanel.createTextField(editor, completionDocument.text)
        val psiFile = PsiDocumentManager.getInstance((editor.project)!!).getPsiFile(editor.document)
        
        // 直接创建EditorTextField，避免重复注册DataProvider
//        val textField = EditorTextField(completionDocument.text, editor.project, psiFile?.fileType)

        // 设置属性使其行为类似于LineStatusMarkerPopupPanel中的文本字段
//        textField.isViewer = true
//        textField.setCaretPosition(0)
//        textField.setOneLineMode(false)

        // 手动安装语法高亮
        com.intellij.openapi.vcs.ex.LineStatusMarkerPopupPanel.installBaseEditorSyntaxHighlighters(
            editor.project,
            textField,
            completionDocument, TextRange(0, completionDocument.textLength), psiFile!!.fileType
        )
        return textField
    }

    override fun closeCodeEditsView(editor: Editor, disposeContextEnum: InlayDisposeContextEnum) {
        LineStatusMarkerPopupPanel.closePopUpView()
    }

    private fun getLineStartAndEndOffset(document: Document, startLineNum: Int, endLineNum: Int): TextRange {
        var startLine = startLineNum
        var endLine = endLineNum
        startLine = max(0.0, startLine.toDouble()).toInt()
        endLine = min((getLineCount(document) - 1).toDouble(), endLine.toDouble()).toInt()
        endLine = max(0.0, endLine.toDouble()).toInt()
        startLine = min(startLine.toDouble(), endLine.toDouble()).toInt()
        return TextRange(document.getLineStartOffset(startLine), document.getLineEndOffset(endLine))
    }

    fun getLineCount(document: Document): Int {
        val lineCount = document.lineCount
        if (lineCount == 0) {
            return 0
        }
        try {
            val lineStartOffset = document.getLineStartOffset(lineCount)
            val lineEndOffset = document.getLineEndOffset(lineCount)
            return if (lineEndOffset == lineStartOffset) lineCount - 1 else lineCount
        } catch (e: Exception) {
            return lineCount - 1
        }
    }

    private fun applyStrikeThroughEffect(textField: EditorTextField, disposable: Disposable) {
        // 获取 EditorTextField 的内部编辑器
        val editor = textField.editor ?: return

        // 获取文档中的全文范围
        val document = editor.document
        val startOffset = 0
        val endOffset = document.textLength
        val markupModel = editor.markupModel // 获取 `MarkupModel`
        markupModel.addRangeHighlighter(
            startOffset,
            endOffset,
            HighlighterLayer.SELECTION - 1, // 高亮层级，低于选择层
            TextAttributes(
                Color.gray, // 字体颜色 (null 表示不变)
                null, // 背景颜色
                Color.gray, // 下划线颜色
                EffectType.STRIKEOUT, // 效果颜色
                Font.PLAIN // 字体样式
            ),
            HighlighterTargetArea.EXACT_RANGE // 精确范围
        )

        // 在必要时可以将 `rangeHighlighter` 添加到 Disposable 中，以便释放资源
        Disposer.register(disposable) {
            ApplicationManager.getApplication()
                .invokeAndWait { markupModel.removeAllHighlighters() }
        }
    }


    /**
     * 在 `vcsTextField` 中为 `fixedDiffFragment` 的区域添加浅绿色背景高亮
     */
    private fun addBackgroundHighlight(vcsTextField: EditorTextField, fixedDiffFragment: List<DiffFragment>, disposable: Disposable) {
//        LogUtil.info("${TAG} addBackgroundHighlight1")
        val editor = vcsTextField.editor ?: return // 获取 `Editor` 对象
//        LogUtil.info("${TAG} addBackgroundHighlight2")
        val markupModel: MarkupModel = editor.markupModel // 获取 `MarkupModel`

        // 定义高亮的背景颜色（浅绿色）
        val highlightColor = java.awt.Color(155, 185, 85, 51) // 浅绿色 (Light Green)

        // 遍历每个差异片段并添加高亮器
        for (fragment in fixedDiffFragment) {
            val startOffset = fragment.startOffset1
            val endOffset = fragment.endOffset1
//            LogUtil.info("${TAG} addBackgroundHighlight startOffset=$startOffset,endOffset=$endOffset")
            // 添加高亮器
            markupModel.addRangeHighlighter(
                startOffset,
                endOffset,
                HighlighterLayer.SELECTION - 1, // 高亮层级，低于选择层
                TextAttributes(
                    null, // 字体颜色 (null 表示不变)
                    highlightColor, // 背景颜色
                    null, // 下划线颜色
                    null, // 效果颜色
                    Font.PLAIN // 字体样式
                ),
                HighlighterTargetArea.EXACT_RANGE // 精确范围
            )
        }

        // 在必要时可以将 `rangeHighlighter` 添加到 Disposable 中，以便释放资源
        Disposer.register(disposable) {
            ApplicationManager.getApplication()
                .invokeAndWait { markupModel.removeAllHighlighters() }
        }
    }

    override fun dispose() {
    }


    private fun covertWindowsLineSplitToLinuxLineSplit(text: String): String {
        if (SystemInfoRt.isWindows) {
            return text.replace("\r\n", "\n")
        }
        return text
    }
}