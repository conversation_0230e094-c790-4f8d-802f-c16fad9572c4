package com.alipay.tsingyan.codeedits

import com.intellij.diff.fragments.DiffFragment
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.markup.RangeHighlighter
import com.intellij.openapi.editor.markup.TextAttributes
import java.awt.Color
import kotlin.math.max

class WordDiffMerger(private val editor: Editor, private val wordDiff: List<DiffFragment>, private val stagedRange: StagedRange) {
    private val highlighters: MutableList<RangeHighlighter> = ArrayList()
    private var dirtyStart = -1
    private var dirtyEnd = -1

    fun run(): List<RangeHighlighter> {
        // Step 1: 渲染 sourceDocumentStartLine 到 sourceDocumentEndLine 为浅灰色背景
        renderStagedRangeBackground()

        // Step 2: 渲染 wordDiff 为红色背景
        for (fragment in this.wordDiff) {
            this.flush(fragment.startOffset2)
            this.markDirtyRange(fragment.startOffset2, fragment.endOffset2)
        }
        this.flush(Int.MAX_VALUE)

        return this.highlighters
    }

    private fun renderStagedRangeBackground() {
        val document = editor.document

        // 获取 sourceDocumentStartLine 和 sourceDocumentEndLine 对应的文本范围
        val startOffset = document.getLineStartOffset(stagedRange.sourceDocumentStartLine)
        val endOffset = document.getLineEndOffset(stagedRange.sourceDocumentEndLine)

        // 渲染为浅灰色背景
        val markupModel = editor.markupModel
        val attributes = createAttributesForStagedRange()
        val rangeHighlighter = markupModel.addRangeHighlighter(
            startOffset,
            endOffset,
            com.intellij.openapi.editor.markup.HighlighterLayer.ADDITIONAL_SYNTAX,
            attributes,
            com.intellij.openapi.editor.markup.HighlighterTargetArea.EXACT_RANGE
        )
//        highlighters.add(rangeHighlighter)
    }

    private fun flush(offset: Int) {
        if (this.dirtyEnd != -1 && this.dirtyEnd < offset) {
            val markupModel = editor.markupModel

            val attributes = createAttributesForCurrentDirtyRange()
            val rangeHighlighter = markupModel.addRangeHighlighter(
                this.dirtyStart,
                this.dirtyEnd,
                com.intellij.openapi.editor.markup.HighlighterLayer.ADDITIONAL_SYNTAX,
                attributes,
                com.intellij.openapi.editor.markup.HighlighterTargetArea.EXACT_RANGE
            )
//            highlighters.add(rangeHighlighter)

            this.dirtyStart = -1
            this.dirtyEnd = -1
        }
    }

    private fun markDirtyRange(start: Int, end: Int) {
        if (this.dirtyEnd == -1) {
            this.dirtyStart = start
            this.dirtyEnd = end
        } else {
            this.dirtyEnd = max(dirtyEnd, end)
        }
    }

    private fun createAttributesForCurrentDirtyRange(): TextAttributes {
        val redAttributes = TextAttributes()
        redAttributes.backgroundColor = Color(255, 0, 0, 51) // 红色背景
        return redAttributes
    }

    private fun createAttributesForStagedRange(): TextAttributes {
        val grayAttributes = TextAttributes()
        grayAttributes.backgroundColor = Color(192, 192, 192, 51) // 浅灰色背景
        return grayAttributes
    }
}