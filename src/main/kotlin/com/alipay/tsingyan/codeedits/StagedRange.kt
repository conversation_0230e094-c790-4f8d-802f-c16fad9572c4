package com.alipay.tsingyan.codeedits

import com.intellij.diff.util.LineRange

class StagedRange(lineRange: LineRange, sourceText: String, completionText: String) {
    var sourceDocumentStartLine: Int = lineRange.start
    var sourceDocumentEndLine: Int
    var sourceCodeText: String = ""
    var completionStartLine: Int = 0
    var completionEndLine: Int = 0
    var completionText: String = ""

    init {
        this.updateCompletionLines(completionText)
        this.sourceDocumentEndLine = lineRange.end
        this.sourceCodeText = sourceText
    }

    fun hasCompletion(): <PERSON><PERSON>an {
        return this.completionEndLine >= this.completionStartLine
    }

    fun updateCompletionLines(completionText: String) {
        val trimmedText = completionText
            .split("\n".toRegex())
            .dropLastWhile { it.isEmpty() }
            .joinToString("\n")
        this.completionText = trimmedText
        val lineCount = trimmedText.lines().count()
        this.completionStartLine = 0
        this.completionEndLine = lineCount
    }

    override fun toString(): String {
        return "StagedRange(sourceDocumentStartLine=" + this.sourceDocumentStartLine + ", sourceDocumentEndLine=" + this.sourceDocumentEndLine + ", completionStartLine=" + this.completionStartLine + ", completionEndLine=" + this.completionEndLine + ")"
    }
}

