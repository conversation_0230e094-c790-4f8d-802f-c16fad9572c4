package com.alipay.tsingyan.codeedits

import com.alipay.tsingyan.services.completion.edit.model.ApplyInlayType
import com.alipay.tsingyan.services.completion.edit.model.EditorRequest
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import java.util.function.Consumer

interface CodeEditsService: Disposable {
    fun showCodeEditsView(editor: Editor,
                          stagedRange: StagedRange,
                          completionText: String,
                          request: EditorRequest,
                          closeRunnable: Runnable):Boolean

    fun requestCodeEdits(editor: Editor,
                         startOffset:Int,
                         contentRequest: EditorRequest,
                         delayTime: Int,
                         onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?): Boolean

    fun requestPreCodeEdits(
        project: Project,
        fileUrl: String,
        contextText: String,
        startOffset: Int,
        request: EditorRequest,
        delayTime: Int,
        onFirstCompletion: Consumer<Triple<EditorRequest, StagedRange?, String>?>?
    ): Boolean

    fun requestNextTab(editor: Editor,
                         startOffset:Int,
                         contentRequest: EditorRequest,
                         delayTime: Int,
                         onFirstCompletion: Consumer<Triple<EditorRequest, String?, Int>>?): Boolean

    fun closeCodeEditsView(editor: Editor, disposeContextEnum: InlayDisposeContextEnum)

    fun isCodeEditsViewShowing(): Boolean

    fun applyCodeEditsCompletion(editor: Editor, applyInlayType: ApplyInlayType): Boolean
}