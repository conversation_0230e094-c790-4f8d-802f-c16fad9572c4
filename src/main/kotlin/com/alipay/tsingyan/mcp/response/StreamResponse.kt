package com.alipay.tsingyan.mcp.response

import com.alibaba.fastjson2.JSONObject

sealed class StreamResponse {
    data class ContentResponse(
        val content: String,
        val finishReason: String?
    ) : StreamResponse()

    data class ToolCallResponse(
        val id: String?,
        val name: String?,
        val arguments: String?,
        val finishReason: String?
    ) : StreamResponse()

    companion object {
        fun fromJson(json: JSONObject): StreamResponse? {
            val choices = json.getJSONArray("choices")?.getJSONObject(0) ?: return null
            val delta = choices.getJSONObject("delta")
            val finishReason = choices.getString("finish_reason")

            return when {
                delta.containsKey("content") -> {
                    ContentResponse(
                        content = delta.getString("content") ?: "",
                        finishReason = finishReason
                    )
                }
                delta.containsKey("tool_calls") -> {
                    val toolCalls = delta.getJSONArray("tool_calls")?.getJSONObject(0)
                    ToolCallResponse(
                        id = toolCalls?.getString("id"),
                        name = toolCalls?.getJSONObject("function")?.getString("name"),
                        arguments = toolCalls?.getJSONObject("function")?.getString("arguments"),
                        finishReason = finishReason
                    )
                }
                finishReason.contentEquals("stop")->{
                    ContentResponse(
                        content = "",
                        finishReason = finishReason
                    )
                }
//                finishReason.contentEquals("tool_calls")->{
//                    ToolCallResponse(
//                        id = null,
//                        name = null,
//                        arguments = null,
//                        finishReason = finishReason
//                    )
//                }
                else -> null
            }
        }
    }
} 