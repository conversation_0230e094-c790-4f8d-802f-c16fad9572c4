package com.alipay.tsingyan.mcp.api

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.mcp.MCPCoreService
import com.alipay.tsingyan.mcp.conf.RemoteMcpService
import com.alipay.tsingyan.mcp.data.NewChatActionData
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.composer.SnapShotWrapper
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.utils.editor.EditorUtil
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.view.jcef.socket.handler.DataCallBack
import com.alipay.tsingyan.view.jcef.socket.handler.rpc.factory.MsgHandler
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.OpenFileDescriptor
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import java.io.File
import java.security.MessageDigest

/**
 * chat Rpc的核心处理逻辑
 * <AUTHOR>
 * 2025-04-28 16:41:15
 */
class ChatRequestHandlerApi : MsgHandler<String>() {
    /**
     * 本地存储用户信息服务
     */
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    var agentService: AgentService = service<AgentService>()
    val mcpCoreService = service<MCPCoreService>()

    override val type: String
        get() = "chat"

    override fun execute(
        project: Project,
        param: String?,
        callBack: DataCallBack
    ) {
        if (param == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Invalid request parameters")
            })
            return
        }
        val chatRequestData = JSONObject.parseObject<ChatRequestData>(param, ChatRequestData::class.java)

        when (chatRequestData.action) {
            "init" -> handleInitRequest(project, callBack)
            "newChat" -> handleNewChatRequest(project, JSONObject.toJSONString(chatRequestData.actionData), callBack)
            "localAgentServer" -> handleLocalAgentServerRequest(project, callBack)
            "addCheckPoint"-> handleAddCheckPointRequest(project, JSONObject.toJSONString(chatRequestData.actionData), callBack)
            "resetCheckPoint"-> handleResetCheckPointRequest(project, JSONObject.toJSONString(chatRequestData.actionData), callBack)
            "stopChat"-> handleStopChatRequest(project, JSONObject.toJSONString(chatRequestData.actionData), callBack)
            "openFile"-> handleOpenFileRequest(project, JSONObject.toJSONString(chatRequestData.actionData), callBack)
            else -> callBack.invoke(JSONObject().apply {
                put("error", "Unsupported action: ${chatRequestData.action}")
            })
        }
    }

    private fun handleInitRequest(project: Project, callBack: DataCallBack) {
        val response = JSONObject().apply {
            put("userInfoModel", localUserStore.getUserInfoModel())
            put("pluginVersion", CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION))
            put("ideVersion", CommonUtils.getIdeVersion())
            put("productType", CommonUtils.getProductType())
            put("localPath", project.basePath)
            put("repo", ProjectCache.getGitData(project))
            put("branch", CommonUtils.getBranch(project))
            put("isSupportMcpFeature", CommonUtils.isSupportMcpFeature())
        }
        callBack.invoke(response)
    }

    private fun handleNewChatRequest(project: Project, actionData: String?, callBack: DataCallBack) {
        if (actionData == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Missing actionData for newChat request")
            })
            return
        }
        //解析actionData
        val parseObject = JSONObject.parseObject(actionData, NewChatActionData::class.java)

        // 缓存当前选中的代码到会话缓存中
        val sessionSelectedCodeCache = project.service<SessionSelectedCodeCache>()
        val currentSelectedCode = EditorUtil.getCurrSelectEditorText(project)
        sessionSelectedCodeCache.cacheSelectedCode(parseObject.sessionUid, currentSelectedCode)
        LogUtil.info("newChat cached selected code for session ${parseObject.sessionUid}: ${currentSelectedCode?.content?.take(100)}")

        // 创建ExtraInfoFactory并生成extraInfo
        var extraInfo: ExtraInfo? = null
        val listTools:List<com.alibaba.fastjson2.JSONObject> = mcpCoreService.getToolList(project)
        ApplicationManager.getApplication().invokeAndWait {
            extraInfo = ExtraInfoFactory(project).createExtraInfo(listTools)
        }

//        LogUtil.info("requestChat extraInfo: $extraInfo")
        // 创建checkpointObject
        val id = parseObject.sessionUid+""+parseObject.roundIndex
        val commitId = project.service<SnapShotWrapper>().doSnapShotInAgentMode(parseObject.sessionUid,parseObject.roundIndex.toString() + "_" + System.currentTimeMillis())
        val checkpointObject = JSONObject().apply {
            put("id", id)
            put("commitId", commitId)
        }
//        LogUtil.info("checkpointObject extraInfo: $checkpointObject")
        val attachment: JSONArray = JSONArray()
        parseObject.attachment?.forEach { item ->
            when (item.type) {
                "chunk" -> {
                    // 处理代码块类型
                    val filePath = item.path
                    val startLine = item.range?.getInteger("start_line") ?: 1
                    val endLine = item.range?.getInteger("end_line") ?: 1

                    // 获取指定行范围的文件内容
                    val fileContent = VfsUtils.getLocalVfsUtf8Content(filePath, project)
                    val contentLines = fileContent.lines()

                    // 确保行号在有效范围内
                    val validStartLine = startLine.coerceIn(1, contentLines.size)
                    val validEndLine = endLine.coerceIn(validStartLine, contentLines.size)

                    // 提取指定范围的内容
                    val chunkContent = contentLines.subList(validStartLine - 1, validEndLine).joinToString("\n")

                    // 创建JSON对象
                    val jsonObject = JSONObject().apply {
                        put("path", filePath)
                        put("content", chunkContent)
                        val rangeObj = JSONObject()
                        rangeObj.put("start_line", validStartLine)
                        rangeObj.put("end_line", validEndLine)
                        put("range", rangeObj)
                        put("hash", fileContent.md5())
                        put("type", "chunk")
                    }
                    attachment.add(jsonObject)
                }
                else -> {
                    // 处理文件类型（默认）
                    val fileContent = VfsUtils.getLocalVfsUtf8Content(item.path, project)
                    val lineSize = fileContent.lines().size
                    val (startLine, endLine) = if (lineSize > 0) {
                        1 to lineSize
                    } else {
                        0 to 0
                    }
                    val jsonObject = JSONObject().apply {
                        put("path", item.path)
                        put("content", fileContent)
                        val rangeObj = JSONObject()
                        rangeObj.put("start_line", startLine)
                        rangeObj.put("end_line", endLine)
                        put("range", rangeObj)
                        put("hash", fileContent.md5())
                        put("type", "file")
                    }
                    attachment.add(jsonObject)
                }
            }
        }
        val response = JSONObject().apply {
            // 创建checkPoint
            put("configType",service<RemoteMcpService>().getCurrType())
            put("checkPoint", checkpointObject)
            put("extraInfo", extraInfo)
            put("attachment", attachment)
        }
        callBack.invoke(response)
    }

    private fun handleLocalAgentServerRequest(project: Project, callBack: DataCallBack) {
        val response = JSONObject().apply {
            put("isRunning", agentService.isRunning())
            put("port", agentService.getPort())
        }
        callBack.invoke(response)
    }

    private fun handleAddCheckPointRequest(project: Project, actionData: String?, callBack: DataCallBack){
        if (actionData == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Missing actionData for resetCheckPoint request")
            })
            return
        }

        //解析actionData
        val parseObject = JSONObject.parseObject(actionData)
        val sessionUid = parseObject.getString("sessionUid")
        val roundIndex = parseObject.getIntValue("roundIndex")
        val commitID = project.service<SnapShotWrapper>()
            .doSnapShotInAgentMode(sessionUid, parseRoundIndexToQuestionID(roundIndex))
        callBack.invoke(mapOf(
            "commitId" to commitID,
            "result" to true
        ))
        return
    }

    private fun handleResetCheckPointRequest(project: Project, actionData: String?, callBack: DataCallBack) {
        if (actionData == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Missing actionData for resetCheckPoint request")
            })
            return
        }
        //解析actionData
        val parseObject = JSONObject.parseObject(actionData)
        val type = parseObject.getString("type")
        val sessionUid = parseObject.getString("sessionUid")
        val commitId = parseObject.getString("commitId")
        val isSuccess = project.service<SnapShotWrapper>().resetSnapshotInAgentMode(sessionUid,commitId,
            "RESET_QUESTION" == type
        )

        val response = JSONObject().apply {
            put("result", isSuccess)
            if (!isSuccess) {
                put("errorMsg", "reset check point failed")
            }
        }
        callBack.invoke(response)
    }

    private fun handleStopChatRequest(project: Project, actionData: String?, callBack: DataCallBack) {
        if (actionData == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Missing actionData for stopChat request")
            })
            return
        }

        //解析actionData
        val parseObject = JSONObject.parseObject(actionData)
        val sessionUid = parseObject.getString("sessionUid")
        val roundIndex = parseObject.getInteger("roundIndex")
        val reason = parseObject.getString("reason")
        val toolName = parseObject.getString("toolName") ?: ""
        LogUtil.info("stop chat request, sessionUid: $sessionUid, roundIndex: $roundIndex, reason: $reason start stop tools")
        //通知mcp tools 停止工具执行
        mcpCoreService.stopTool(project,toolName)
        val response = JSONObject().apply {
            put("result", true)
            put("errorMsg", "")
        }
        callBack.invoke(response)
    }

    private fun handleOpenFileRequest(project: Project, actionData: String?, callBack: DataCallBack) {
        if (actionData == null) {
            callBack.invoke(JSONObject().apply {
                put("error", "Missing actionData for openFile request")
            })
            return
        }

        // 解析actionData
        val parseObject = JSONObject.parseObject(actionData, OpenFileActionData::class.java)
        val filePath = parseObject.filePath
        val startLine = parseObject.startLine

        // 打开文件并跳转到指定行
        ApplicationManager.getApplication().invokeLater {
            try {
                // 处理文件路径 - 支持相对路径和绝对路径
                val fileObj = if (File(filePath).isAbsolute) {
                    // 如果是绝对路径，直接使用
                    File(filePath)
                } else {
                    // 如果是相对路径，与项目路径拼接
                    File(project.basePath, filePath)
                }

                LogUtil.info("Opening file: ${fileObj.absolutePath}")
                val virtualFile = LocalFileSystem.getInstance().findFileByIoFile(fileObj)

                if (virtualFile != null && virtualFile.exists()) {
                    // 创建一个文件描述符，指定文件、行和列
                    val descriptor = OpenFileDescriptor(project, virtualFile, startLine - 1, 0)
                    // 打开文件并导航到指定位置
                    FileEditorManager.getInstance(project).openTextEditor(descriptor, true)

                    callBack.invoke(JSONObject().apply {
                        put("result", true)
                    })
                } else {
                    LogUtil.info("File not found: ${fileObj.absolutePath}")
                    callBack.invoke(JSONObject().apply {
                        put("result", false)
                        put("errorMsg", "File not found: ${fileObj.absolutePath}")
                    })
                }
            } catch (e: Exception) {
                LogUtil.info("Failed to open file: ${e.message}")
                callBack.invoke(JSONObject().apply {
                    put("result", false)
                    put("errorMsg", "Failed to open file: ${e.message}")
                })
            }
        }
    }

    // 计算 MD5 哈希值的扩展函数
    private fun String.md5(): String {
        val bytes = MessageDigest.getInstance("MD5").digest(this.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }

    private fun parseRoundIndexToQuestionID(roundInt: Int) : String{
        return roundInt.toString() + "_" + System.currentTimeMillis()
    }
}

/**
 * OpenFile动作的数据结构
 */
data class OpenFileActionData(
    val filePath: String,
    val fileName: String,
    val startLine: Int,
    val endLine: Int,
    val startCharacter: Int,
    val endCharacter: Int
)
