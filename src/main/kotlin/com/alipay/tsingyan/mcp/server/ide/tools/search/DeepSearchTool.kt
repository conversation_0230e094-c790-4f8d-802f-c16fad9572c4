package com.alipay.tsingyan.mcp.server.ide.tools.search

import com.alipay.tsingyan.mcp.server.base.ParamDesc
import com.alipay.tsingyan.mcp.server.base.ResultType
import com.alipay.tsingyan.mcp.server.base.ToolDesc
import com.alipay.tsingyan.mcp.server.base.ToolResult
import com.alipay.tsingyan.mcp.server.ide.tools.IdeFunTool
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project

@ToolDesc(
    name = "deep_search",
    resultType = ResultType.CONTENT_LIST,
    desc = "TREAT this tool as `codebase_retrieval`, if you want to use `codebase_retrival` tool please use `deep_search` tool instead.\n" +
            "This tool is Action2Code's context engine, the best codebase context engine in AntGroup. It:\n" +
            "1. Takes in a natural language description of the code you are looking for;\n" +
            "2. Uses a proprietary retrieval/embedding model suite that produces the highest-quality recall of relevant code snippets from across the codebase;\n" +
            "3. Maintains a real-time index of the codebase, so the results are always up-to-date and reflects the current state of the codebase;\n" +
            "4. Can retrieve across different programming languages;\n" +
            "5. Only reflects the current state of the codebase on the disk, and has no information on version control or code history.",
    timeOut = 120_000
)
@Service(Service.Level.PROJECT)
class DeepSearchTool(project: Project) : IdeFunTool<DeepSearchInfo>(project) {
    /**
     * 工具是否启动
     */
    override fun available(): Boolean {
        return true
    }

    override fun execute(param: DeepSearchInfo, sessionUid: String, questionUid: String): ToolResult {
        return ToolResult.notRedayResult
    }
}

data class DeepSearchInfo(
    @ParamDesc("搜索查询以查找相关代码。除非有明确的理由不这样做，否则您应该重复使用用户的确切查询/最新消息。")
    val query: String,

    @ParamDesc(required = false, desc = "一个句子解释了为什么要使用此工具，以及它如何贡献目标。")
    val explanation: String? = null,
)