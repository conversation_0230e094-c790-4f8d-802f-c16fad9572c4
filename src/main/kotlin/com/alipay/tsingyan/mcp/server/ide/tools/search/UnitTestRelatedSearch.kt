package com.alipay.tsingyan.mcp.server.ide.tools.search

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.agent.bean.ChatIntentType
import com.alipay.tsingyan.agent.bean.ChatRelatedRequest
import com.alipay.tsingyan.agent.bean.SearchResult
import com.alipay.tsingyan.agent.`interface`.AgentService
import com.alipay.tsingyan.mcp.server.base.ContentResult
import com.alipay.tsingyan.mcp.server.base.ParamDesc
import com.alipay.tsingyan.mcp.server.base.ResultType
import com.alipay.tsingyan.mcp.server.base.ToolDesc
import com.alipay.tsingyan.mcp.server.base.ToolResult
import com.alipay.tsingyan.mcp.server.ide.tools.IdeFunTool
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.ProjectCache
import com.alipay.tsingyan.utils.editor.EditorUtil
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project

@ToolDesc(
    name = "ut_related_search",
    resultType = ResultType.CONTENT_LIST,
    enabled = false,
    desc = "When generating unit test code, retrieve relevant code snippets for the Code Under Test (SUT) and formulate specific instructions.\n" +
            "This tool specializes in relevant code retrieval and instruction generation, exclusively for unit test code generation contexts.\n" +
            "It is designed to be invoked only during the initial phase of a unit test generation task. The tool will retrieve code pertinent to the SUT, combine it with specific instructions, and directly output the concatenated string as the model's prompt input.\n" +
            "Should the model identify additional missing dependencies, or find the tool's returned result problematic, it can utilize other code retrieval tools for a subsequent retrieval."
)
@Service(Service.Level.PROJECT)
class UnitTestRelatedSearch(project: Project) : IdeFunTool<UnitTestSearchInfo>(project) {


    override fun execute(param: UnitTestSearchInfo, sessionUid: String, questionUid: String): ToolResult {
        val searchResult = doQuery(param.query, sessionUid, questionUid)
        LogUtil.info("ut_related_search searchResult: ${JSON.toJSONString(searchResult)}")
        val nlBuilder = StringBuilder()
        val fileList = mutableListOf<ContentResult>()

        if (searchResult == null || searchResult.localRepoReferenceRagList.isNullOrEmpty()) {
            nlBuilder.appendLine("未查询到对应结果")
        } else {
            nlBuilder.appendLine(searchResult.localRepoReferenceRagList.first().snippet)
        }

        return ToolResult()
            .buildContentSuccess(
                nlBuilder.toString(),
                "获取到结果如下：",
                fileList
            )
    }


    /**
     * 做查询
     */
    private fun doQuery(query: String, sessionUid: String, questionUid: String): SearchResult? {
        // 从会话缓存中获取用户提问时选中的代码块
        val sessionSelectedCodeCache = project.service<SessionSelectedCodeCache>()
        val cachedSelectedCode = sessionSelectedCodeCache.getCachedSelectedCode(sessionUid)

        val currReferenceList = if (null == cachedSelectedCode) {
            emptyList()
        } else {
            listOf(cachedSelectedCode)
        }

        LogUtil.info("ut_related_search using cached selected code for session $sessionUid: ${cachedSelectedCode?.content?.take(100)}")

        val chatRelatedRequest = ChatRelatedRequest(
            sessionId = sessionUid,
            questionUid = (questionUid),
            projectUrl = VfsUtils.getProjectBaseDir(project),
            query = query,
            currentFileUrl = VfsUtils.getEditorSelectedFile(project)?.path ?: "",
            projectGitUrl = ProjectCache.getGitData(project),
            branch = CommonUtils.getBranch(project),
            userToken = CommonUtils.getDefaultToken(),
            productType = CommonUtils.getProductType(),
            chatIntent = ChatIntentType.TEST,
            referenceList = currReferenceList
        )
        LogUtil.info("ut_related_search chatRelatedRequest: ${JSON.toJSONString(chatRelatedRequest)}")
        return service<AgentService>().queryChatRelatedInfoWithAgent(chatRelatedRequest)
    }
}

data class UnitTestSearchInfo(
    @ParamDesc("The user's original instruction.")
    val query: String,

    @ParamDesc(
        required = false,
        desc = "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
    )
    val explanation: String? = null,
)