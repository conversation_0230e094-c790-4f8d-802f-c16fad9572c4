package com.alipay.tsingyan.mcp.server.ide.tools.search

// O(1) 时间
val excludePatternsSuffix = hashSetOf(
    ".min.js",
    ".min.css",
    ".bundle.js",
    ".lock",
    ".tmp",
    ".bak",
    ".swp",
    ".log",
    ".DS_Store",
    ".class",
    ".jar",
    ".war",
    ".ear",
    ".o",
    ".a",
    ".so",
    ".dylib",
    ".dll",
    ".exe",
    ".out",
    ".bin",
    ".img",
    ".iso",
    ".tar",
    ".gz",
    ".zip",
    ".rar",
    ".7z"
)

// O(n) 时间
val excludePatternsPath = listOf(
    "/target/",
    "/.github/",
    "/.git/",
    "/smartunit/",
    "/node_modules/",
    "/dist/",
    "/build/",
    "/out/",
    "/venv/",
    "/__pycache__/",
    "/coverage/",
    "/logs/",
    "/tmp/",
    "/.idea/",
    "/.vscode/",
)
