package com.alipay.tsingyan.mcp.server.ide.argument

import cn.hutool.core.thread.ThreadUtil
import com.alipay.tsingyan.inline2.bean.FastApplyBean
import com.alipay.tsingyan.inline2.bean.StrReplaceEdit
import com.alipay.tsingyan.mcp.server.base.DiffResult
import com.alipay.tsingyan.mcp.server.base.ResultType
import com.alipay.tsingyan.mcp.server.base.ToolDesc
import com.alipay.tsingyan.mcp.server.base.ToolResult
import com.alipay.tsingyan.mcp.server.ide.tools.TimeoutEditFunTool
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.composer.ComposerService
import com.alipay.tsingyan.services.composer.flow.StrReplaceInfo
import com.alipay.tsingyan.services.composer.flow.StrReplaceUtil
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project

@ToolDesc(
    name = "str_replace_editor",
    resultType = ResultType.DIFF_RESULT,
    timeOut = 15000,
    enabled = true,
    desc = "Custom editing tool for editing existing files\n" +
            "\n" +
            "* `path` is a file path relative to the workspace root.\n" +
            "  The tool outputs the final diff for each file, reflecting its state after all edits and IDE auto-formatting have been applied.\n" +
            "\n" +
            "Notes for using this tool:\n" +
            "\n" +
            "* The `old_str_start_line_number` and `old_str_end_line_number` parameters are 1-based line numbers\n" +
            "* Both `old_str_start_line_number` and `old_str_end_line_number` are INCLUSIVE\n" +
            "* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespace!\n" +
            "* Empty `old_str` is allowed only when the file is empty or contains only whitespaces\n" +
            "* It is important to specify `old_str_start_line_number` and `old_str_end_line_number` to disambiguate between multiple occurrences of `old_str` in the file* The `new_str` parameter should contain the edited lines that should replace the `old_str`. Can be an empty string to delete content\n" +
            "\n" +
            "IMPORTANT:\n" +
            "\n" +
            "* This is the only tool you should use for editing files.\n" +
            "* If it fails try your best to fix inputs and retry.\n" +
            "* DO NOT fall back to removing the whole file and recreating it from scratch.\n" +
            "* Call view_file tool to read the file before editing it.",
)
@Service(Service.Level.PROJECT)
class StrReplaceTool(project: Project) : TimeoutEditFunTool<StrReplaceInfo>(project) {
    private val logger = LogUtil
    private val lineNumberErrorTolerance = 0.2
    override fun doExecute(
        param: StrReplaceInfo,
        sessionUid: String,
        questionUid: String,
    ): ToolResult {
        // 编辑文件信息
        targetResult = null
        val result = ToolResult()
        try {
            logger.info("开始执行字符串替换编辑工具，路径: ${param.path}")
            // 读取文件内容，使用 VfsUtils
            val fileContents = VfsUtils.getLocalVfsUtf8Content(param.path, project)
            if (fileContents.isEmpty()) {
                // 尝试创建文件
                val vfsFile = VfsUtils.getOrCrateVfsFile(param.path, project)
                if (vfsFile == null) {
                    logger.error("无法创建或找到文件: ${param.path}")
                    return result.buildDiffError("Cannot create or find file: ${param.path}","无法创建或找到文件")
                }
            }

            // 执行字符串替换
            val toolResponse = handleStrReplace(param.path, sessionUid, questionUid, fileContents, param)
            logger.info("字符串替换执行完成，路径: ${param.path}")
            return toolResponse
        } catch (e: Exception) {
            logger.error("字符串替换编辑工具执行异常", e)
            return result.buildDiffError("看起来工具调用异常了, 异常信息如下：ExceptionType ${e::class.java.name} message:${e.message}","看起来工具调用异常了")
        }
    }

    /**
     * 处理字符串替换命令 - 移植自 Python 版本的 handle_str_replace
     */
    private fun handleStrReplace(
        path: String,
        sessionUid: String,
        questionUid: String,
        fileContents: String,
        strReplaceInfo: StrReplaceInfo
    ): ToolResult {
        logger.debug("开始处理字符串替换命令，文件: $path")

        val fileTextInfo = StrReplaceUtil.prepareTextForEditing(fileContents)
        val originalContent = fileTextInfo["content"]!!
        val originalLineEnding = fileTextInfo["originalLineEnding"]!!

        var currentContent = originalContent

        logger.debug("执行字符串替换")
        val result = StrReplaceUtil.singleStrReplace(
            path,
            currentContent,
            strReplaceInfo.old_str,
            strReplaceInfo.new_str,
            lineNumberErrorTolerance,
            project,
            sessionUid,
            questionUid
        )

        if (!(result["isError"] as Boolean) && result.containsKey("newContent")) {
            currentContent = result["newContent"] as String
            logger.debug("替换成功")
        } else if (result["isError"] as Boolean) {
            logger.debug("替换失败")
        }

        return prepareToolResponse(path, sessionUid, questionUid, originalContent, currentContent, result, originalLineEnding)
    }

    /**
     * 准备工具响应消息 - 移植自 Python 版本的 prepare_tool_response
     * 🔧 这个函数展示了 originalLineEnding 的完整用途：
     * 1. single_str_replace 返回标准化的内容（使用 \n）
     * 2. 在这里恢复原始行结束符格式
     * 3. 写入文件时保持原始格式
     */
    private fun prepareToolResponse(
        path: String,
        sessionUid: String,
        questionUid: String,
        originalFileContents: String,
        newContent: String,
        result: Map<String, Any>,
        originalLineEnding: String
    ): ToolResult {
        logger.debug("准备工具响应消息，命令: str_replace，文件: $path")
        val toolResult = ToolResult()

        // 🔧 重要！这里才是 originalLineEnding 真正被使用的地方
        val originalContentWithLineEndings = StrReplaceUtil.restoreLineEndings(originalFileContents, originalLineEnding)
        val newContentWithLineEndings = StrReplaceUtil.restoreLineEndings(newContent, originalLineEnding)

        val bean = FastApplyBean(isAgent = true)
        if (originalContentWithLineEndings != newContentWithLineEndings) {
            logger.debug("检测到内容变更，开始写入文件")
            // 🔧 关键！这里是 originalLineEnding 真正发挥作用的地方
            // 写入文件时使用恢复了原始行结束符格式的内容
            // 确保文件格式的跨平台兼容性
            try {
                bean.fileUrl = path
                bean.question = ""
                bean.draftCode = newContentWithLineEndings
                bean.sessionId = sessionUid
                bean.questionUid = questionUid
                bean.editPlus = StrReplaceEdit(result)
                // 先进行真正的文本内容计算
                project.service<ComposerService>().applyCodeStreamQueue(bean)
            } catch (e: Exception) {
                logger.error("写入文件失败: $path", e)
                return toolResult.buildDiffError("Error writing file $path: ${e.message}","写入文件失败")
            }

            // Sort results by line number for correct line number adjustments

            var lineShift = 0
            if (result.containsKey("newStrStartLineNumber") && result.containsKey("newStrEndLineNumber")) {
                logger.debug(
                    "调整行号，开始行: ${result["newStrStartLineNumber"]}，" +
                            "结束行: ${result["newStrEndLineNumber"]}，偏移: $lineShift"
                )

                val mutableResult = result.toMutableMap()
                mutableResult["newStrStartLineNumber"] = (result["newStrStartLineNumber"] as Int) + lineShift
                mutableResult["newStrEndLineNumber"] = (result["newStrEndLineNumber"] as Int) + lineShift
                mutableResult["newContent"] = newContent

                lineShift += result["numLinesDiff"] as Int
            }

            val mutableResult = result.toMutableMap()
            mutableResult["newContent"] = newContent
        } else {
            logger.debug("未检测到内容变更")
        }

        // Check if all edits succeeded or failed
        val failed = result["isError"] as Boolean

        // Generate result messages
        val resultMessages = mutableListOf<String>()
        val genMessageFunc = result["genMessageFunc"] as? ((Map<String, Any>) -> String)
        val message = genMessageFunc?.invoke(result) ?: "No message available"
        resultMessages.add("Result for str_replace for entry with index [${result["index"]}]:\n$message\n")

        val resultMessagesStr = resultMessages.joinToString("\n")

        // Generate summary message
        val summaryMessage = when {
            failed -> {
                logger.info("编辑操作失败，文件: $path")
                """Failed to edit the file $path. See below for details.
$resultMessagesStr
Fix failed str_replace entries accordingly and try again.
"""
            }
            !failed -> {
                logger.info("编辑操作成功，文件: $path")
                """Successfully edited the file $path.
str_replace
Review the changes and make sure they are as expected (correct indentation, no duplicate lines, etc).
Edit the file again if necessary.
"""
            }
            else -> {
                logger.info("编辑操作部分成功，文件: $path")
                """Partially edited the file $path. See below for details.
$resultMessagesStr
Fix failed str_replace entries accordingly and try again.
"""
            }
        }

        // 🔧 关键修复：根据执行结果返回正确的 ToolResult 类型
        if (failed) {
            logger.debug("返回错误响应，文件: $path")
            return toolResult.buildDiffError(nlResult = summaryMessage, "替换操作失败")
        } else {
            logger.debug("返回成功响应，文件: $path")
            val startTime = System.currentTimeMillis()
            while (System.currentTimeMillis() < startTime + editTimeout){
                if (null == targetResult) {
                    ThreadUtil.safeSleep(200)
                } else {
                    if (targetResult?.uid == bean.getQuestionHash()) {

                        val lastNl = summaryMessage + "\n the diff patch is \n " + (targetResult?.diffPatch ?: "")
                        toolResult.buildDiffSuccess(
                            nlResult = lastNl,
                            desc = "字符串替换操作完成",
                            data = DiffResult(
                                path = targetResult?.filePath ?: "",
                                currAddLine = targetResult?.currAddLine ?: 0,
                                currRemoveLine = targetResult?.currRemoveLine ?: 0,
                                totalAddLine = targetResult?.totalAddLine ?: 0,
                                totalRemoveLine = targetResult?.currRemoveLine ?: 0,
                                diffPatch = targetResult?.diffPatch ?: ""
                            )
                        )
                        return toolResult
                    }
                }
            }
            return toolResult.buildDiffError("Change code timed out, Please check the file content later to confirm","变更代码超时，请稍后查询文件内容以确认")
        }
    }
}


