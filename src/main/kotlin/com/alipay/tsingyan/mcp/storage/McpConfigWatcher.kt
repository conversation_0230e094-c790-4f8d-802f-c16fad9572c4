//package com.alipay.tsingyan.mcp.storage
//
//import com.alipay.tsingyan.mcp.MCPCoreService
//import com.intellij.openapi.Disposable
//import com.intellij.openapi.diagnostic.Logger
//import java.io.IOException
//import java.nio.file.FileSystems
//import java.nio.file.Path
//import java.nio.file.StandardWatchEventKinds
//import java.nio.file.WatchEvent
//import java.nio.file.WatchService
//import java.util.concurrent.atomic.AtomicBoolean
//
///**
// * 监听MCP配置文件变化的监听器
// * <AUTHOR>
// * @date 2025/04/22
// */
//class McpConfigWatcher(private val configFile: Path, private val mcpCoreService: MCPCoreService) : Disposable {
//    private val logger = Logger.getInstance(McpConfigWatcher::class.java)
//    private var watchService: WatchService? = null
//    private var watchThread: Thread? = null
//    private val isRunning = AtomicBoolean(false)
//
//    fun start() {
//        if (isRunning.get()) {
//            return
//        }
//        try {
//            val parentDir = configFile.parent
//            if (parentDir == null) {
//                logger.warn("无法获取配置文件的父目录")
//                return
//            }
//            watchService = FileSystems.getDefault().newWatchService()
//            parentDir.register(
//                watchService,
//                StandardWatchEventKinds.ENTRY_MODIFY,
//                StandardWatchEventKinds.ENTRY_CREATE,
//                StandardWatchEventKinds.ENTRY_DELETE
//            )
//            isRunning.set(true)
//            watchThread = Thread {
//                while (isRunning.get()) {
//                    try {
//                        val key = watchService?.take() ?: break
//                        for (event in key.pollEvents()) {
//                            handleFileChangeEvent(event, parentDir)
//                        }
//                        if (!key.reset()) {
//                            break
//                        }
//                    } catch (e: InterruptedException) {
//                        Thread.currentThread().interrupt()
//                        break
//                    } catch (e: Exception) {
//                        logger.warn("监听MCP配置文件时出错", e)
//                    }
//                }
//            }.apply {
//                isDaemon = true
//                name = "MCP-Config-Watcher"
//                start()
//            }
//        } catch (e: IOException) {
//            logger.error("初始化MCP配置文件监听器失败", e)
//        }
//    }
//
//    private fun handleFileChangeEvent(event: WatchEvent<*>, parentDir: Path) {
//        val kind = event.kind()
//        if (kind == StandardWatchEventKinds.OVERFLOW) {
//            return
//        }
//        val eventPath = event.context() as? Path ?: return
//        val resolvedPath = parentDir.resolve(eventPath)
//        if (resolvedPath != configFile) {
//            return
//        }
//        logger.info("检测到MCP配置文件变动: ${event.kind().name()}")
//        if (kind == StandardWatchEventKinds.ENTRY_MODIFY || kind == StandardWatchEventKinds.ENTRY_CREATE) {
//            try {
//                Thread.sleep(200L)
//            } catch (e: InterruptedException) {
//                Thread.currentThread().interrupt()
//            }
//            mcpCoreService.reloadFromStorage()
//        }
//    }
//
//    fun stop() {
//        isRunning.set(false)
//        watchThread?.interrupt()
//        watchThread = null
//        watchService?.let {
//            try {
//                it.close()
//            } catch (e: IOException) {
//                logger.warn("关闭MCP配置文件监听器失败", e)
//            }
//            watchService = null
//        }
//    }
//
//    override fun dispose() {
//        stop()
//    }
//}