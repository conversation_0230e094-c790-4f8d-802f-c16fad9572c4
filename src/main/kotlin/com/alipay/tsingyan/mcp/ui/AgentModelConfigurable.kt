package com.alipay.tsingyan.mcp.ui

import com.alipay.tsingyan.mcp.conf.RemoteMcpService
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.intellij.openapi.components.service
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.options.ConfigurableProvider
import com.intellij.openapi.util.NlsContexts
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.awt.*
import javax.swing.*

/**
 * <AUTHOR>
 */
class AgentModelConfigurableProvider : ConfigurableProvider() {
    override fun createConfigurable() = AgentModelConfigurable()
    override fun canCreateConfigurable(): Boolean = service<RemoteMcpService>().showConf
}

/**
 * <AUTHOR>
 */
class AgentModelConfigurable : Configurable {
    private var mainPanel: JPanel? = null
    private var buttonGroup: ButtonGroup? = null
    private var radioButtons: MutableList<JRadioButton> = mutableListOf()
    
    private val manager = service<RemoteMcpService>()
    private val settingStore = service<TsingYanSettingStore>()
    private var initialSelection: String = ""

    override fun getDisplayName(): @NlsContexts.ConfigurableName String = "AP Partner"

    override fun createComponent(): JComponent? {
        return createMainPanel()
    }

    private fun createMainPanel(): JComponent {
        mainPanel = JPanel(BorderLayout())
        mainPanel!!.border = JBUI.Borders.empty(20)

        // 创建主内容面板
        val contentPanel = JPanel()
        contentPanel.layout = BoxLayout(contentPanel, BoxLayout.Y_AXIS)

        // 页面启动时调用 updateRemoteToolsConf 更新 toolMapList
        manager.updateRemoteToolsConf()

        // 创建Agent模式增强部分
        contentPanel.add(createAgentModeSection())

        mainPanel!!.add(contentPanel, BorderLayout.CENTER)
        return mainPanel!!
    }

    private fun createAgentModeSection(): JPanel {
        val section = JPanel()
        section.layout = BoxLayout(section, BoxLayout.Y_AXIS)
        section.alignmentX = Component.LEFT_ALIGNMENT

        // Agent模式标题
        val titleLabel = JBLabel("Agent 模式增强（Beta）")
        titleLabel.font = UIUtil.getLabelFont().deriveFont(Font.BOLD, 16f)
        titleLabel.alignmentX = Component.LEFT_ALIGNMENT
        section.add(titleLabel)
        section.add(Box.createVerticalStrut(15))

        // 创建单选按钮组
        buttonGroup = ButtonGroup()
        radioButtons.clear()

        // 根据 toolMapList 动态创建单选按钮
        val toolMapList = manager.toolMapLists // 转换为List以便排序
        if (toolMapList.isNotEmpty()) {
            // 获取当前保存的选择
            val savedSelection = manager.selectName
            // 确定初始选择：
            // 1、如果settingStore.state.agentModeSelection是空的，就选择defaultFlag为true的一个
            // 2、如果settingStore.state.agentModeSelection不是空的，并且toolConfMap.showText有与settingStore.state.agentModeSelection相同,就选择相同的radiobutton
            // 3、如果settingStore.state.agentModeSelection不是空的，并且toolConfMap.showText没有与settingStore.state.agentModeSelection相同,就选择defaultFlag为true的一个
            initialSelection = if (savedSelection.isEmpty()) {
                // 情况1：如果settingStore.state.agentModeSelection是空的，就选择defaultFlag为true的一个
                toolMapList.firstOrNull{it.defaultFlag}?.showText ?: toolMapList.first().showText
            } else {
                // 情况2和3：检查保存的选择是否在当前列表中
                if (toolMapList.any { it.showText == savedSelection }) {
                    // 情况2：保存的选择在列表中，使用保存的选择
                    savedSelection
                } else {
                    // 情况3：保存的选择不在列表中，选择第一个
                    toolMapList.firstOrNull{it.defaultFlag}?.showText ?: toolMapList.first().showText
                }
            }

            // 动态创建单选按钮
            toolMapList.forEachIndexed { index, toolConfMap ->
                val radioButton = JRadioButton(toolConfMap.showText)
                radioButton.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 14f)
                radioButton.alignmentX = Component.LEFT_ALIGNMENT
                radioButton.isSelected = toolConfMap.showText == initialSelection
                
                // 添加事件监听器
                radioButton.addActionListener {
                    if (radioButton.isSelected) {
                        // 更新RemoteMcpService的选择
                        manager.updateSelect(toolConfMap.showText)
                    }
                }

                buttonGroup!!.add(radioButton)
                radioButtons.add(radioButton)
                section.add(radioButton)
                
                if (index < toolMapList.size - 1) {
                    section.add(Box.createVerticalStrut(10))
                }
            }

            // 设置RemoteMcpService的当前选择
            manager.updateSelect(initialSelection)
        } else {
            // 如果没有可用的工具配置，显示提示信息
            val noOptionsLabel = JBLabel("暂无可用的Agent模式配置")
            noOptionsLabel.font = UIUtil.getLabelFont().deriveFont(Font.PLAIN, 14f)
            noOptionsLabel.foreground = JBColor(Color.GRAY, Color.GRAY)
            noOptionsLabel.alignmentX = Component.LEFT_ALIGNMENT
            section.add(noOptionsLabel)
        }

        // 为整个Agent模式部分设置动态高度
        val estimatedHeight = maxOf(120, 80 + radioButtons.size * 35)
        section.preferredSize = Dimension(section.preferredSize.width, estimatedHeight)
        section.maximumSize = Dimension(Int.MAX_VALUE, estimatedHeight)
        section.minimumSize = Dimension(0, estimatedHeight)

        return section
    }

    override fun isModified(): Boolean {
        val currentSelection = getCurrentSelection()
        return currentSelection != initialSelection
    }

    override fun apply() {
        if (isModified) {
            val selectedMode = getCurrentSelection()
            
            // 更新配置
            manager.updateSelect(selectedMode)
            initialSelection = selectedMode
        }
    }

    override fun reset() {
        // 重置为初始状态
        radioButtons.forEach { radioButton ->
            radioButton.isSelected = radioButton.text == initialSelection
        }
        
        // 重置RemoteMcpService的选择
        if (initialSelection.isNotEmpty()) {
            manager.updateSelect(initialSelection)
        }
    }
    
    private fun getCurrentSelection(): String {
        return radioButtons.find { it.isSelected }?.text ?: ""
    }
}