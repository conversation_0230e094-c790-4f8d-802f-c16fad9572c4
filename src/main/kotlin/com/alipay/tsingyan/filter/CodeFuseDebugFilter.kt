package com.alipay.tsingyan.filter

import com.alipay.tsingyan.model.enums.PermissionsEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.debugfilter.DebugFilterService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.IconConstant
import com.alipay.tsingyan.utils.NotificationUtils
import com.intellij.codeInsight.hints.presentation.InputHandler
import com.intellij.execution.filters.ExceptionWorker
import com.intellij.execution.filters.Filter
import com.intellij.execution.filters.Filter.ResultItem
import com.intellij.execution.filters.HyperlinkInfo
import com.intellij.execution.filters.JvmExceptionOccurrenceFilter
import com.intellij.execution.impl.InlayProvider
import com.intellij.lang.LanguageUtil
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.colors.EditorColors
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.markup.GutterIconRenderer
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiClass
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import org.apache.commons.collections.CollectionUtils
import org.apache.http.util.TextUtils
import java.awt.Graphics
import java.awt.Point
import java.awt.Rectangle
import java.awt.event.MouseEvent
import java.io.IOException
import java.util.*
import javax.swing.Icon

/**
 * run和debug发生异常时，会有codefuse的提示，点击后会创建一个icon
 *
 * <AUTHOR>
 *
 * @date 2024-03-19 11:57:17
 */
class CodeFuseDebugFilter : JvmExceptionOccurrenceFilter {

    override fun applyFilter(exceptionClassName: String, classes: MutableList<PsiClass>, exceptionStartOffset: Int): Filter.ResultItem {
        LogUtil.info("applyFilter ${exceptionClassName} ${ classes.get(0).getProject()} ${exceptionStartOffset}")

        return CreateExceptionBreakpointResult(
            exceptionStartOffset,
            exceptionStartOffset + exceptionClassName.length,
            exceptionClassName,
            classes.get(0).getProject()
        )

    }

    private class CreateExceptionBreakpointResult(
        private val startOffset: Int,
        highlightEndOffset: Int,
        private val myExceptionClassName: String,
        private val project: Project
    ) : ResultItem(startOffset, highlightEndOffset, null as HyperlinkInfo?), InlayProvider {

        override fun createInlayRenderer(editor: Editor): EditorCustomElementRenderer {
            return CodeFusePresentation(editor, this.project, this.startOffset)
        }
    }

}


class CodeFusePresentation(editor: Editor, project: Project, startOffset: Int): EditorCustomElementRenderer, InputHandler {
    var localUserStore: LocalUserStore = service<LocalUserStore>()

    var editor = editor
    var myProject = project
    var startOffset = startOffset

    val NEW_LINE: String = "\n"
    var consoleIcon: Icon = IconConstant.CONSOLE_ICON

    override fun paint(inlay: Inlay<*>, g: Graphics, targetRegion: Rectangle, textAttributes: TextAttributes) {
        val color = EditorColorsManager.getInstance().globalScheme.getColor(EditorColors.READONLY_FRAGMENT_BACKGROUND_COLOR)
        consoleIcon = if (color != null) {
            IconConstant.CONSOLE_ICON_LIGHT
        } else {
            IconConstant.CONSOLE_ICON
        }
        val curX: Int = (targetRegion.x + (targetRegion.width / 2)) - (consoleIcon.iconWidth / 2)
        val curY: Int = (targetRegion.y + (targetRegion.height / 2)) - (consoleIcon.iconHeight / 2)
        if (curX < 0 || curY < 0) {
            return
        }
        consoleIcon.paintIcon(inlay.editor.component, g, curX, curY)
    }


    override fun getContextMenuGroupId(inlay: Inlay<*>): String? {
        return super.getContextMenuGroupId(inlay)
    }

    override fun getContextMenuGroup(inlay: Inlay<*>): ActionGroup? {
        return super.getContextMenuGroup(inlay)
    }

    override fun calcGutterIconRenderer(inlay: Inlay<*>): GutterIconRenderer? {
        return super.calcGutterIconRenderer(inlay)
    }

    override fun calcWidthInPixels(inlay: Inlay<*>): Int {
        return consoleIcon.iconWidth
    }

    override fun calcHeightInPixels(inlay: Inlay<*>): Int {
        return consoleIcon.iconHeight
    }

    override fun mouseExited() {
        //super.mouseExited()
    }

    override fun mouseMoved(event: MouseEvent, translated: Point) {
        //super.mouseMoved(event, translated)
    }

    override fun mouseClicked(event: MouseEvent, translated: Point) {
        LogUtil.info("CodeFuseDebugFilter mouseClicked ${event} ")
        val project: Project? = myProject
        project ?: return  // 如果项目为空，则直接返回
        val editor: Editor? = editor
        editor ?: return// 如果编辑器为空，则直接返回
        if (!checkLogin()){
            NotificationUtils.showBeginLoginMessage(project)
            return
        }
        val line: Int = editor.getDocument().getLineNumber(this.startOffset)
        val errorInformation = getErrorStacktrace(editor.document, this.startOffset, line)
        val triple = findExceptionContentAndCode(myProject, this.editor, line)

//        LogUtil.info("CodeFuseDebugFilter mouseClicked errorInformation ${errorInformation} ")
//        LogUtil.info("CodeFuseDebugFilter mouseClicked codeContext ${triple} ")

        project.service<DebugFilterService>()?.generateFixPrompt(project, errorInformation, triple.first, triple.second, triple.third)
    }

    private fun getErrorStacktrace(document: Document, startOffset: Int, line: Int): String {
        var line = line
        val errorHeader = document.getText(TextRange(startOffset, document.getLineEndOffset(line)))
        val sb = StringBuilder(errorHeader)
        while (true) {
            line++
            if (line >= document.lineCount) {
                break
            }
            val lineContent = document.getText(TextRange(document.getLineStartOffset(line), document.getLineEndOffset(line)))
            /**
             * 。在堆栈跟踪中，行通常以 "at " 开头，表示异常发生的位置；以 "Caused by" 开头，表示异常的原因；以 "..." 开头，表示堆栈的省略部分。如果 lineContent 表示的行不包含这些前缀，循环将被终止。
             *
             * 这是一种在解析异常堆栈跟踪时常见的逻辑，可以用来分隔异常信息和其他日志信息。
             */
            if (!lineContent.trim { it <= ' ' }.startsWith("at ") && !lineContent.trim { it <= ' ' }
                    .startsWith("Caused by") && !lineContent.trim { it <= ' ' }
                    .startsWith("...")) {
                break
            }
            sb.append(NEW_LINE)
            sb.append(lineContent)
        }
        return sb.toString()
    }

    /**
     * 返回值的Triple对象
     * 第一个是异常代码，第二个是异常代码所在文件的内容，第三个是异常代码所在文件的语言类型
     */
    fun findExceptionContentAndCode(project: Project?, editor: Editor, line: Int): Triple<String, String, String>{
        var line = line
        while (line < editor.document.lineCount) {
            val lineContent = editor.document.getText(
                TextRange(
                    editor.document.getLineStartOffset(line),
                    editor.document.getLineEndOffset(line)
                )
            )
            val myInfo = ExceptionWorker.parseExceptionLine(lineContent)
            if (myInfo?.fileName != null) {
                val fileName = myInfo.fileName
                val documentLine = myInfo.lineNumber
                val classFullPath = lineContent.substring(myInfo.classFqnRange.startOffset, myInfo.classFqnRange.endOffset)
                val vFiles: ArrayList<VirtualFile?> = ArrayList<VirtualFile?>(
                    FilenameIndex.getVirtualFilesByName(
                        project,
                        fileName!!, GlobalSearchScope.projectScope(
                            project!!
                        )
                    )
                )
                if (CollectionUtils.isEmpty(vFiles)) {
                    ++line
                } else {
                    val vFile: VirtualFile? = findMostRelatedVirtualFile(vFiles, classFullPath)
                    LogUtil.info("Find stacktrace related vfs " + vFile?.name)
                    if (vFile == null) {
                        continue
                    }

                    var content = ""
                    var exceptionCode = ""
                    var languageStr = ""
                    try {
                        val documentManager = FileDocumentManager.getInstance()
                        val document = documentManager.getDocument(vFile)
                        content = document?.text ?: ""
                        if (TextUtils.isEmpty(content)){
                            return Triple("", "", "")
                        }
                        val language = LanguageUtil.getFileLanguage(vFile)
                        if (language != null) {
                            languageStr = language.displayName.lowercase(Locale.getDefault())
                        }

                        val sb: StringBuilder = getStringBuilder(
                            content,
                            documentLine,
                            languageStr
                        )
                        exceptionCode = sb.toString()
                    } catch (var18: IOException) {
                        continue
                    } finally {
                        ++line
                    }

                    return Triple(exceptionCode, content, languageStr)
                }
            } else {
                ++line
            }
        }

        return Triple("", "", "")
    }

    fun findMostRelatedVirtualFile(virtualFiles: List<VirtualFile?>, classFullPath: String?): VirtualFile? {
        if (!CollectionUtils.isEmpty(virtualFiles) && classFullPath != null) {
            val var2: Iterator<*> = virtualFiles.iterator()

            var virtualFile: VirtualFile
            var vFileDotPath: String
            do {
                if (!var2.hasNext()) {
                    return virtualFiles[0]
                }

                virtualFile = var2.next() as VirtualFile
                var vPath = virtualFile.path
                val extPos = vPath.lastIndexOf(".")
                if (extPos > 0) {
                    vPath = vPath.substring(0, extPos)
                }

                vFileDotPath = vPath.replace("/", ".")
            } while (!vFileDotPath.endsWith(classFullPath))

            return virtualFile
        } else {
            return null
        }
    }

    fun getStringBuilder(content: String, documentLine: Int, languageStr: String?): java.lang.StringBuilder {
        val contentLines = content.split("\n".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        val sb = java.lang.StringBuilder()
        sb.append(findCompleteCodeBlock(
                contentLines,
                documentLine,
                "{",
                "}",
                10
            )
        )
        sb.append("\n")

        return sb
    }

    fun findCompleteCodeBlock(
        contentLines: Array<String>,
        documentLine: Int,
        blockStartSymbol: String?,
        blockEndSymbol: String?,
        maxSearchLine: Int
    ): String {
        var i = 0

        var found: Boolean
        found = false
        while (documentLine - i >= 0 && i < maxSearchLine) {
            val line = contentLines[documentLine - i]
            if (line.endsWith(blockStartSymbol!!)) {
                found = true
                break
            }
            ++i
        }

        var j = 0
        if (found) {
            while (documentLine + j <= contentLines.size - 1 && j < maxSearchLine) {
                val line = contentLines[documentLine + j]
                if (line.endsWith(blockEndSymbol!!)) {
                    break
                }

                ++j
            }
        } else {
            j = maxSearchLine
        }

        val sb = java.lang.StringBuilder()
        for (k in Math.max(documentLine - i, 0)..Math.min(documentLine + j, contentLines.size - 1)) {
            sb.append(contentLines[k])
            sb.append("\n")
        }

        if (sb.length > 1) {
            sb.setLength(sb.length - 1)
        }

        return sb.toString()
    }

    /**
     * 检查登录状态
     */
    fun checkLogin(): Boolean {
        val permission = localUserStore.getUserInfoModel()?.permissionsGpu
        permission?.let {
            if (permission == PermissionsEnum.ACCESS.name) {
                return true
            }
        }

        return false
    }
}