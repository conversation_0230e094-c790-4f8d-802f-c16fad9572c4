package com.alipay.tsingyan.agent.bean

data class ChatRelatedRequest(
    var sessionId: String,
    var questionUid: String,
    var projectUrl: String,
    var query: String,
    var userToken: String,
    var productType: String,

    /**
     * 当前文件的地址
     * app/web/src/main/java/com/User.java
     */
    var currentFileUrl: String,
    // 仓库远程地址 可能为空
    var projectGitUrl: String?,
    // 当前分支
    var branch: String,
    // chatHistory
    val chatHistory: List<ChatHistory> = listOf(),
    // 引用的文件列表
    var referenceList: List<ReferenceBean> = listOf(),
    val chatIntent: ChatIntentType = ChatIntentType.APAR,
)

enum class ChatIntentType {
    APAR,
    CHAT,
    TEST
}

// 返回结果映射
data class SearchResult(
    val chatStatus: AgentIndexStatus?,
    val necessaryIndexPercent: Int? = null,
    val questionUid: String? = null,
    val sessionId: String? = null,
    val assitantParam: Map<String, String>? = null,
    val extraData: Map<String, String>? = null,
    val localRepoSelectedList: MutableList<SnapData>? = null,
    val localRepoReferenceRagList: MutableList<SnapData>? = null,
)

enum class AgentIndexStatus {
    BUILD_INDEX, //构建索引中
    UNDERSTAND_QUERY, // 理解问题中
    QUERY_INDEX, // 检索中
    ANSWER, //检索完成（走到这个状态，返回值localRepoReferenceRagList大概率有数据了,)
}

data class SnapData(
    val relativePath: String,
    val snippet: String,
    val startLine: Int?,
    val endLine: Int?,
)