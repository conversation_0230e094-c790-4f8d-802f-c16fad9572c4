package com.alipay.tsingyan.inline2.action

import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.inline2.compontent.FloatingToolBar
import com.alipay.tsingyan.inline2.stream.CodeLineService
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.actionSystem.OverridingAction
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import org.apache.http.util.TextUtils
import java.awt.event.KeyEvent
import java.awt.event.MouseEvent


class InlineChatAction : AnAction(),OverridingAction {
    var localUserStore: LocalUserStore = service<LocalUserStore>()
    private val editorManagerService = service<EditorManagerService>()
    val tracerService = service<CodeFuseTracerService>()
    private val codeLineService: CodeLineService = service<CodeLineService>()


    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        if (!editorManagerService.checkLogin()) {
            NotificationUtils.showBeginLoginMessage(project)
            return
        }

        val editor = e.dataContext.getData(CommonDataKeys.EDITOR) ?: return
        if (!project.isDisposed) {
            if (project.service<FileAITagService>().isTextToCodeEnableWithWarning(e)){
                return
            }

            val selectionModel = editor.selectionModel
            var selectedText = selectionModel.selectedText ?: ""
            if (TextUtils.isEmpty(selectedText)) {
                val startLine = editor.document.getLineNumber(selectionModel.selectionStart)
                selectedText = CommonUtils.getLinesContent2(editor, startLine, startLine)
                if (!TextUtils.isEmpty(selectedText.trim())) {
                    codeLineService.selectLine(editor, startLine)
                }

            }
            if (selectedText.lines().size > 700) {
                ApplicationManager.getApplication().invokeLater {
                    NotificationUtils.notifyMessage("CodeFuse", AppConstant.INLINE_CHAT_NUMS_TIPS, e.project, null)
                }
                return
            }

            val inputEvent = e.inputEvent ?: return
            var enterType = ClickType.RIGHT
            if(inputEvent is MouseEvent){
                enterType = ClickType.RIGHT
            }else if(inputEvent is KeyEvent){
                enterType = ClickType.SHORT_CUT
            }
            //来源于悬浮窗
            if (e.place == FloatingToolBar.PLACE_NAME){
                enterType = ClickType.FLOATING_TOOL_BAR
            }
            if (enterType == ClickType.RIGHT && AppConstant.IS_CLOUD_IDE_211){
                return
            }
            project.service<InlineChatService>().showInlineChat(project, editor, enterType)
        }
    }


    override fun update(e: AnActionEvent) {
        super.update(e)

        if (e.project == null) {
            e.presentation.isEnabled = false
            return
        }

        if (!editorManagerService.checkLogin()) {
            e.project?.let { e.presentation.isVisible = false }
            return
        }

        if (!AppConstant.INLINE_CHAT_ENABLE) {
            e.project?.let { e.presentation.isVisible = false }
            return
        }

        if (AppConstant.IS_CLOUD_IDE_211) {
            e.project?.let { e.presentation.isVisible = false }
            return
        }
        e.presentation.isEnabled = true
    }

}