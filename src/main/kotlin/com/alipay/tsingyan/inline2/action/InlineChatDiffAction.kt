package com.alipay.tsingyan.inline2.action

import com.alipay.tsingyan.inline2.InlineChatService
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.UpdateInBackground
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor

class InlineChatDiffAction: AnAction(), UpdateInBackground {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val editor = e.dataContext.getData("editor") as? Editor ?: return
        if (editor.isDisposed) {
            return
        }
        val service = project.service<InlineChatService>()
        service.showDiff(editor)
    }
}