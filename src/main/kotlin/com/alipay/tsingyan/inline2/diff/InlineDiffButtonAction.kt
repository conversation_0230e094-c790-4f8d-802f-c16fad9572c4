package com.alipay.tsingyan.inline2.diff

import java.awt.event.ActionEvent
import javax.swing.AbstractAction
import javax.swing.Icon

/**
 * <AUTHOR>
 * @date 2024-10-30 17:00:44
 */
abstract class InlineDiffButtonAction : AbstractAction {
    var window: InlineDiffWindow? = null

    abstract fun doAction()

    constructor()

    constructor(name: String?) : super(name)

    constructor(name: String?, icon: Icon?) : super(name, icon)

    override fun actionPerformed(e: ActionEvent) {
        doAction()
        if (this.window != null) {
            window!!.close(0)
        }
    }
}