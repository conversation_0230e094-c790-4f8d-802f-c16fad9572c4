package com.alipay.tsingyan.inline2.diff

import com.intellij.diff.DiffContentFactory
import com.intellij.diff.DiffManager
import com.intellij.diff.contents.DiffContent
import com.intellij.diff.contents.DocumentContent
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUserDataKeys
import com.intellij.diff.util.DiffUserDataKeysEx
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.fileTypes.FileTypeRegistry
import com.intellij.openapi.fileTypes.FileTypes
import com.intellij.openapi.fileTypes.UnknownFileType
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.testFramework.LightVirtualFile
import org.apache.commons.lang3.StringUtils
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Toolkit
import java.awt.Window
import java.nio.charset.StandardCharsets
import javax.swing.Action
import javax.swing.Icon
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * <AUTHOR>
 * @date 2024-10-30 17:00:44
 */
class InlineDiffWindow(private val project: Project,
                       private val actions: MutableList<InlineDiffButtonAction>,
                       private val originCode: String,
                       private val newCode: String,
                       private val fileType: FileType = FileTypes.PLAIN_TEXT,
                       private val removeLastEmptyLine: Boolean = false) : DialogWrapper(true) {

    init {
        title = "Diff"
        init()
    }

    var afterChangeContent: DiffContent? = null
    var targetContent: String? = null

    override fun createActions(): Array<Action> {
        val list = ArrayList<Action>()
        if (actions.isNotEmpty()) {
            for (action in this.actions) {
                action.window = this
                list.add(action)
            }
        }
        return list.toTypedArray<Action>()
    }

    override fun createCenterPanel(): JComponent {
        var afterContent = this.newCode
        if (removeLastEmptyLine && afterContent.endsWith("\n")){
            afterContent = afterContent.substring(0, afterContent.length - 1)
        }


        val beforeFile = buildVirtualFile("temp", fileType, this.originCode, true)
        val afterFile = buildVirtualFile("temp", fileType, afterContent, true).apply {
            // 确保行分割符一致，避免老代码与新代码的行分隔符不一致的问题
            detectedLineSeparator = beforeFile.detectedLineSeparator
        }
        val beforeChangeContent: DiffContent = DiffContentFactory.getInstance().create(this.project, beforeFile)
        afterChangeContent = DiffContentFactory.getInstance().create(this.project, afterFile)
        beforeChangeContent.putUserData(DiffUserDataKeys.FORCE_READ_ONLY, true)
        afterChangeContent!!.putUserData(DiffUserDataKeys.FORCE_READ_ONLY, false)
        val diffRequest = SimpleDiffRequest(
            "代码对比",
            beforeChangeContent,
            afterChangeContent!!,
            "原代码",
            "CodeFuse生成代码"
        )
        val bottomPanel = JPanel(BorderLayout())
        val diffRequestPanel =
            DiffManager.getInstance().createRequestPanel(this.project, Disposer.newDisposable(), null as Window?)
        diffRequestPanel.setRequest(diffRequest)
        diffRequestPanel.putContextHints(DiffUserDataKeysEx.BOTTOM_PANEL, bottomPanel)


        // Set preferred size to match the parent window
        val diffComponent = diffRequestPanel.component

        // 获取屏幕的宽度和高度
        val screenSize = Toolkit.getDefaultToolkit().screenSize

        // 设置bottomPanel的大小为屏幕的80%
        diffComponent.preferredSize =
            Dimension((screenSize.width * 0.8).toInt(), (screenSize.height * 0.8).toInt()) // Adjust as needed


        return diffRequestPanel.component
    }

    fun getContent(): String? {
        if (this.afterChangeContent is DocumentContent) {
            return (this.afterChangeContent as DocumentContent).document.text
        }
        return this.targetContent
    }

    fun buildVirtualFile(fileName: String?, fileType: FileType?, content: String?, writable: Boolean): VirtualFile {
        return object : LightVirtualFile(
            fileName!!, fileType,
            content!!, StandardCharsets.UTF_8, 0L
        ) {
            // from class: com.alibabacloud.intellij.cosy.util.FileUtil.1
            override fun isWritable(): Boolean {
                return writable
            }
        }
    }

    abstract class DiffAcceptAction : InlineDiffButtonAction {
        abstract fun doAccept(mergeCodeStr: String?)

        constructor()

        constructor(name: String?) : super(name)

        constructor(name: String?, icon: Icon?) : super(name, icon)

        override fun doAction() {
            val content = window!!.getContent()
            doAccept(content)
        }
    }

    open class RedoAction : InlineDiffButtonAction {

        constructor()

        constructor(name: String?) : super(name)

        constructor(name: String?, icon: Icon?) : super(name, icon)

        override fun doAction() {
        }
    }

    open class CancelAction : InlineDiffButtonAction {
        constructor()

        constructor(name: String?) : super(name)

        constructor(name: String?, icon: Icon?) : super(name, icon)

        override fun doAction() {
        }
    }
}