package com.alipay.tsingyan.inline2

import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import com.intellij.ui.components.JBLabel
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.UIManager

/**
 * inlineChat 选项窗口Panel
 * <AUTHOR>
 * @date 2024-10-28 20:39:42
 */
class OptionInlayPanel(val themeColor: ThemeColor) : JPanel() {
    private val preferredHeight = 30
    private val separatorPadding = 8
    private val vGap = 0
    private val optComponentList: MutableList<InlayComponent?> = mutableListOf()
    val codeFuseLogo = JBLabel(CodeFuseIcons.INLINE_OPTION_LOGO)

    init {
        isOpaque = false
        updateLayout()
    }

    class InlayComponent(private val renderText: String, themeColor: ThemeColor, height: Int, runnable: Runnable?) : JComponent() {
//        private val myFont: Font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        private val myFont: Font = UIManager.getFont("Label.font")
        val textWidth: Int
            get() = getFontMetrics(this.myFont).stringWidth(renderText)
        val fontColor:Color = themeColor.optionFontColor

        init {
            isOpaque = false
            addMouseListener(object : MouseAdapter() {
                override fun mouseClicked(mouseEvent: MouseEvent) {
                    runnable?.run()
                }
            })
            preferredSize = Dimension(textWidth, height)
        }

        override fun paintComponent(graphics: Graphics) {
            val create = graphics.create() as Graphics2D
            create.font = myFont
            val fontMetrics = create.fontMetrics
            val stringWidth = fontMetrics.stringWidth(this.renderText)
            val height = fontMetrics.height
            val gradientPaint = GradientPaint(
                0.0f,
                0.0f,
                fontColor,
                stringWidth.toFloat(),
                height.toFloat(),
                fontColor
            )
            val height2 = ((getHeight() - height) / 2) + fontMetrics.ascent
            create.paint = gradientPaint
            create.drawString(this.renderText, 0, height2)
            create.dispose()
        }
    }

    fun addOption(str: String, runnable: Runnable?): OptionInlayPanel {
        optComponentList.add(InlayComponent(str,themeColor, preferredHeight, runnable))
        updateLayout()
        return this
    }

    fun updateLayout() {
        removeAll()
        var textWith = 0
        layout = FlowLayout(0, 0, vGap)
        //绘制icon
        codeFuseLogo.preferredSize = Dimension(14, 14)
        add(codeFuseLogo)
        textWith += codeFuseLogo.preferredSize.width

        //绘制选项
        for (index in optComponentList.indices) {
            // 左侧间隔
            val leftSpace = javax.swing.Box.createHorizontalStrut(separatorPadding)
            add(leftSpace)

            val separator = InlayComponent("|",themeColor, preferredHeight, null)
            textWith += separator.textWidth
            add(separator)

            // 右侧间隔
            val rightSpace = javax.swing.Box.createHorizontalStrut(separatorPadding)
            add(rightSpace)

            val optionComponent = optComponentList[index]
            textWith += optionComponent!!.textWidth
            add(optionComponent)

            //左右间隔的距离
            textWith += separatorPadding*2
        }
        preferredSize = Dimension(textWith + 30, preferredHeight)
//        LogUtil.info("updateLayout: $preferredSize")
        revalidate()
    }
}
