package com.alipay.tsingyan.inline2.base


//import org.fife.ui.rsyntaxtextarea.Theme
import java.awt.Color

/**
 * ui颜色常量
 */
object ColorConstant {
    /**
     * 深色模式
     */
    val DASHBOARD_TEXT = Color(187, 192, 196, 255)
    val DASHBOARD_STYLE_FOCUS = Color(72, 88, 227)
    val DASHBOARD_STYLE = Color(92, 108, 247, 255)
    val PANEL_BG = Color(44, 48, 51, 255)
    val PANEL_TEXT_COLOR = Color(225, 225, 226)//Color.WHITE
    val NEW_SESSION_TEXT = Color(255, 255, 255, 78)
    val PANEL_TEXT_UNLIGHT = Color(255, 255, 255, 150)
    val PANEL_BUTTON_ENABLE_COLOR = Color(82, 139, 247)
    val INLINE_BUTTON_HOVER_COLOR = Color(61, 100, 195)
    val PANEL_COMPLETION_BG = Color(56, 60, 63)
    val CHAT_REQUEST_BG = Color(71, 73, 76)//Color(84, 88, 90)//Color(255, 255, 255, 50)
    val CHAT_RESPONSE_BG = Color(54, 56, 59)//Color(68,71,74)//Color(255, 255, 255, 30)
    val HOVER_BG = Color(62, 64, 67)
    val CARD_BG = Color(61, 64, 67)
    val CARD_BUTTON_COLOR = Color(70, 73, 76)
    val TEXT_FIELD_BG = Color(0, 0, 0, 63)
    val CODE_FIELD_BG = Color(22, 27, 32, 255)
    val TEXT_FIELD_HINT = Color(0, 0, 0, 88)
    val TRANSPARENT_COLOR = Color(0, 0, 0, 0)
    val DIALOG_BG = Color(54, 57, 61)
    val BUTTON_INTROD_TEXT = Color(170, 171, 172)

//    val inputStream: InputStream? = FileUtil.readInputStream("themes/dark.xml")
//    val theme: Theme? = if (inputStream != null) Theme.load(inputStream) else null

}