package com.alipay.tsingyan.inline2

import com.alipay.tsingyan.inline2.base.UIPaintUtil
import com.alipay.tsingyan.inline2.compontent.SimpleBgPanel
import com.alipay.tsingyan.inline2.eventlistener.InlineChatEventListener
import com.alipay.tsingyan.inline2.stream.CodeLineService
import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.InlayDisposeContextEnum
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorCustomElementRenderer
import com.intellij.openapi.editor.Inlay
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.project.Project
import com.intellij.ui.components.JBLabel
import org.apache.http.util.TextUtils
import java.awt.*
import java.awt.event.KeyEvent
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.util.concurrent.atomic.AtomicReference
import javax.swing.JScrollPane
import javax.swing.SwingUtilities
import javax.swing.event.DocumentEvent

/**
 * @author: jianzhi
 * @date: 2024-10-17 10:22:26
 */
class InlineChatWindow(
    val project: Project,
    val sourceEditor: Editor,
    val clickType: ClickType,
    val theme: ThemeEnum,
    val themeColor: ThemeColor
) : SimpleBgPanel(), EditorCustomElementRenderer, InlineChatEventListener, Disposable {
    companion object {
        var showingWindow: InlineChatWindow? = null
    }
    private val windowBgColor: Color = themeColor.windowBgColor
    private val windowBorderColor: Color = themeColor.windowBorderColor
    private var sourceCaretOffset = 0
    private var inlay: Inlay<EditorCustomElementRenderer>? = null
    private var isChatWindowShowing = false
    private val DEFAULT_WIDTH = 500
    private val DEFAULT_HEIGHT = 44
    private var curWindowHeight = DEFAULT_HEIGHT
    private val codeLineService: CodeLineService = service<CodeLineService>()

    val chatPanel: InlineChatEditorPanel = createEditorPanel(sourceEditor).apply {
        addInlineChatEventListener(this@InlineChatWindow)
    }

    init {
        layout = GridBagLayout()
        this.isFocusable = true
        //isOpaque false这样Panel就不会填充其背景，而是显示其父容器的背景。
        isOpaque = false
        cursor = Cursor.getPredefinedCursor(0)
//        border = BorderFactory.createLineBorder(Color.RED)

        var constraints = GridBagConstraints()
        constraints.gridx = 0
        constraints.gridy = 0
        constraints.anchor = GridBagConstraints.NORTHWEST
        constraints.insets = Insets(12, 8, 0, 8) // Add 8px margin to the left
        val codeFuseLogo = JBLabel(CodeFuseIcons.INLINE_CHAT_LOGO)

        add(codeFuseLogo, constraints)

        constraints = GridBagConstraints()
        constraints.gridx = 1
        constraints.gridy = 0
        constraints.fill = GridBagConstraints.HORIZONTAL
        constraints.anchor = GridBagConstraints.CENTER
        constraints.weighty = 1.0 // Make the editor panel take up all available vertical space
        constraints.weightx = 1.0 // Make the editor panel fill the available space
        constraints.insets = Insets(6, 0, 6, 0)
        add(chatPanel, constraints)

        constraints = GridBagConstraints()
        constraints.gridx = 2
        constraints.gridy = 0
        constraints.anchor = GridBagConstraints.NORTHEAST
        constraints.insets = Insets(14, 5, 0, 5)
        val closeLabel = JBLabel(themeColor.inlineCloseIcon)
        closeLabel.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                val service = project.service<InlineChatService>()
                service.cancelRequest(sourceEditor)
                closeWindow()
            }
        })
        add(closeLabel, constraints)
    }

    override fun dispose() {
        VfsUtils.getVfsFileByEditor(sourceEditor)?.let { vfsFile ->
            project.service<FileAITagService>().removeTextToCodeTag(vfsFile)
        }
    }

    private fun createEditorPanel(editor: Editor): InlineChatEditorPanel {
        val inlineChatEditorPanel = InlineChatEditorPanel(editor, theme, themeColor)
        return inlineChatEditorPanel
    }

    fun createInlay() {
        showingWindow = this
        isChatWindowShowing = true
        val selectionModel = sourceEditor.selectionModel
        sourceCaretOffset = if (selectionModel.hasSelection()) {
            selectionModel.selectionStart
        } else {
            sourceEditor.caretModel.offset
        }
        sourceEditor.contentComponent.add(showingWindow!!)
        inlay = sourceEditor.inlayModel.addBlockElement(sourceCaretOffset, false, true, 1, showingWindow!!)
        if (inlay != null) {
            ApplicationManager.getApplication().invokeLater {
                chatPanel.getChatTextField().requestFocus()
                chatPanel.getChatTextField().isFocusable = true
                this.isFocusable = true
                val editorManagerService = service<EditorManagerService>()
                editorManagerService.cancelCompletionRequests(sourceEditor)
                editorManagerService.disposeInlays(sourceEditor, InlayDisposeContextEnum.UserAction)
            }
        } else {
            println("Failed to create inlay at offset $sourceCaretOffset")
        }
    }

    fun requestInlineChatFocus() {
        ApplicationManager.getApplication().invokeLater {
            chatPanel.getChatTextField().requestFocus()
            chatPanel.getChatTextField().isFocusable = true
            this.isFocusable = true
            val editorManagerService = service<EditorManagerService>()
            editorManagerService.cancelCompletionRequests(sourceEditor)
            editorManagerService.disposeInlays(sourceEditor, InlayDisposeContextEnum.UserAction)
        }
    }

    fun closeInlay() {
        isChatWindowShowing = false
        inlay?.dispose()
        inlay = null
        val contentComp = sourceEditor.getContentComponent()
        contentComp.remove(this);
        contentComp.revalidate();
        contentComp.repaint();
        showingWindow = null
        AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW= AtomicReference(true)
        dispose()
    }

    override fun calcWidthInPixels(p0: Inlay<*>): Int {
        return DEFAULT_WIDTH
    }

    override fun calcHeightInPixels(inlay: Inlay<*>): Int {
        return curWindowHeight
    }

    var newHeight = 0
    override fun paint(inlay: Inlay<*>, g: Graphics, r: Rectangle, textAttributes: TextAttributes) {
        val it = inlay.bounds ?: return
        if (curWindowHeight != newHeight) {
//            LogUtil.info("paint:......")
            setBounds(it.x, it.y, it.width, curWindowHeight)
            newHeight = curWindowHeight
            revalidate()
            repaint()
        }
    }

    override fun paintComponent(g: Graphics) {
        super.paintComponent(g)
        val g2d = g.create() as Graphics2D
        // 绘制自定义背景颜色的圆角矩形
        UIPaintUtil.paintRoundedCornerWithBorderAndColor(this, g2d, 16, 1f, windowBgColor, windowBorderColor)
        g2d.dispose()
    }

    override fun textChanged(event: DocumentEvent) {
//        LogUtil.info("textChanged: ${event.document}")
        ApplicationManager.getApplication().invokeLater {
            updateTextAreaHeight3()
            //这一步是必须的！！！否则高度不会变化
            this.inlay?.let {
                inlay!!.update()
            }
            <EMAIL>()
            <EMAIL>()
        }
    }

    override fun keyTyped(event: KeyEvent) {
//        LogUtil.info("keyTyped: ${event.keyChar}")
        var isShiftDown: Boolean = false
        try {
            isShiftDown = event.isShiftDown
        } catch (e: Exception) {
            LogUtil.info("e.isShiftDown 获取失败：$e")
        }
        if ((isShiftDown && event.keyChar.toInt() == KeyEvent.VK_ENTER) || event.keyChar.toInt() == 13) {
            // 换行
            chatPanel.getChatTextField().append("\n")
        } else if (event.keyChar.toInt() == KeyEvent.VK_ENTER) {
            // 回车
            onEnterKeyClick()
            event.consume() // 拦截回车事件，防止添加换行符
        } else if (event.keyChar.toInt() == KeyEvent.VK_ESCAPE) {
            val service = project.service<InlineChatService>()
            service.cancelRequest(sourceEditor)
            closeWindow()
        }
    }

    override fun sendButtonClicked() {
        onEnterKeyClick()
    }

    private fun onEnterKeyClick() {
        val message = chatPanel.getChatTextField().text.trim()
        if (TextUtils.isEmpty(message)){
            return
        }
        ApplicationManager.getApplication().invokeLater {
            LogUtil.info("startLoading..........")
            chatPanel.startLoading()
        }
        val service = project.service<InlineChatService>()
        if (!service.isAllEditorProcessing()){
            if (TextUtils.isEmpty(sourceEditor.selectionModel.selectedText)) {
                val startLine = sourceEditor.document.getLineNumber(sourceEditor.selectionModel.selectionStart)
                val selectedText = CommonUtils.getLinesContent2(sourceEditor, startLine, startLine)
                if (!TextUtils.isEmpty(selectedText)) {
                    codeLineService.selectLine(sourceEditor, startLine)
                }

            }
            service.startRequestAlarm(project, sourceEditor, message, clickType)
        } else {
            closeWindow()
            ApplicationManager.getApplication().invokeLater {
                NotificationUtils.notifyInlineChatMessageInProcessing(project)
            }
        }
    }

    private fun closeWindow() {
        ApplicationManager.getApplication().invokeLater {
            if (isChatWindowShowing) {
                closeInlay()
            }
        }
    }

    val maxLineSize = 9
    var lastRecordLineCount = 1 // 从1开始的
    fun updateTextAreaHeight3() {
        val lineCount = chatPanel.getChatTextField().lineCount
        val lineHeight = chatPanel.fontHeight
        val addedLineCount = lineCount - lastRecordLineCount
//        LogUtil.info("updateTextAreaHeight lineSize: $lineCount")
        //根据行数调整高度
        if (lineCount <= maxLineSize){
            //小于等于maxLineSize行的高度时，逐步变化
            adjustOriginalHeight((lineCount-1) * lineHeight)
            chatPanel.getScrollPane().verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED
        } else {
            //最多显示maxLineSize行的高度
            adjustOriginalHeight(maxLineSize * lineHeight)
            chatPanel.getScrollPane().verticalScrollBarPolicy = JScrollPane.VERTICAL_SCROLLBAR_ALWAYS
        }

//        LogUtil.info("updateTextAreaHeight addedLineCount: $addedLineCount, curWindowHeight: $curWindowHeight")
        lastRecordLineCount = lineCount
        SwingUtilities.invokeLater { chatPanel.getChatTextField().requestFocus() }
    }

    private fun adjustOriginalHeight(deltaHeight: Int) {
        curWindowHeight = DEFAULT_HEIGHT + deltaHeight
        chatPanel.preferredSize = Dimension(chatPanel.preferredSize.width, chatPanel.chatPanelDimension.height + deltaHeight)
        chatPanel.getScrollPane().preferredSize = Dimension(-1, chatPanel.scrollPaneDimension.height + deltaHeight)
    }

    fun getIsChatWindowShowing(): Boolean {
        return isChatWindowShowing
    }

}