package com.alipay.tsingyan.inline2

import com.alipay.tsingyan.compontent.InlineChatMultiTextField
import com.alipay.tsingyan.inline2.base.UIPaintUtil
import com.alipay.tsingyan.inline2.compontent.SimpleBgPanel
import com.alipay.tsingyan.inline2.eventlistener.InlineChatEventListener
import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import com.intellij.openapi.editor.Editor
import com.intellij.ui.DocumentAdapter
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.util.ui.AsyncProcessIcon
import com.intellij.util.ui.UIUtil.HIDE_EDITOR_FROM_DATA_CONTEXT_PROPERTY
import java.awt.*
import java.awt.event.*
import javax.swing.BorderFactory
import javax.swing.JScrollPane
import javax.swing.ScrollPaneConstants
import javax.swing.event.DocumentEvent

/**
 * @author: jianzhi
 * @date: 2024-10-17 10:22:26
 */
class InlineChatEditorPanel(editor: Editor, themeEnum: ThemeEnum, themeColor: ThemeColor) : JBPanel<SimpleBgPanel>() {
    private var chatTextField: InlineChatMultiTextField
    private val scrollPane: JScrollPane
    private lateinit var inlineChatListener: InlineChatEventListener
    var chatPanelDimension: Dimension = Dimension(438, 32)
    var chatTextFieldDimension: Dimension = Dimension(406, 18)
    private var chatBackgroundColor: Color = themeColor.chatBackgroundColor
    private var chatBorderColorFocused: Color = themeColor.chatBorderColorFocused
    private var chatBorderColorNotFocused: Color = themeColor.chatBorderColorNotFocused
    var scrollPaneDimension: Dimension = Dimension(-1, 18)
    var isFocused: Boolean = false
    var fontHeight = 12
    val sendButton: JBLabel = JBLabel(CodeFuseIcons.INLINE_NOT_SEND_ICON)
    val loadingIcon = AsyncProcessIcon("Loading")
    var isLoading: Boolean = false

    init {
        layout = GridBagLayout()
        isLoading = false
        isOpaque = false
        this.isFocusable = true
        cursor = Cursor.getPredefinedCursor(0)
//        border = BorderFactory.createLineBorder(Color.RED)
        preferredSize = chatPanelDimension
        chatTextField = InlineChatMultiTextField("输入需求描述，支持shift+回车换行。例如增加xxx逻辑。",null,  sendButton, themeColor)
        chatTextField.font = Font(Font.MONOSPACED, Font.PLAIN, 12)
        fontHeight = chatTextField.getFontMetrics(chatTextField.font).height
        chatTextField.lineWrap = true  // 开启自动换行
        chatTextField.wrapStyleWord = true  // 确保不拆分单词
        chatTextField.border = BorderFactory.createEmptyBorder() // 去掉滚动条边框
        chatTextField.isEnabled = true
        chatTextField.caretColor = themeColor.caretColor
        chatTextField.isFocusable = true
        chatTextField.background = chatBackgroundColor
        chatTextField.putClientProperty(HIDE_EDITOR_FROM_DATA_CONTEXT_PROPERTY, true)

        scrollPane = JScrollPane(chatTextField)
        scrollPane.alignmentX = CENTER_ALIGNMENT
        scrollPane.verticalScrollBarPolicy = ScrollPaneConstants.VERTICAL_SCROLLBAR_AS_NEEDED
        scrollPane.horizontalScrollBarPolicy = ScrollPaneConstants.HORIZONTAL_SCROLLBAR_NEVER
        scrollPane.border = BorderFactory.createEmptyBorder()
        scrollPane.verticalScrollBar.preferredSize = Dimension(8, 5)
//        scrollPane.verticalScrollBar.background = chatBackgroundColor
        scrollPane.isWheelScrollingEnabled = true
        scrollPane.preferredSize = scrollPaneDimension
        addInputListener()
        var constraints = GridBagConstraints()
        constraints.gridx = 0
        constraints.gridy = 0
        constraints.fill = GridBagConstraints.BOTH
        constraints.anchor = GridBagConstraints.CENTER
        constraints.weightx = 1.0
        constraints.weighty = 1.0
        constraints.insets = Insets(7, 12, 6, 0)
//        chatTextField.border = BorderFactory.createLineBorder(Color.RED)
        add(scrollPane, constraints)

        constraints = GridBagConstraints()
        constraints.gridx = 1
        constraints.gridy = 0
        constraints.anchor = GridBagConstraints.SOUTHEAST
        constraints.insets = Insets(0, 0, 8, 12)
        add(sendButton, constraints)
        sendButton.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                if (inlineChatListener != null) {
                    inlineChatListener.sendButtonClicked()
                }
            }
        })
    }

    /**
     * 发送后，开始显示loading状态
     */
    fun startLoading() {
        isLoading = true
        remove(sendButton)
        add(loadingIcon, GridBagConstraints().apply {
            gridx = 1
            gridy = 0
            anchor = GridBagConstraints.SOUTHEAST
            insets = Insets(0, 0, 8, 12)
        })
        loadingIcon.isVisible = true
        loadingIcon.resume()
        revalidate()
        repaint()
    }

    /**
     * 发送完成，停止loading状态
     */
    fun stopLoading() {
        remove(loadingIcon)
        add(sendButton, GridBagConstraints().apply {
            gridx = 1
            gridy = 0
            anchor = GridBagConstraints.SOUTHEAST
            insets = Insets(0, 0, 8, 12)
        })
        loadingIcon.suspend()
        loadingIcon.isVisible = false
        revalidate()
        repaint()
    }

    override fun paintComponent(g: Graphics) {
        super.paintComponent(g)
        val g2d = g.create() as Graphics2D
        if (isFocused){
            UIPaintUtil.paintRoundedCornerWithBorderAndColor(this, g2d, 12, 1f, chatBackgroundColor, chatBorderColorFocused)
        } else {
            // 绘制自定义背景颜色的圆角矩形
            UIPaintUtil.paintRoundedCornerWithBorderAndColor(this, g2d, 12, 1f, chatBackgroundColor, chatBorderColorNotFocused)
        }
        g2d.dispose()
    }

    fun getChatTextField(): InlineChatMultiTextField {
        return chatTextField
    }

    fun setChatTextField(text: String) {
        chatTextField.text = text
    }

    fun getScrollPane(): JScrollPane {
        return scrollPane
    }

    private fun addInputListener() {
        chatTextField.addFocusListener(object : FocusListener {
            override fun focusGained(e: FocusEvent?) {
                isFocused = true
                repaint()
            }

            override fun focusLost(e: FocusEvent?) {
                isFocused = false
                repaint()
            }
        })

        // 键盘输入
        chatTextField.addKeyListener(object : KeyAdapter() {
            override fun keyTyped(e: KeyEvent) {
            }

            override fun keyPressed(e: KeyEvent) {
//                LogUtil.info("keyPressed: ${e.keyChar}")
                inlineChatListener.keyTyped(e)
            }

            override fun keyReleased(e: KeyEvent) {
//                LogUtil.info("keyReleased: ${e.keyChar}")

            }
        })

        chatTextField.document.addDocumentListener(object : DocumentAdapter() {
            override fun textChanged(documentEvent: DocumentEvent) {
                inlineChatListener.textChanged(documentEvent)
            }})
    }

    fun addInlineChatEventListener(listener: InlineChatEventListener) {
        inlineChatListener = listener
    }

}