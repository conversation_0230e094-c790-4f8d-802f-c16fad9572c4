package com.alipay.tsingyan.inline2

import com.alibaba.fastjson.JSONObject
import com.alipay.tsingyan.floating.service.FloatingToolBarService
import com.alipay.tsingyan.inline2.bean.InlineChatIntentRequestBean
import com.alipay.tsingyan.inline2.bean.InlineChatStreamReqBean
import com.alipay.tsingyan.inline2.bean.StreamResBean
import com.alipay.tsingyan.inline2.diff.InlineDiffButtonAction
import com.alipay.tsingyan.inline2.diff.InlineDiffWindow
import com.alipay.tsingyan.inline2.markup.MarkupCodeManager
import com.alipay.tsingyan.inline2.stream.StreamDiffHandler
import com.alipay.tsingyan.inline2.stream.StreamDiffRequest
import com.alipay.tsingyan.inline2.theme.ColorManager
import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.model.ClickType
import com.alipay.tsingyan.model.enums.FastApplyType
import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.model.enums.WebActionTypeEnum
import com.alipay.tsingyan.model.enums.WebTargetEnum
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.CancellableAlarm
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.file.FileAITagService
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.talk.MessageModel
import com.alipay.tsingyan.talk.UIThemeListener
import com.alipay.tsingyan.talk.WebViewService
import com.alipay.tsingyan.ui.config.LocalUserStore
import com.alipay.tsingyan.ui.config.TsingYanSettingStore
import com.alipay.tsingyan.utils.*
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.ToolWindowManager
import kotlinx.coroutines.flow.Flow
import org.apache.http.util.TextUtils
import java.util.*
import java.util.concurrent.Semaphore
import java.util.concurrent.atomic.AtomicReference
import javax.swing.SwingUtilities

/**
 * <AUTHOR>
 * @date 2024-10-15 17:01:14
 *
 * inlineChat 服务
 */
class InlineChatService(val currProject: Project): Disposable {

    var localUserStore: LocalUserStore = service<LocalUserStore>()
    val tracerService = service<CodeFuseTracerService>()
    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    private val settingData = service<TsingYanSettingStore>()
    private val inlineChatIntents: Key<InlineChatIntent> = Key.create("Ant.inlineChatIntents")
    private val intentMap:  HashMap<String,  InlineChatIntent>  = HashMap()
    val  floatingToolBarService = currProject.service<FloatingToolBarService>()

    protected var requestAlarm = CancellableAlarm(this)
    var theme: ThemeEnum = UIThemeListener.getTheme()
    var themeColor: ThemeColor = ColorManager.getColor(theme)

    override fun dispose() {
    }

    fun showInlineChat(project: Project, editor: Editor, clickType: ClickType, question: String = "") {
        if (!AppConstant.INLINE_CHAT_ENABLE) {
            return
        }
        //通知H5对话页要开始inlineChat了
//        notifyH5StartInlineChat(project)
//        if (isH5ReadingJob) {
//            ApplicationManager.getApplication().invokeLater {
//                openToolWindow(project)
//                NotificationUtils.notifyMessage("CodeFuse", "正在对话中，请稍后再试", project, null)
//            }
//            return
//        }
        //不能有别的inlineChat进行中
        if (isAllEditorProcessing()){
            ApplicationManager.getApplication().invokeLater {
                NotificationUtils.notifyInlineChatMessageInProcessing(project)
            }
            return
        }


        resetMarkUpCode(editor)
        if (inlineChatIntents[editor] == null) {
            val intent = InlineChatIntent(editor)
            inlineChatIntents[editor] = intent
            val key = getFilePath(editor)
            intentMap[key] = intent
        }
        floatingToolBarService.blockHint(editor)

        inlineChatIntents[editor].clickType = clickType
        ApplicationManager.getApplication().invokeLater {
            if (inlineChatIntents[editor]!!.isChatWindowShowing()) {
                inlineChatIntents[editor]!!.chatWindow!!.requestInlineChatFocus()
                LogUtil.info("InlineChatService showInlineChat 1 " + inlineChatIntents[editor]!!.key, false)
                return@invokeLater
            }
            theme = UIThemeListener.getTheme()
            themeColor = ColorManager.getColor(theme)
            val inlineChat = InlineChatWindow(project, editor, clickType, theme, themeColor)
            inlineChatIntents[editor]!!.chatWindow = inlineChat
            inlineChatIntents[editor]!!.chatWindow!!.createInlay()
            if (question.isNotEmpty()) {
                inlineChatIntents[editor]!!.chatWindow!!.chatPanel.setChatTextField(question)
            }
            //视图移动到选中初始行
            SwingUtilities.invokeLater {
                val position = LogicalPosition( inlineChatIntents[editor]!!.startLine, 0)
                editor.scrollingModel.scrollTo(position,ScrollType.CENTER)
            }
            LogUtil.info("InlineChatService showInlineChat 2 " + inlineChatIntents[editor]!!.key, false)
            VfsUtils.getVfsFileByEditor(editor)?.let { vfsFile ->
                currProject.service<FileAITagService>().putTextToCodeTag(vfsFile)
            }
        }
        submitInlineChatInit(project, clickType)
    }

    fun closeInlineChat(editor: Editor) {
        if (inlineChatIntents[editor] == null) {
            return
        }
        LogUtil.info("InlineChatService closeInlineChat " + inlineChatIntents[editor]?.key, false)
        if (inlineChatIntents[editor]!!.isChatWindowShowing()) {
            ApplicationManager.getApplication().invokeLater {
                floatingToolBarService.openHint()
                inlineChatIntents[editor]!!.chatWindow!!.closeInlay()
                return@invokeLater
            }
        }
    }

    fun closeAllInlineChat() {
        //遍历intentMap，关闭所有的chatWindow
        for (intent in intentMap.values) {
            if (intent.isChatWindowShowing()) {
                cancelRequest(intent.editor)
                closeInlineChat(intent.editor)
                return
            }
        }
    }

    fun isInlineChatShowing(editor: Editor): Boolean {
        return inlineChatIntents[editor]?.isChatWindowShowing() ?: false
    }

    fun showOptionViewInEditor(project: Project, editor: Editor) {
        if (inlineChatIntents[editor] == null) {
            // 这段代码用不到，暂时留着测试用，因为optionView之前肯定有ChatView出现过
            val intent = InlineChatIntent(editor)
            inlineChatIntents[editor] = intent
        }
        ApplicationManager.getApplication().invokeLater {
            if (inlineChatIntents[editor]!!.isOptionWindowShowing()) {
                LogUtil.info("InlineChatService showOptionViewInEditor 1 " + inlineChatIntents[editor]!!.key, false)
                return@invokeLater
            }
            val optionWindow = InlineOptionWindow(project, editor, theme, themeColor)
            inlineChatIntents[editor]!!.optionWindow = optionWindow
            inlineChatIntents[editor]!!.optionWindow!!.createInlay(inlineChatIntents[editor]!!.lineStartOffset)
            LogUtil.info("InlineChatService showOptionViewInEditor 2 " + inlineChatIntents[editor]!!.key, false)
        }
    }

    fun closeOptionView(editor: Editor) {
        if (inlineChatIntents[editor] == null) {
            return
        }
        LogUtil.info("InlineChatService closeOptionView " + inlineChatIntents[editor]?.key, false)
        ApplicationManager.getApplication().invokeLater {
            if (inlineChatIntents[editor]!!.isOptionWindowShowing()) {
                inlineChatIntents[editor]!!.optionWindow!!.closeInlay()
                return@invokeLater
            }
        }
        inlineChatIntents[editor].isProcessing = false
        AppConstant.ENABLE_SEND_SELECTED_CODE_MSG=true
        VfsUtils.getVfsFileByEditor(editor)?.let { vfsFile ->
            currProject.service<FileAITagService>().removeTextToCodeTag(vfsFile)
        }
    }

    fun cancelRequest(editor: Editor) {
        val intent = inlineChatIntents[editor] ?: return
        if (intent.chatWindow!!.chatPanel.isLoading){
            intent.isCancel = true
            ApplicationManager.getApplication().invokeLater {
                if (editor.project != null) {
                    NotificationUtils.notifyInlineChatCancel(editor.project!!)
                }
            }
        }
    }

    fun cleanMarkupCode(editor: Editor, isUndo: Boolean = false) {
        val markupCodeManager = service<MarkupCodeManager>()
        val intent = inlineChatIntents[editor] ?: return
        markupCodeManager.cleanMarkupCode2(intent, editor)
    }

    fun acceptMarkupCode(editor: Editor, mergeCode:String = "") {
        val intent = inlineChatIntents[editor] ?: return
        editor.markupModel.removeAllHighlighters()
        var mergeCodeStr = intent.newCode
        if (mergeCode.isNotEmpty()) {
            mergeCodeStr = mergeCode
        }
        submitInlineChatData(editor.project!!, "operate/codeGenerate/accept", mergeCodeStr ?: "")
    }

    fun redoMarkupCode(editor: Editor) {
        val intent = inlineChatIntents[editor] ?: return
        val clickType = intent.clickType
        val questionStr = intent.questionStr
        cleanMarkupCode(editor)
        ApplicationManager.getApplication().invokeLater {
                showInlineChat(editor.project!!, editor, clickType!!, questionStr)
        }
        submitInlineChatData(editor.project!!, "operate/codeGenerate/retry")
    }

    //重置标记代码
    private fun resetMarkUpCode(editor: Editor) {
        inlineChatIntents[editor] = null
        val key = getFilePath(editor)
        intentMap.remove(key)
        AppConstant.ENABLE_SEND_SELECTED_CODE_MSG = true
    }

    fun isHasMarkupCode(editor: Editor): Boolean {
        val intent = inlineChatIntents[editor] ?: return false
        return intent.isMarkup
    }

    fun isOptionViewShowing(editor: Editor): Boolean {
        return inlineChatIntents[editor]?.isOptionWindowShowing() ?: false
    }

    fun isAllEditorProcessing(): Boolean {
        //判断intentMap中是否有正在处理的
        for (intent in intentMap.values) {
            if (!intent.editor.isDisposed && intent.isProcessing) {
                return true
            }
        }
        return false
    }

//    fun getQuestionIntent(question: String, questionCode: String, editor: Editor, language: String, inlineChatId: String): String {
//        inlineChatIntents[editor]?.questionStr = question
//        inlineChatIntents[editor]?.isProcessing = true
//        val requestBean = createRequestBean(question, questionCode, editor, language,inlineChatId)
//        //Thread.sleep(10*1000)
//        val intentStr = tsingYanProdService.getInlineChatIntention(requestBean) ?: return ""
//        return intentStr
//    }

    fun getIntnetKey(editor: Editor): String {
        if (editor.isDisposed){
            return ""
        }
        return inlineChatIntents[editor]?.key ?: ""
    }

    fun isCanceled(editor: Editor): Boolean {
        if (editor.isDisposed){
            return true
        }
        return inlineChatIntents[editor]?.isCancel ?: false
    }

    fun getModelGenCode(question: String, questionCode: String, editor: Editor, language: String, inlineChatId: String): String? {
        val requestBean = createRequestBean(question, questionCode, editor, language, inlineChatId)
        requestBean.intentionList = null
        val intent = tsingYanProdService.getInlineChatCodeGen(requestBean)
        return intent
    }

    fun getStreamModelGenCode(question: String, questionCode: String, editor: Editor, language: String, inlineChatId: String, codeAfter: String, codeBefore: String): Flow<StreamResBean>? {
        val requestBean = createStreamRequestBean(question, questionCode, language, inlineChatId,editor,codeAfter,codeBefore)
        return tsingYanProdService.queryCodeGeneration(requestBean)
    }

    public fun createRequestBean(question: String, questionCode: String, editor: Editor, language: String, inlineChatId: String): InlineChatIntentRequestBean {
        val requestBean = InlineChatIntentRequestBean()
        requestBean.ideVersion = CommonUtils.getIdeVersion()
        requestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
        requestBean.userToken = localUserStore.getUserInfoModel()?.userToken
        requestBean.question = question
        requestBean.questionCode = questionCode
        requestBean.language = language
        requestBean.optionLanguage = settingData.state.ANNOTATION_LANGUAGE_SELECTOR
        requestBean.inlineChatId = inlineChatId
        return requestBean
    }


     fun createStreamRequestBean(question: String, questionCode: String, language: String, inlineChatId: String, editor: Editor, codeAfter: String, codeBefore: String): InlineChatStreamReqBean {
         val requestBean = InlineChatStreamReqBean()
         requestBean.ideVersion = CommonUtils.getIdeVersion()
         requestBean.pluginVersion = CommonUtils.getPluginVersion(AppConstant.PLUGIN_VERSION)
         requestBean.userToken = localUserStore.getUserInfoModel()?.userToken
         requestBean.question = question
         requestBean.questionCode = questionCode
         requestBean.language = language
         requestBean.optionLanguage = settingData.state.ANNOTATION_LANGUAGE_SELECTOR
         requestBean.inlineChatId = inlineChatId
         requestBean.repo = ProjectCache.getGitData(editor.project!!)
         requestBean.fileUrl = FileDocumentManager.getInstance().getFile(editor.document)?.path
         requestBean.codeAfter = codeAfter
         requestBean.codeBefore = codeBefore
         return requestBean
    }

    fun showDiff(editor: Editor){
        val intent = inlineChatIntents[editor] ?: return
        val buttonActions: MutableList<InlineDiffButtonAction> = mutableListOf()
        buttonActions.add(object : InlineDiffWindow.DiffAcceptAction("采纳"){
            override fun doAccept(mergeCodeStr: String?) {
                LogUtil.info("InlineChatService accept doAction" + intent.key, false)
                var mergeCode = mergeCodeStr ?: ""
                if (!mergeCode.endsWith("\n")) {
                    mergeCode += "\n"
                }
                acceptMarkupCode(editor, mergeCode)
                closeOptionView(editor)
            }
        })
        buttonActions.add(object : InlineDiffWindow.RedoAction("重做"){
            override fun doAction() {
                LogUtil.info("InlineChatService redo doAction " + intent.key, false)
                closeOptionView(editor)
                redoMarkupCode(editor)
            }
        })
        buttonActions.add(object : InlineDiffWindow.CancelAction("撤销"){
            override fun doAction() {
                LogUtil.info("InlineChatService cancel doAction " + intent.key, false)
                cleanMarkupCode(editor)
                closeOptionView(editor)

            }
        })
        ApplicationManager.getApplication().invokeLater {
            val diffWindow =
                InlineDiffWindow(editor.project!!, buttonActions, intent.selectedCode, intent.newCode,removeLastEmptyLine = true)
            diffWindow.show()
        }
        submitInlineChatData(editor.project!!, "operate/codeGenerate/toDiffWindow")
    }


    private fun openToolWindow(project: Project) {
        //尝试主动打开codefuse页面
        val toolWindow = ToolWindowManager.getInstance(project)
            .getToolWindow(AppConstant.TOOL_WINDOW_ID)
            ?: return
        if (!toolWindow.isVisible) {
            toolWindow.show {}
        }
    }


    fun submitInlineChatInit(project: Project, clickType: ClickType) {
        var actionType = ""
        if (clickType == ClickType.RIGHT) {
            actionType =  "right/codeGenerate"
        } else if (clickType == ClickType.ICON) {
            actionType =  "icon/codeGenerate"
        }else if (clickType == ClickType.SHORT_CUT) {
            actionType =  "shortCut/codeGenerate"
        }else if (clickType ==ClickType.FLOATING_TOOL_BAR){
            //悬浮按钮点击以及上报过事件，不需要重新上报
            return
        }
        if (actionType.isNotEmpty()){
            val jsonData = JSONObject()
            jsonData["actionType"] = actionType
            tracerService.submitTypeStrAndData(project, "FUNCTION_ACTION", jsonData)
        }
    }

    fun submitInlineChatData(project: Project, actionType: String, acceptCode: String = "") {
        if (actionType.isNotEmpty()){
            val jsonData = JSONObject()
            jsonData["actionType"] = actionType
            jsonData["traceId"] = AppConstant.lastInlineChatTraceId
            if (acceptCode.isNotEmpty()){
                jsonData["acceptCode"] = acceptCode
            }
            tracerService.submitTypeStrAndData(project, "FUNCTION_ACTION", jsonData)
        }
    }

    var isH5ReadingJob = false

    fun notifyH5StartInlineChat(project: Project) {
        val webView = project.getService(WebViewService::class.java).getWebView()
        if (!webView.getHasLoadedFinished() && !CommonUtils.isCloudIDE) {
            return
        }
        val messageModel = MessageModel()
        messageModel.actionType = WebActionTypeEnum.TO_JS.name
        messageModel.target = WebTargetEnum.QUERY_STATUS_INFO.name
        webView.sendMsgToBrowser(messageModel,messageModel.target!!,null)
    }

    fun goToOpeningChatView() {
        //遍历intentMap，找到正在处理的editor
        for (intent in intentMap.values) {
            if (intent.isProcessing) {
                val editor = intent.editor
                if (editor.isDisposed){
                    continue
                }
                val virtualFile = FileDocumentManager.getInstance().getFile(editor.document) ?: continue
                // 通过FileEditorManager打开文件
                FileEditorManager.getInstance(editor.project!!).openFile(virtualFile, true)
                // 通过editor对象设置光标位置
                editor.caretModel.moveToLogicalPosition(LogicalPosition(intent.startLine, 0))
                editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
                return
            }
        }
    }

    fun closeFile(virtualFile: VirtualFile) {
        if (intentMap[virtualFile.path] == null) {
            return
        }
        val editor = intentMap[virtualFile.path]!!.editor
        if (intentMap[virtualFile.path]!!.isProcessing) {
            ApplicationManager.getApplication().runWriteAction {
                WriteCommandAction.runWriteCommandAction(editor.project) {
                    editor.document.setText(intentMap[virtualFile.path]!!.oldDocument)
                }
            }
            cancelRequest(editor)
            AppConstant.FLOATING_TOOL_BAR_IS_SHOULD_SHOW= AtomicReference(true)
        }
        resetMarkUpCode(editor)
        currProject.service<FileAITagService>().removeAllTag(virtualFile)
    }

    private fun getFilePath(editor: Editor): String {
        val  document  = editor.document
        val  virtualFile:  VirtualFile  = FileDocumentManager.getInstance().getFile(document)  ?: return  ""
        return  virtualFile.path
    }

    fun startRequestAlarm(project: Project, editor: Editor, question: String, clickType: ClickType) {
        requestAlarm.cancelAllAndAddRequest({
            LogUtil.info("startRequestAlarm........")
            sendMessage(project, editor, question, clickType)
        }, 10)
    }

    private fun sendMessage(project: Project, editor: Editor, question: String, clickType: ClickType) {
        val semaphore = Semaphore(0)
        val intentKey = getIntnetKey(editor)
        LogUtil.info("InlineChatService sendMessage: $question, key $intentKey", false)
        if (TextUtils.isEmpty(question)){
            failed(editor, "问题不能为空")
            return
        }
        var startLine = -1
        var endLine = -1
        var fullLines = ""
        var language = "java"
        var codeAfter = ""
        var codeBefore = ""
        ApplicationManager.getApplication().invokeLater{
            try {
                fullLines = CommonUtils.getSelectedTextWithFullLines(editor)?:""
                language = AntEditorUtil.getLanguage(editor)
                val selectionModel = editor.selectionModel
                val document =editor.document
                val selectionStart = selectionModel.selectionStart
                val selectionEnd = selectionModel.selectionEnd
                inlineChatIntents[editor].oldStartOffset = selectionStart
                inlineChatIntents[editor].oldEndOffset = selectionEnd
                inlineChatIntents[editor].oldDocument = editor.document.text

                startLine = document.getLineNumber(selectionStart)
                endLine = document.getLineNumber(selectionEnd)
                val startLineOffSet = document.getLineStartOffset(startLine)
                val endLineOffSet = document.getLineStartOffset(endLine)
                if (TextUtils.isEmpty(fullLines)){
                    inlineChatIntents[editor].startOffset = selectionStart
                    inlineChatIntents[editor].endOffset = selectionStart
                    inlineChatIntents[editor].newCodeStartOffSet = selectionStart
                    inlineChatIntents[editor].emptySelected=true

                }else {
                    inlineChatIntents[editor].startOffset = startLineOffSet
                    inlineChatIntents[editor].endOffset = endLineOffSet
                    inlineChatIntents[editor].newCodeStartOffSet = startLineOffSet
                    if (selectionEnd == editor.document.textLength){
                        inlineChatIntents[editor].selectEnd =true
                    }
                }
                inlineChatIntents[editor].clickType = clickType
                inlineChatIntents[editor].questionStr = question
                inlineChatIntents[editor].selectedCode = fullLines
                inlineChatIntents[editor].isProcessing = true
                AppConstant.ENABLE_SEND_SELECTED_CODE_MSG = false
                codeBefore = editor.document.getText(TextRange(0, inlineChatIntents[editor].startOffset))
                codeAfter = editor.document.getText(TextRange(inlineChatIntents[editor].endOffset, editor.document.textLength))
            }catch (e: Exception){
                LogUtil.error("InlineChatService sendMessage  error", e)
            }finally {
                //fullLines是放在主线程里面拿，偶发没拿到流式调用开始执行，因此需要等待参数正确拿到
                semaphore.release()
            }
        }
        semaphore.acquire()
        val inlineChatId = UUID.randomUUID().toString()
        inlineChatIntents[editor].uuid = inlineChatId
        //用流式生成代码
        val streamModelGenCodeFlow = getStreamModelGenCode(question,fullLines, editor, language, inlineChatId,codeAfter,codeBefore)
        val oldLines: MutableList<String> = if (!TextUtils.isEmpty(fullLines)){
            fullLines.split("\n").toMutableList()
        }else{
            emptyList<String>().toMutableList()
        }
        if (streamModelGenCodeFlow != null) {
            //将fullLines转化为oldLines: List<String>
            StreamDiffRequest(
                    oldLines,
                    StreamDiffHandler(project, editor, startLine, endLine, inlineChatIntents[editor], FastApplyType.INLINE_CHAT_APPLY)
            ).processStream(streamModelGenCodeFlow)
        }else{
            //生成失败
            failed(editor)
        }
    }

    private fun failed(editor: Editor, content: String = ""){
        closeInlineChat(editor)
        inlineChatIntents[editor].isProcessing = false
        AppConstant.ENABLE_SEND_SELECTED_CODE_MSG = true
        ApplicationManager.getApplication().invokeLater {
            if (content.isEmpty()) {
                NotificationUtils.notifyInlineChatFailed(editor.project!!)
            } else {
                NotificationUtils.notifyInlineChat(editor.project!!, content)
            }
        }
        Thread.sleep(100)
        ApplicationManager.getApplication().invokeLater {
            showInlineChat(
                editor.project!!,
                editor,
                inlineChatIntents[editor].clickType!!,
                inlineChatIntents[editor].questionStr
            )
        }
    }
}