package com.alipay.tsingyan.inline2.listener

import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.EditorMouseEvent
import com.intellij.openapi.editor.event.EditorMouseListener
import com.intellij.openapi.util.TextRange
import org.apache.commons.lang3.StringUtils

class InlineChatMouseListener(editor: Editor) : EditorMouseListener {
    @Volatile
    var hasSelectionWhenPressed: Boolean = false

    init {
        //关闭逻辑
//        val project = editor.project
//        if (project != null) {
//            editor.scrollingModel.addVisibleAreaListener { visibleAreaEvent: VisibleAreaEvent? ->
//                LogUtil.info("InlineChatMouseListener visibleAreaEvent editor " + editor.toString())
//                project.service<InlineChatService>().closeInlineChat()
//            }
//        }
    }

//    override fun mousePressed(editorMouseEvent: EditorMouseEvent) {
//        LogUtil.info("InlineChatMouseListener mousePressed")
//        val editor = editorMouseEvent.editor
//        if (editor.project == null) {
//            return
//        }
//        if (editor.selectionModel.hasSelection() && StringUtils.isNotBlank(editor.selectionModel.selectedText)) {
//            this.hasSelectionWhenPressed = true
//        } else {
//            this.hasSelectionWhenPressed = false
//        }
//    }

    override fun mouseReleased(editorMouseEvent: EditorMouseEvent) {
//        LogUtil.info("InlineChatMouseListener mouseReleased")
        val editor = editorMouseEvent.editor
        if (editor.project == null) {
            return
        }
        val inlineChatService = editor.project!!.service<InlineChatService>()
        if (!inlineChatService.isAllEditorProcessing()){
            inlineChatService.closeAllInlineChat()
        }
//        if (editor.selectionModel.hasSelection() && StringUtils.isNotBlank(
//                editor.selectionModel.selectedText
//            ) && !this.hasSelectionWhenPressed && !inlineChatService.isInlineChatShowing(editor) && !inlineChatService.isOptionViewShowing(editor)
//        ) {
//            val text = editor.document.getText(
//                TextRange(
//                    editor.selectionModel.selectionStart,
//                    editor.selectionModel.selectionEnd
//                )
//            )
//            val lineNumber = editor.document.getLineNumber(editor.selectionModel.selectionStart)
//            val text2 = editor.document.getText(
//                TextRange(
//                    editor.document.getLineStartOffset(lineNumber),
//                    editor.document.getLineEndOffset(lineNumber)
//                )
//            )
//            if (text.contains("\n") || text.trim { it <= ' ' } == text2.trim { it <= ' ' }) {
//
////                LogUtil.info("show codefuse inline chat icon")
//            }
//        }
    }
}