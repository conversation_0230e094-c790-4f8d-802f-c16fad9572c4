package com.alipay.tsingyan.inline2.compontent

import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.model.enums.FloatingToolBarActionTypeEnum
import com.alipay.tsingyan.model.enums.OsTypeEnum
import com.alipay.tsingyan.services.trace.CodeFuseTracerService
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.AppConstant.CHAT_ACTION_ID
import com.alipay.tsingyan.utils.AppConstant.EDIT_ACTION_ID
import com.alipay.tsingyan.utils.AppConstant.FLOATING_TOOL_BAR_CHAT_ACTION_TEXT
import com.alipay.tsingyan.utils.AppConstant.FLOATING_TOOL_BAR_EDIT_ACTION_TEXT
import com.alipay.tsingyan.utils.AppConstant.FLOATING_TOOL_BAR_TEST_ACTION_TEXT
import com.alipay.tsingyan.utils.AppConstant.TEST_ACTION_ID
import com.alipay.tsingyan.utils.CommonUtils
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.actionSystem.impl.ActionButtonWithText
import com.intellij.openapi.actionSystem.impl.PresentationFactory
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.keymap.KeymapUtil
import com.intellij.ui.JBColor
import com.intellij.ui.LightweightHint
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import java.awt.Dimension
import java.awt.FlowLayout
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.BorderFactory
import javax.swing.JPanel


class FloatingToolBar(editor: Editor, themeColor: ThemeColor)  {

    private val presentationFactory = PresentationFactory()

    private val DEFAULT_MINIMUM_BUTTON_SIZE: Dimension = JBUI.size(30, 20)

    var hint: LightweightHint? = null

    val tracerService = service<CodeFuseTracerService>()

    companion object {
        val PLACE_NAME = "EditorToolbar"
    }

    init {
        if (hint == null) {
            hint = createFloatingToolBarHint(editor,themeColor)
        }
    }


    private fun createHint(actionButtons: List<ActionButtonWithText?>, themeColor: ThemeColor): LightweightHint {
        val buttonPanel = JPanel(FlowLayout(0, 5, 0))
        buttonPanel.isOpaque = false
        buttonPanel.border = BorderFactory.createEmptyBorder(6, 4, 6, 4)
        buttonPanel.background = JBColor.PanelBackground
        val codeFuseLogo = JBLabel(CodeFuseIcons.INLINE_CHAT_LOGO)
        codeFuseLogo.border = BorderFactory.createEmptyBorder(1, 0, 0, 0)
        buttonPanel.add(codeFuseLogo)
        for (button in actionButtons) {
            if (button !=null) {
                buttonPanel.add(button)
            }
        }
        val closeLabel = JBLabel(themeColor.inlineCloseIcon)
        closeLabel.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                hint!!.hide()
            }
        })
        closeLabel.background = JBColor.PanelBackground
        buttonPanel.add(closeLabel)
        val component = JPanel()
        component.add(buttonPanel)
        val hint = LightweightHint(buttonPanel)
        hint.setCancelOnClickOutside(false)
        hint.setForceShowAsPopup(true)
        return hint
    }




    private fun createFloatingToolBarHint(editor: Editor, themeColor: ThemeColor): LightweightHint {
        val chatButton: ActionButtonWithText = createFloatingBarTextButton(CHAT_ACTION_ID, FLOATING_TOOL_BAR_CHAT_ACTION_TEXT)
        val testButton: ActionButtonWithText = createFloatingBarTextButton(TEST_ACTION_ID, FLOATING_TOOL_BAR_TEST_ACTION_TEXT)
        var editButton: ActionButtonWithText? = null
        if (AppConstant.INLINE_CHAT_ENABLE) {
            editButton = createFloatingBarTextButton(EDIT_ACTION_ID, FLOATING_TOOL_BAR_EDIT_ACTION_TEXT)
        }
        val hint: LightweightHint = createHint(listOf(chatButton, editButton,testButton),themeColor)
        addButtonHandler(chatButton, editor, hint, FloatingToolBarActionTypeEnum.CHAT)
        addButtonHandler(editButton, editor, hint,FloatingToolBarActionTypeEnum.EDIT)
        addButtonHandler(testButton, editor, hint,FloatingToolBarActionTypeEnum.TEST)
        return hint
    }

    private fun addButtonHandler(button: ActionButtonWithText?, editor: Editor, hint: LightweightHint, floatingToolBarActionTypeEnum: FloatingToolBarActionTypeEnum) {
        button?.addMouseListener(object : MouseAdapter() {
             override fun mouseReleased(e: MouseEvent) {
                hint.hide()
                val action = button.action
                val anActionEvent = AnActionEvent.createFromAnAction(action, e, ActionPlaces.EDITOR_TOOLBAR, DataManager.getInstance().getDataContext(editor.component))
                action.actionPerformed(anActionEvent)
                tracerService.submitFloatingToolBarClickEvent(editor.project!!, floatingToolBarActionTypeEnum)
            }
        })
    }

    private fun createFloatingBarTextButton(actionId: String, actionText: String): ActionButtonWithText {
        val action = ActionManager.getInstance().getAction(actionId)
        val shortcutText: String = getShortcutText(action)
        val presentation: Presentation = presentationFactory.getPresentation(action)
        val text =  if (shortcutText.isEmpty()) actionText else "$actionText ($shortcutText)"
        presentation.text = text
        return FloatingToolButton(action, presentation, PLACE_NAME, DEFAULT_MINIMUM_BUTTON_SIZE)
    }


    private fun getShortcutText(action: AnAction): String {
        val shortcuts = action.shortcutSet.shortcuts
        return if (shortcuts.isNotEmpty() && shortcuts[0] is KeyboardShortcut) {
            var shortcutText = KeymapUtil.getShortcutText(shortcuts[0])
            if (shortcutText.length >= 2) {
                if (CommonUtils.detectOsType() == OsTypeEnum.MAC) {
                    //mac环境
                    shortcutText = shortcutText.replace("⌘", "⌘+")
                }
            }
            shortcutText
        } else ""
    }




}






