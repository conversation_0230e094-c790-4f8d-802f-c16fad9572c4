package com.alipay.tsingyan.compontent

import java.awt.*
import javax.swing.BorderFactory
import javax.swing.JFrame
import javax.swing.JLabel
import javax.swing.SwingUtilities
import javax.swing.border.Border


class RoundedText(text: String?, private val cornerRadius: Int, private val borderL: Border,
                  private val sizeL: Dimension,
                  private val backgroundColor: Color, private val foregroundColor: Color) : JLabel(text) {

    init {
        preferredSize = sizeL
        font = Font(Font.SANS_SERIF, Font.ROMAN_BASELINE, 12)
        isOpaque = false
        foreground = foregroundColor
        border = borderL
        alignmentX = CENTER_ALIGNMENT
    }
    override fun paintComponent(g: Graphics) {
        val g2 = g.create() as Graphics2D

        // 设置抗锯齿
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        val width = width
        val height = height

        // 绘制圆角矩形背景
        g2.color = backgroundColor
        g2.fillRoundRect(0, 0, width, height, cornerRadius, cornerRadius)

        // 绘制文本
        super.paintComponent(g)
        g2.dispose()
    }
}

