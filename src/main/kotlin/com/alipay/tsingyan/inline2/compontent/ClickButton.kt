package com.alipay.tsingyan.compontent;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.geom.RoundRectangle2D;

public open class ClickButton(text: String, icon: Icon? = null,
                              private val cornerRadius: Int,
                              var hoverColor: Color? = null,
                              private val hoverTextColor : Color? = null, private val unHoverTextColor: Color? = null
): JButton(text, icon) {

    var hover = false
    init {
        isBorderPainted = false
        isFocusPainted = false
        isContentAreaFilled = false
        isOpaque = false
        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                hover = true
                if (hoverTextColor != null) {
                    foreground = hoverTextColor
                    repaint()
                }
            }

            override fun mouseExited(e: MouseEvent) {
                hover = false
                if (unHoverTextColor != null) {
                    foreground = unHoverTextColor
                    repaint()
                }
            }
        })
    }

    override fun paintComponent(g: Graphics) {
        val g2d = g.create() as Graphics2D
        val h = height
        val w = width

        g2d.composite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1F)
        val clip = g2d.clip

        val r2d = RoundRectangle2D.Float(0f, 0f, (w - 1).toFloat(), (h - 1).toFloat(), cornerRadius.toFloat(), cornerRadius.toFloat())
        g2d.clip = r2d

        val gp = GradientPaint(0.0F, 0.0F, background, 0.0F, h.toFloat(), background, true)
        g2d.paint = gp
        g2d.fillRect(0, 0, w, h)

        if (hover && isEnabled && hoverColor != null) {
            val r2d2 = RoundRectangle2D.Float(2f, 2f, (w - 4).toFloat(), (h - 5).toFloat(), cornerRadius.toFloat(), cornerRadius.toFloat())
            g2d.clip = r2d2
            val gp2 = GradientPaint(0.0F, 0.0F, hoverColor, 0.0F, h.toFloat(), hoverColor, true)
            g2d.paint = gp2
            g2d.fillRect(2, 2, w - 4, h - 5)
        }

        g2d.clip = clip

        g2d.dispose()
        super.paintComponent(g)
    }

}
