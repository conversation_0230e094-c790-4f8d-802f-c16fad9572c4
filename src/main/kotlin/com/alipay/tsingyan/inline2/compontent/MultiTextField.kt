package com.alipay.tsingyan.compontent

import com.alipay.tsingyan.inline2.base.ColorConstant
import com.alipay.tsingyan.util.IntentionVO
import com.intellij.openapi.diagnostic.Logger
import java.awt.*
import javax.swing.JTextArea
import javax.swing.SwingUtilities
import javax.swing.event.CaretEvent
import javax.swing.event.CaretListener
import kotlin.math.max
import kotlin.math.min


class MultiTextField(var hint: String, private var suffixIntention: IntentionVO? = null) : JTextArea() {
    private val LOGGER: Logger = Logger.getInstance(MultiTextField::class.java)
    private var placeHolderForSuffix: String = if (suffixIntention == null) "" else suffixIntention!!.placeHolder
    public var isCodeSetText = false

    init {
        /**
         * 监听光标位置，不允许用户移动光标到placeholder之间的位置，指定为Move的动作
         */
        addCaretListener(object : CaretListener {
            var previousDot = 0
            override fun caretUpdate(e: CaretEvent?) {
                val dot = e!!.dot
                if (dot != previousDot) {
                    if (dot < placeHolderForSuffix.length) {
                        SwingUtilities.invokeLater {
                            caretPosition = placeHolderForSuffix.length
                        }
                    }
                }
                previousDot = dot
            }
        })
    }

    override fun setCaretPosition(position: Int) {
        /**
         * 定位的时候需要修改光标位置
         */
        super.setCaretPosition(min(text.length, max(position, placeHolderForSuffix.length)))
    }

    override fun paint(g: Graphics) {
        super.paint(g)
        val ins: Insets = insets
        val fm: FontMetrics = g.fontMetrics
        var startX = insets.left + 2
        val startY = fm.getAscent() / 2 + 11
        if (suffixIntention != null) {
            // 写一个带圆角背景的文字
            val adjustWidth = suffixIntention!!.fixedWidth
            val g2d = g as Graphics2D
            g2d.color = Color(61, 100, 195, 80)
            g2d.fillRoundRect(startX - 2, fm.getAscent() / 2 - 2, adjustWidth, 20, 5, 5)
            g2d.color = ColorConstant.PANEL_BUTTON_ENABLE_COLOR
            g2d.font = Font(Font.SANS_SERIF, Font.PLAIN, 12)
            g2d.drawString(suffixIntention!!.tag, startX + 5, fm.getAscent() / 2 + 12)
            startX += (adjustWidth + 5)
        }
        if (getRealText().isEmpty()) {
            val h = height
            (g as Graphics2D).setRenderingHint(
                RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_ON
            )
            val c0 = background.rgb
            val c1 = foreground.rgb
            val m = -0x1010102
            val c2 = (c0 and m ushr 1) + (c1 and m ushr 1)
            g.setColor(Color(c2, true))
            g.drawString(hint, startX, startY)
        }
    }

    override fun getText(): String {
        return super.getText()
    }

//    override fun setText(t: String?) {
//        super.setText(t)
//    }

    fun getRealText(): String {
        val currentText: String = super.getText()
        val regex = Regex("""^\s+""")
        val matchResult = regex.find(super.getText())
        val numSpaces = matchResult?.value?.length ?: 0
        return currentText.replaceFirst(
            if (numSpaces > placeHolderForSuffix.length) placeHolderForSuffix else (matchResult?.value ?: ""), ""
        )
    }

    fun setRealText(t: String) {
        isCodeSetText = true
        super.setText(placeHolderForSuffix + t)
    }

    fun setSuffixIntention(suffix: IntentionVO?, text: String = "") {
        SwingUtilities.invokeLater {
            this.suffixIntention = suffix
            if (suffix == null) {
                placeHolderForSuffix = ""
            } else {
                placeHolderForSuffix = suffix.placeHolder
            }
            setRealText(text)
            repaint()
        }
    }

    fun getSuffixIntention(): IntentionVO? {
        return this.suffixIntention
    }

    fun getSuffix(): String {
        return this.placeHolderForSuffix
    }
}