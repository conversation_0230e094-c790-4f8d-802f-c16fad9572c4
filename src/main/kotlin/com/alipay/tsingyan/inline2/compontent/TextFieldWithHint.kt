package com.alipay.tsingyan.inline2.compontent

import com.intellij.ui.components.JBTextField
import com.intellij.util.BooleanFunction

class TextFieldWithHint(var hintTextStr: String) : JBTextField() {
    companion object {
        fun setHintText(textField: JBTextField, hintStr: String) {
            textField.emptyText.text = hintStr
            textField.putClientProperty(
                "StatusVisibleFunction",
                BooleanFunction<JBTextField> { _ -> textField.text.isEmpty() })
        }
    }

    init {
        TextFieldWithHint.setHintText(this, hintTextStr)
    }
}