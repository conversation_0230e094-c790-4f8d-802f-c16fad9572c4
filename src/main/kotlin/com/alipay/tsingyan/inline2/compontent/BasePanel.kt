package com.alipay.tsingyan.compontent

import java.awt.Component
import javax.swing.JPanel

open class BasePanel: JPanel() {
    private val baseComponents: MutableList<Component> = mutableListOf()
    override fun add(comp: Component?): Component {
        if (comp != null) {
            baseComponents.add(comp)
        }
        return super.add(comp)
    }

    override fun remove(comp: Component?) {
        if (comp != null) {
            baseComponents.remove(comp)
        }
        super.remove(comp)
    }

    fun getBaseComponents(): List<Component> {
        return baseComponents.toList()
    }
}