package com.alipay.tsingyan.compontent

import java.awt.*
import java.awt.geom.RoundRectangle2D
import javax.swing.JPopupMenu


class PopMenu(): JPopupMenu() {
    override fun paintComponent(g: Graphics) {
        // 画圆角矩形背景
        val width = width
        val height = height
        val g2 = g.create() as Graphics2D
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        g2.color = Color.BLACK
        g2.fillRoundRect(0, 0, width - 1, height - 1, 15, 15)
        g2.dispose()
    }
}