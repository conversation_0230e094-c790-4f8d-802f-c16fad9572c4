package com.alipay.tsingyan.compontent

import java.awt.Color
import java.awt.Dimension
import java.awt.Rectangle
import javax.swing.JComponent
import javax.swing.JScrollBar
import javax.swing.plaf.basic.BasicScrollBarUI

class ScrollBarUI: BasicScrollBarUI() {
    override fun configureScrollBarColors() {
        if (this.scrollbar.orientation == JScrollBar.VERTICAL) {
            trackColor = Color.BLACK
            setThumbBounds(0,0,3,10)
        }
        if (this.scrollbar.orientation == JScrollBar.HORIZONTAL) {
            trackColor = Color.BLACK
            setThumbBounds(0,0,10,3)
        }
    }

    override fun getThumbBounds(): Rectangle {
        return super.getThumbBounds()
    }

    override fun getPreferredSize(c: JComponent): Dimension {
        if (this.scrollbar.orientation == JScrollBar.VERTICAL) {
            c.preferredSize = Dimension(6,0)
        }
        if (this.scrollbar.orientation == JScrollBar.HORIZONTAL) {
            c.preferredSize = Dimension(0, 6)
        }
        return super.getPreferredSize(c)
    }
}