package com.alipay.tsingyan.compontent;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.awt.geom.RoundRectangle2D;

public class BasicButton(text: String, private val cornerRadius: Int,
                         private val unFocusColor: Color? = null,
                         private val focusColor: Color? = null,
                         private val backgroundOver: Color? = null,
        private val isBorderPaintOver: Boolean = true
        ): JButton(text) {
    companion object {
//        val BUTTON_COLOR1 = Color(125, 161, 237)
        val BUTTON_COLOR1 = Color(125, 125, 125)
//        val BUTTON_COLOR2 = Color(91, 118, 173)
        val BUTTON_COLOR2 = Color(90, 90, 90)
        val BUTTON_BAK_COLOR1_1 = Color(108, 135, 210, 179)
//        val BUTTON_BAK_COLOR1_2 = Color(108, 135, 210, 255)
        val BUTTON_BAK_COLOR2_1 = Color(180, 230, 250, 179)
//        val BUTTON_BAK_COLOR2_2 = Color(180, 230, 250, 255)
//        var BUTTON_BAK_COLOR1_2 = Color(108, 108, 108, 150)
//        var BUTTON_BAK_COLOR2_2 = Color(180, 180, 180, 150)
//        val BUTTON_FOREGROUND_COLOR = Color.WHITE
        var UNFOCUS_COLOR = Color(108, 108, 108, 150)
        var FOCUS_COLOR = Color(180, 180, 180, 150)
        var BUTTON_FOREGROUND_COLOR = Color.WHITE
    }


    var hover = false

    public fun changeStyle(unFocusColor: Color?, focusColor: Color?, backgroundColor: Color?) {
        UNFOCUS_COLOR = unFocusColor ?: Color(108, 108, 108, 150)
        FOCUS_COLOR = focusColor ?: Color(180, 180, 180, 150)
        background = backgroundColor ?: Color(0,0,0,0)
        repaint()
    }

    init {
        if (unFocusColor != null) UNFOCUS_COLOR = unFocusColor
        if (focusColor != null) FOCUS_COLOR = focusColor
        isBorderPainted = false
        isFocusPainted = false
        isContentAreaFilled = false
        isOpaque = false
        foreground = BUTTON_FOREGROUND_COLOR
        background = backgroundOver ?: Color(0,0,0,0)
        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                hover = true
                repaint()
            }

            override fun mouseExited(e: MouseEvent) {
                hover = false
                repaint()
            }
        })
    }

    override fun paintComponent(g: Graphics) {
        val g2d = g.create() as Graphics2D
        val h = height
        val w = width

        var tran = 1F
        if (!hover) {
            tran = 0.7F
        }

        if (!isEnabled) {
            tran = 0.5F
        }

        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        val p1: Paint
        val p2: Paint
        if (model.isPressed) {
            p1 = GradientPaint(0.0F, 0.0F, Color(0, 0, 0), 0.0F, (h - 1).toFloat(), Color(100, 100, 100))
            p2 = GradientPaint(0.0F, 1.0F, Color(0, 0, 0, 50), 0.0F, (h - 3).toFloat(), Color(255, 255, 255, 100))
        } else {
            p1 = GradientPaint(0.0F, 0.0F, Color(100, 100, 100), 0.0F, (h - 1).toFloat(), Color(0, 0, 0))
            p2 = GradientPaint(0.0F, 1.0F, Color(255, 255, 255, 100), 0.0F, (h - 3).toFloat(), Color(0, 0, 0, 50))
        }

        g2d.composite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, tran)
        val clip = g2d.clip

        val r2d = RoundRectangle2D.Float(0f, 0f, (w - 1).toFloat(), (h - 1).toFloat(), cornerRadius.toFloat(), cornerRadius.toFloat())
        g2d.clip = r2d

        val gp = GradientPaint(0.0F, 0.0F, background, 0.0F, h.toFloat(), background, true)
        g2d.paint = gp
        g2d.fillRect(0, 0, w, h)

        if (hover && isEnabled) {
            val r2d2 = RoundRectangle2D.Float(2f, 2f, (w - 4).toFloat(), (h - 5).toFloat(), (cornerRadius / 2).toFloat(), (cornerRadius / 2).toFloat())
            g2d.clip = r2d2
            val gp2 = GradientPaint(0.0F, 0.0F, FOCUS_COLOR, 0.0F, h.toFloat(), UNFOCUS_COLOR, true)
            g2d.paint = gp2
            g2d.fillRect(2, 2, w - 4, h - 5)
        }

        g2d.clip = clip

        if (isBorderPaintOver) {
            g2d.paint = p1
            g2d.drawRoundRect(0, 0, w - 1, h - 1, cornerRadius, cornerRadius)
            g2d.paint = p2
            g2d.drawRoundRect(1, 1, w - 3, h - 3, cornerRadius - 2, cornerRadius - 2)
        }

        g2d.dispose()
        super.paintComponent(g)
    }

}
