package com.alipay.tsingyan.compontent

import com.intellij.ui.plaf.beg.BegResources
import java.awt.Dimension
import java.awt.Graphics
import java.awt.Graphics2D
import java.awt.Insets
import java.awt.RenderingHints


class RoundedPanel(private val cornerRadius: Int, private val padding: Insets) : BasePanel() {
    override fun getPreferredSize(): Dimension {
        val size = super.getPreferredSize()
        size.width += (padding.left + padding.right)
        size.height += (padding.top + padding.bottom)
        return size
    }
    override fun paintComponent(g: Graphics) {
//        val g2d = g.create() as Graphics2D
//        val shape = RoundRectangle2D.Double(
//                0.0, 0.0, width.toDouble(), height.toDouble(),
//                cornerRadius.toDouble(), cornerRadius.toDouble()
//        )
//        g2d.clip = shape // 设置剪辑区域为圆角矩形
//        super.paintComponent(g2d)
//        g2d.dispose()
        // 获取组件的宽高
        // 获取组件的宽高
        val width = width
        val height = height

        // 绘制背景圆角矩形

        // 绘制背景圆角矩形
        val g2d = g.create() as Graphics2D
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
        g2d.color = background
        g2d.fillRoundRect(padding.left, padding.top, width - (padding.right + padding.left), height - (padding.top + padding.bottom), cornerRadius, cornerRadius)
        g2d.dispose()
    }
}