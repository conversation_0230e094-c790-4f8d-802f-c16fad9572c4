package com.alipay.tsingyan.compontent

import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.alipay.tsingyan.util.IntentionVO
import com.intellij.ui.components.JBLabel
import java.awt.*
import javax.swing.JTextArea


class InlineChatMultiTextField(
    var hint: String,
    private var suffixIntention: IntentionVO? = null,
    var sendButton: JBLabel? = null,
    themeColor: ThemeColor
) : JTextArea() {
    val hintColor = themeColor.hitColor
    val sendIcon = themeColor.inlineSendIcon
    val notSendIcon = themeColor.inlineNotSendIcon

    override fun paint(g: Graphics) {
        super.paint(g)
        if (getText().isEmpty()) {
            (g as Graphics2D).setRenderingHint(
                RenderingHints.KEY_TEXT_ANTIALIASING,
                RenderingHints.VALUE_TEXT_ANTIALIAS_ON
            )
            g.setColor(hintColor)
            g.drawString(hint, 0, 13)
            if (sendButton != null) {
                sendButton!!.icon = notSendIcon
            }
        } else {
            if (sendButton != null) {
                sendButton!!.icon = sendIcon
            }

        }
    }

    override fun getText(): String {
        return super.getText()
    }

    override fun setText(t: String?) {
        super.setText(t)
    }
}