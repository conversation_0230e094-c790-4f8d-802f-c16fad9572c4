package com.alipay.tsingyan.inline2.bean

import com.alipay.tsingyan.model.request.AbstractRequestBean

class InlineChatStreamReqBean() : AbstractRequestBean() {
    var question: String? = null
    var questionCode: String? = null
    var language: String? = null
    var optionLanguage: String? = null
    var fileUrl: String? = null
    var repo: String? = null
    var inlineChatId: String? = null
    var intention: String = StreamEnum.CODE_GENERATE.name
    var codeAfter:String? = null
    var codeBefore:String? = null
}