package com.alipay.tsingyan.inline2.bean

import com.alipay.tsingyan.model.request.AbstractRequestBean

class InlineChatIntentRequestBean(): AbstractRequestBean() {
    var question: String? = null
    var questionCode: String? = null
    var intentionList: List<String>? = listOf(InlineChatIntention.CHAT.name, InlineChatIntention.CODE_GENERATE.name)
    var language: String? = null
    var optionLanguage: String? = null
    var inlineChatId:String? = null
}