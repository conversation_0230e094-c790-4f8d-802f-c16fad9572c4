package com.alipay.tsingyan.inline2

import com.alipay.tsingyan.inline2.theme.ThemeColor
import com.intellij.ui.JBColor
import com.intellij.ui.RoundedLineBorder
import javax.swing.*

/**
 * inlineChat 选项窗口Panel
 * <AUTHOR>
 * @date 2024-10-28 20:39:42
 *
 * Inlay 的按钮形式
 */
class ButtonInlayPanel(val themeColor: ThemeColor) : JPanel() {
    private val preferredHeight = 30
    private val optComponentList: MutableList<JComponent?> = mutableListOf()

    init {
        isOpaque = false
        updateLayout()
    }

    /**
     *  Inlay Button 按钮
     */
    class InlayButtonComponent(
        renderText: String,
        themeColor: ThemeColor,
        height: Int,
        action: (() -> Unit)?,
        aggravate: Boolean = false,
    ) : JButton(renderText) {
        init {
            isOpaque = false
            isFocusPainted = false
            isContentAreaFilled = false // 禁用默认背景填充
            foreground = themeColor.optionFontColor
            border = RoundedLineBorder(themeColor.optionFontColor, 15)
            maximumSize.height = height - 10
            if (aggravate) {
                background = JBColor.BLUE
            }
            addActionListener { action?.invoke() }
        }
    }

    /**
     *  添加圆角按钮选项
     */
    fun addRoundButtonOption(str: String, aggravate: Boolean = false, action: (() -> Unit)?): ButtonInlayPanel {
        optComponentList.add(InlayButtonComponent(str, themeColor, preferredHeight, action, aggravate))
        updateLayout()
        return this
    }

    fun updateLayout() {
        removeAll()
        layout = BoxLayout(this, BoxLayout.X_AXIS)
        border = BorderFactory.createEmptyBorder(0, 0, 0, 10)
        isOpaque = false
        //绘制选项
        for (index in optComponentList.indices) {
            add(Box.createHorizontalStrut(10)) // 按钮间距
            add(optComponentList[index])
        }
        revalidate()
    }
}
