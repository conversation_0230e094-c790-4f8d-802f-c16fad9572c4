package com.alipay.tsingyan.inline2.theme

import com.alipay.tsingyan.model.enums.ThemeEnum
import com.alipay.tsingyan.talk.UIThemeListener
import com.alipay.tsingyan.view.statusbar.CodeFuseIcons
import java.awt.Color
import javax.swing.Icon

open class ThemeColor {
    open val windowBgColor: Color = Color(0x2d3034)
    open val windowBorderColor: Color = Color(0x5b5b5b)
    open val chatBackgroundColor: Color = Color(0x101419)
    open val chatBorderColorFocused: Color = Color(0x5566D1)
    open val chatBorderColorNotFocused: Color = Color(0x333D73)
    open val hitColor: Color = Color(255, 255, 255, 0x59)
    open val caretColor: Color = Color.WHITE
    open val inlineCloseIcon:Icon = CodeFuseIcons.INLINE_CLOSE_ICON
    open val inlineNotSendIcon:Icon = CodeFuseIcons.INLINE_NOT_SEND_ICON
    open val inlineSendIcon:Icon = CodeFuseIcons.INLINE_SEND_ICON
    open val optionFontColor:Color = Color(0xff, 0xff, 0xff, 0xa6)
}

class LightThemeColor:ThemeColor(){
    override val windowBgColor: Color = Color(0xfafafa)
    override val windowBorderColor: Color = Color(0xcacccc)
    override val chatBackgroundColor: Color = Color(0xffffff)
    override val chatBorderColorFocused: Color = Color(0x5566D1)
    override val chatBorderColorNotFocused: Color = Color(0x333D73)
    override val hitColor: Color = Color(59, 59, 59, 0x80)
    override val caretColor: Color = Color.BLACK
    override val inlineCloseIcon:Icon = CodeFuseIcons.INLINE_CLOSE_LIGHT_ICON
    override val inlineNotSendIcon:Icon = CodeFuseIcons.INLINE_NOT_SEND_LIGHT_ICON
    override val inlineSendIcon:Icon = CodeFuseIcons.INLINE_SEND_LIGHT_ICON
    override val optionFontColor:Color = Color(0x3b, 0x3b, 0x3b, 0xd9)
}

object ColorManager {
    fun getColor(theme: ThemeEnum): ThemeColor {
        return when (theme) {
            ThemeEnum.LIGHT -> LightThemeColor()
            ThemeEnum.DARK -> ThemeColor()
        }
    }

    fun getCurrBorderColor() : Color{
        return getColor(UIThemeListener.getTheme()).windowBorderColor
    }
}