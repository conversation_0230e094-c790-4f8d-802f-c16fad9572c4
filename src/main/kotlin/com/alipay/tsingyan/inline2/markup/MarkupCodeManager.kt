package com.alipay.tsingyan.inline2.markup

import com.alipay.tsingyan.inline2.stream.CodeLineService
import com.alipay.tsingyan.inline2.BaseIntent
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor



/**
 * 用来处理Markup的代码意图
 * <AUTHOR>
 * @date 2024-10-21 16:46:17
 */
@Service
class MarkupCodeManager : Disposable {

    override fun dispose() {
    }

    /**
     * 清除标记代码,ESC键触发
     */
    fun cleanMarkupCode2(intent: BaseIntent, editor: Editor) {
       //文件关闭情况下editor.isDisposed 但是要对流式处理还原 因此不能直接return 需要判断是不是流式取消状态
        if (editor.isDisposed&&!intent.isCancel) {
            return
        }
        //去除所有高亮
        ApplicationManager.getApplication().invokeLater {
            editor.markupModel.removeAllHighlighters()

            WriteCommandAction.runWriteCommandAction(editor.project) {
                try {
                    if (intent.lineEndOffset >= 0&&intent.newCodeEndLine>0&&intent.newCodeEndLine<editor.document.lineCount) {
                        //取消用户的选中代码状态
                        editor.selectionModel.removeSelection()
                        intent.newCodeEndOffSet=editor.document.getLineEndOffset(intent.newCodeEndLine)
                        //未选中代码  插入时都加了一个换行符 因此撤销操作时需要加上
                        if (intent.emptySelected){
                            intent.newCodeEndOffSet++
                        }
                        if (intent.newCodeStartOffSet >= editor.document.textLength) {
                            intent.newCodeStartOffSet = editor.document.textLength
                        }
                        if (intent.newCodeEndOffSet >= editor.document.textLength) {
                            intent.newCodeEndOffSet = editor.document.textLength
                        }
                        val codeLineService: CodeLineService = service<CodeLineService>()
                        LogUtil.info("esc 撤销操作 ${intent.newCodeStartOffSet} ${intent.newCodeEndOffSet}" +
                                " ${intent.startOffset} ${intent.startLine} ${intent.newCodeEndLine}", false)
                        //文档最末尾处空行处理
                        if (intent.selectEnd&&editor.document.textLength==intent.newCodeEndOffSet+1){
                            intent.newCodeEndOffSet++
                        }
                        //删除流式输出diff后代码
                        codeLineService.deleteLineByOffset(editor, intent.newCodeStartOffSet, intent.newCodeEndOffSet,intent.isCancel)
                        //将旧代码写入 如果是中断生成的状态，旧代码是经历过diff后的
                        val oldCode = if (intent.isCancel) {
                            intent.afterStreamOldCode.removeSuffix("\n")
                        }else{
                            intent.selectedCode
                        }
                        codeLineService.addLineByOffset(editor,oldCode, intent.startOffset,intent.isCancel,null,true)
                        intent.isMarkup = false
                    }
                }catch (e:Exception){
                    LogUtil.info("cleanMarkupCode2 error",e)
                }

            }
        }
    }
}