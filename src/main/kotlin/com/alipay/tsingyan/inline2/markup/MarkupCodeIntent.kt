//package com.alipay.tsingyan.inline2.markup
//
//import com.intellij.openapi.application.ApplicationManager
//import com.intellij.openapi.fileEditor.FileEditorManager
//import com.intellij.openapi.fileEditor.TextEditor
//import com.intellij.openapi.project.Project
//import com.intellij.openapi.util.TextRange
//
//
///**
// * 用来处理Markup的代码意图
// * <AUTHOR>
// * @date 2024-10-21 16:46:17
// */
//class MarkupCodeIntent(private val project: Project) {
//    var startOffset = -1
//    var endOffset = -1
//
//    var startLine = -1
//    var endLine = -1
//
//    var lineStartOffset = -1
//    var lineEndOffset = -1
//
//    var selectedCode = ""
//
//    var isMarkup = false
//
//    var newCodeStartOffSet = -1
//    var newCodeEndOffSet = -1
//    var newCode = ""
//
//    fun getMarkupCode(code: String): String {
//        return selectedCode
//    }
//
//    fun resetSelectedCode(): String {
//        var result = ""
//        ApplicationManager.getApplication().runReadAction {
//            val selectedEditor = FileEditorManager.getInstance(project).selectedEditor
//            if (selectedEditor is TextEditor) {
//                val editor = selectedEditor.editor
//                val document = editor.document
//                val selectionModel = editor.selectionModel
//
//                startOffset = selectionModel.selectionStart
//                endOffset = selectionModel.selectionEnd
//
//                startLine = document.getLineNumber(startOffset)
//                endLine = document.getLineNumber(endOffset)
//
//                lineStartOffset = document.getLineStartOffset(startLine)
//                lineEndOffset = document.getLineEndOffset(endLine)
//                val text = document.getText(TextRange(lineStartOffset, lineEndOffset))
//
//                selectedCode = text
//                result = text
//
//                newCodeStartOffSet = -1
//                newCodeEndOffSet = -1
//                newCode = ""
//                isMarkup = false
//            }
//        }
//        return result
//    }
//}
//
//
//
