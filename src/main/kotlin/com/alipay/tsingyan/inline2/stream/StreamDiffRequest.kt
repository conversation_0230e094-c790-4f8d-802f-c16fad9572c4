package com.alipay.tsingyan.inline2.stream

import com.alibaba.fastjson.JSON
import com.alipay.tsingyan.inline2.bean.StreamResBean
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.onDebug
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.contentEqualsNew
import com.alipay.tsingyan.utils.removeTrailingEmptyLine
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.commons.text.similarity.LevenshteinDistance
import org.apache.http.util.TextUtils

/**
 * stream diff算法
 * @author: jianzhi
 * @date: 2024-12-16 17:39:21
 */
class StreamDiffRequest(oldLines: MutableList<String>, diffHandler: StreamDiffHandler) : CodeLineListener{

    val oldLines = oldLines
    var diffHandler = diffHandler


    data class DiffLine(val type: DiffLineType, val line: String)
    enum class DiffLineType { OLD, NEW, SAME }

    var seenIndentationMistake = false
    // 假设你有一个 Flow<StreamResBean>
    override fun processStream(flow: Flow<StreamResBean>) {
            diffHandler.onStart()
            runBlocking {
                launch {
                    flow.collectIndexed { index, streamResBean ->
                        if (!streamResBean.content.isNullOrBlank()){
                            LogUtil.info("processStream Received: ${JSON.toJSONString(streamResBean)}")
                        }
                        if(diffHandler.inlineChatIntent.isFailed){
                            this.cancel()
                            return@collectIndexed
                        }
                        //失败不走End方法
                        if (diffHandler.inlineChatIntent.isCancel) {
                            LogUtil.info("用户取消生成，流式终止")
                            diffHandler.onEnd()
                            this.cancel()
                            return@collectIndexed
                        }

                        if (AppConstant.STREAM_ERROR.contentEqualsNew(streamResBean.errorMsg)){
                            LogUtil.info("生成失败，流式终止")
                            diffHandler.inlineChatIntent.isCancel = true
                            diffHandler.inlineChatIntent.isFailed = true
                            diffHandler.onFailed()
                            this.cancel()
                            return@collectIndexed
                        }
                        //使用stream函数处理oldLines
                        //每一行的内容是streamResBean.content
                        //将streamResBean.content于oldLines中进行对比，然后输出一组队列 OLD, NEW, SAME ，分别对应操作删除，增加和不做任何事情。
                        //通过操作输出的数组行为，可以把oldLines替换为streamResBean.content的内容，并且可以将新增和相同标记出来。
                        if (!AppConstant.STREAM_END.contentEqualsNew(streamResBean.errorMsg) && streamResBean.content != null) {
                            //streamResBean.content!!去除掉末尾\n
                            if (streamResBean.content!!.endsWith("\n")) {
                                streamResBean.content = streamResBean.content!!.substring(0, streamResBean.content!!.length - 1)
                            }
                            val newLinesFlow = flowOf(streamResBean.content!!) // 你需要定义 content 提供内容的方式
                            streamDiff(oldLines, newLinesFlow).collectLatest { diffLine ->
                                when (diffLine.type) {
                                    DiffLineType.OLD -> {
                                        diffHandler.onOld(diffLine.line)
                                    }

                                    DiffLineType.NEW -> {
                                        diffHandler.onNew(diffLine.line)
                                    }

                                    DiffLineType.SAME -> {
                                        diffHandler.onSame(diffLine.line)
                                    }
                                }
                            }
                        }
                        //流式已经执行完了，但是发现oldLines还有内容，说明流式已经结束了，直接删除没有匹配的内容
                        if (AppConstant.STREAM_END.contentEqualsNew(streamResBean.errorMsg) && oldLines.size > 0) {
                            for (i in 0 until oldLines.size) {
//                                LogUtil.info("Remove: ${oldLines.get(i)}")
                                diffHandler.onOld(oldLines.get(i))
                            }
                        }
                    }
                }
            }
        //取消操作上面会执行一次 end方法，不需要重复执行
        if (!diffHandler.inlineChatIntent.isCancel) {
            diffHandler.onEnd()
        }
    }

    fun streamDiff(oldLines: MutableList<String>, newLines: Flow<String>): Flow<DiffLine> = flow {
        newLines.collectIndexed { index, newLineResult ->
//            LogUtil.info("streamDiff 1-1 newLineResult: ${JSON.toJSONString(newLineResult)}")
//            LogUtil.info("streamDiff 1-2 oldLinesCopy: ${JSON.toJSONString(oldLinesCopy)}")
            // 如果oldLines为空，直接输出newLineResult
            if (oldLines.size == 0){
                emit(DiffLine(DiffLineType.NEW, newLineResult))
            } else {
                // 如果oldLines不为空，进行匹配
                val matchResult = matchLine(newLineResult, oldLines, seenIndentationMistake)
                val matchIndex = matchResult.matchIndex
                val isPerfectMatch = matchResult.isPerfectMatch
                val newLine = matchResult.newLine
//            LogUtil.info("streamDiff 1 matchResult: ${JSON.toJSONString(matchResult)}")
                if (!seenIndentationMistake && newLineResult != newLine) {
                    seenIndentationMistake = true
                }

                val isNewLine = matchIndex == -1
                val type: DiffLineType
                if (isNewLine) {
                    type = DiffLineType.NEW
                } else {
                    // 删除所有在匹配行之前的旧行
                    for (i in 0 until matchIndex) {
                        emit(DiffLine(DiffLineType.OLD, oldLines.removeAt(0)))
                    }
                    type = if (isPerfectMatch) DiffLineType.SAME else DiffLineType.OLD
                }

                when (type) {
                    DiffLineType.NEW -> {
                        emit(DiffLine(type, newLine))
                    }

                    DiffLineType.SAME -> {
                        emit(DiffLine(type, oldLines.removeAt(0)))
                    }

                    DiffLineType.OLD -> {
                        emit(DiffLine(type, oldLines.removeAt(0)))
                        //如果 oldLines 中的下一行与 newLine 不匹配，则添加新行。
                        if (oldLines.getOrNull(0) != newLine) {
                            emit(DiffLine(DiffLineType.NEW, newLine))
                        } else {
                            //如果 oldLines 中的下一行与 newLine 匹配，直接就再返回一个Same
                            emit(DiffLine(DiffLineType.SAME, oldLines.removeAt(0)))
                        }
                    }
                }
//                LogUtil.info("streamDiff 2-1 newLineResult: ${JSON.toJSONString(oldLines)}")
            }

        }
    }


    fun linesMatch(lineA: String, lineB: String, linesBetween: Int = 0): Boolean {
        // Require a perfect (without padding) match for these lines
        // Otherwise they are edit distance 1 from empty lines and other single char lines (e.g. each other)
        // 检查 lineA 是否包含特殊字符，如果包含，则要求 lineA 和 lineB 完全精确匹配。
        if (listOf("}", "*", "});", "})").contains(lineA.trim())) {
            return lineA.trim() == lineB.trim()
        }

        //如果 lineA 不包含特殊字符，计算 lineA 和 lineB 之间的 Levenshtein 距离。
        val levenshteinDistance = LevenshteinDistance.getDefaultInstance()
        val d = levenshteinDistance.apply(lineA, lineB)

        //根据 Levenshtein 距离和行数差（linesBetween）判断是否匹配。
        return (
                // Should be more unlikely for lines to fuzzy match if they are further away
                // 如果距离小于0.48 - linesBetween *0.06，则返回true，否则返回false。
                (d.toDouble() / maxOf(lineA.length, lineB.length) <= maxOf(0.0,0.48 - linesBetween *0.06) ||
                        lineA.trim() == lineB.trim()) &&
                        lineA.trim().isNotEmpty()
                )
    }

    fun linesMatchPerfectly(lineA: String, lineB: String): Boolean {
        return !TextUtils.isEmpty(lineA) && lineA.contentEqualsNew(lineB)
    }

    val END_BRACKETS = listOf("}", "});", "})")
    fun matchLine(
        newLine: String,
        oldLines: MutableList<String>,
        permissiveAboutIndentation: Boolean = false
    ): MatchLineResult {
        // 空行匹配
        if (newLine.trim() == "" && oldLines.getOrNull(0)?.trim() == "") {
            return MatchLineResult(
                matchIndex = 0,
                isPerfectMatch = true,
                newLine = newLine.trim()
            )
        }
        // 末尾括号匹配, 判断是否为括号结束符号
        val isEndBracket = END_BRACKETS.contains(newLine.trim())

        for (i in oldLines.indices) {
            // Don't match end bracket lines if too far away
           // 如果是括号结束符号，且索引大于4，则不匹配，返回index -1
            if (i > 4 && isEndBracket) {
                return MatchLineResult(
                    matchIndex = -1,
                    isPerfectMatch = false,
                    newLine = newLine
                )
            }

            // 完全匹配上返回匹配index
            if (linesMatchPerfectly(newLine, oldLines[i])) {
                return MatchLineResult(
                    matchIndex = i,
                    isPerfectMatch = true,
                    newLine = newLine
                )
            }
            // 模糊匹配上返回匹配index
            if (linesMatch(newLine, oldLines[i], i)) {
                // This is a way to fix indentation, but only for sufficiently long lines to avoid matching whitespace or short lines
                // 通过判断新行和旧行的开头是否相同，以及是否允许不严格匹配缩进来确定是否精确匹配。
                if (newLine.trimStart() == oldLines[i].trimStart() &&
                    (permissiveAboutIndentation || newLine.trim().length > 8)
                ) {
                    return MatchLineResult(
                        matchIndex = i,
                        isPerfectMatch = true,
                        newLine = oldLines[i]
                    )
                }
                return MatchLineResult(
                    matchIndex = i,
                    isPerfectMatch = false,
                    newLine = newLine
                )
            }
        }

        return MatchLineResult(
            matchIndex = -1,
            isPerfectMatch = false,
            newLine = newLine
        )
    }

    // 输入为字符串列表，模仿流式输出
    fun steamByCodeList(newContent: MutableList<String>) {
        var index = 0
        newContent.forEach {
            val newLinesFlow = flowOf(it)
            runBlocking {
                streamDiff(oldLines, newLinesFlow).collectLatest { diffLine ->
                    index++
                    when (diffLine.type) {
                        DiffLineType.OLD -> {
                            diffHandler.onOld(diffLine.line)
                        }

                        DiffLineType.NEW -> {
                            diffHandler.onNew(diffLine.line)
                        }

                        DiffLineType.SAME -> {
                            diffHandler.onSame(diffLine.line)
                        }
                    }
                }

                //流式已经执行完了，但是发现oldLines还有内容，说明流式已经结束了，直接删除没有匹配的内容
                if (index == newContent.size && oldLines.size > 0) {
                    for (i in 0 until oldLines.size) {
                        LogUtil.info("Remove: ${oldLines.get(i)}")
                        diffHandler.onOld(oldLines.get(i))
                    }
                }
            }
        }
    }


    /**
     * 流式Diff时，自动修复之前Diff不正确的逻辑
     *
     */
    fun processStreamWithAutoFix(flow: Flow<StreamResBean>, callFun : (() -> Unit)? = null, refreshDiff : (() -> Unit)? = null) {
        diffHandler.onStart()
        runBlocking {
            launch {
                val newContentBuilder = StringBuilder()
                //流式Diff
                flow.collectIndexed { index, streamResBean ->
                    onDebug {
                        if (!streamResBean.content.isNullOrBlank()){
                            LogUtil.info("StreamWithAutoFix Received: ${JSON.toJSONString(streamResBean)}")
                        }
                    }
                    if(diffHandler.inlineChatIntent.isFailed){
                        this.cancel()
                        return@collectIndexed
                    }

                    //失败不走End方法
                    if (diffHandler.inlineChatIntent.isCancel) {
                        LogUtil.info("用户取消生成，流式终止")
                        diffHandler.onEnd(false)
                        this.cancel()
                        return@collectIndexed
                    }

                    if (AppConstant.STREAM_ERROR.contentEqualsNew(streamResBean.errorMsg)){
                        LogUtil.info("生成失败，流式终止")
                        diffHandler.inlineChatIntent.isCancel = true
                        diffHandler.inlineChatIntent.isFailed = true
                        diffHandler.onFailed(false)
                        this.cancel()
                        return@collectIndexed
                    }
                    //使用stream函数处理oldLines
                    //每一行的内容是streamResBean.content
                    //将streamResBean.content于oldLines中进行对比，然后输出一组队列 OLD, NEW, SAME ，分别对应操作删除，增加和不做任何事情。
                    //通过操作输出的数组行为，可以把oldLines替换为streamResBean.content的内容，并且可以将新增和相同标记出来。
                    if (!AppConstant.STREAM_END.contentEqualsNew(streamResBean.errorMsg) && streamResBean.content != null) {
                        //streamResBean.content!!去除掉末尾\n
                        if (streamResBean.content!!.endsWith("\n")) {
                            streamResBean.content = streamResBean.content!!.substring(0, streamResBean.content!!.length - 1)
                        }

                        newContentBuilder.appendLine(streamResBean.content)
                        // 生成新的
                        val newLinesFlow = flowOf(streamResBean.content!!) // 你需要定义 content 提供内容的方式
                        streamDiff(oldLines, newLinesFlow).collectLatest { diffLine ->
                            when (diffLine.type) {
                                DiffLineType.OLD -> {
                                    diffHandler.onOld(diffLine.line)
                                }

                                DiffLineType.NEW -> {
                                    diffHandler.onNew(diffLine.line)
                                }

                                DiffLineType.SAME -> {
                                    diffHandler.onSame(diffLine.line)
                                }
                            }
                        }
                    }

                    //流式已经执行完了，但是发现oldLines还有内容，说明流式已经结束了，直接删除没有匹配的内容
                    if (AppConstant.STREAM_END.contentEqualsNew(streamResBean.errorMsg) && oldLines.size > 0) {
                        for (i in 0 until oldLines.size) {
                            LogUtil.info("Remove: ${oldLines.get(i)}")
                            diffHandler.onOld(oldLines.get(i))
                        }
                    }

                }

                //流式Diff结束后，重置Diff面板
//                LogUtil.info("确认最终代码开始-----------------------》")
//                LogUtil.info("$newContentBuilder")
//                LogUtil.info("确认最终代码结束-----------------------》")
                // 删除最后一行代码
                newContentBuilder.removeTrailingEmptyLine()
                diffHandler.autoFix(newContentBuilder.toString(),callFun, refreshDiff)
            }
        }
        //取消操作上面会执行一次 end方法，不需要重复执行
        if (!diffHandler.inlineChatIntent.isCancel) {
            diffHandler.onEnd(false)
        }
    }

    /**
     * 无流式的文件变更
     */
    fun processNoStream(newCode : String, callFun : (() -> Unit)? = null, refreshDiff : (() -> Unit)? = null) {
        diffHandler.onStart()
        runBlocking {
            diffHandler.autoFix(newCode,callFun, refreshDiff)
        }
        diffHandler.onEnd(false)
    }
}



