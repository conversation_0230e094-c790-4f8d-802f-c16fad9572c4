package com.alipay.tsingyan.inline2.stream

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.util.DocumentUtil
import org.apache.http.util.TextUtils
import java.awt.Color
import kotlin.math.min

/**
 * 代码行处理
 */
@Service
class CodeLineService : Disposable {


    /**
     * 新增代码插入
     * @param editor 编辑器
     * @param text 插入的代码
     * @param line 插入的行数 从0开始
     * @param color 背景色
     */
    fun addLine(editor: Editor, text: String, lineNum: Int, color: Color) {
        if (editor.isDisposed) {
            return
        }
        ApplicationManager.getApplication().invokeLater {
            DocumentUtil.writeInRunUndoTransparentAction {
                // TODO 考虑加空行
                if (lineNum > editor.document.lineCount){
                    return@writeInRunUndoTransparentAction
                }
                if (lineNum == editor.document.lineCount) {
                    editor.document.insertString(editor.document.textLength, "\n")
                }
                val offset = editor.document.getLineStartOffset(lineNum)
                editor.document.insertString(offset, text+"\n")
                val textAttributes = TextAttributes()
                textAttributes.backgroundColor = color
                editor.markupModel.addLineHighlighter(lineNum, HighlighterLayer.ADDITIONAL_SYNTAX, textAttributes)
            }
        }
    }

    /**
     * 新增代码插入
     * @param editor 编辑器
     * @param text 插入的代码
     * @param startOffset 开始插入的光标位置
     * @param color 背景色
     * @param isSelect 是否选中插入的代码
     */
    fun addLineByOffset(editor: Editor, text: String, startOffset: Int, cancel: Boolean,color:Color?,isSelect:Boolean) {
        if (editor.isDisposed&&!cancel) {
            return
        }
        ApplicationManager.getApplication().invokeLater {
            DocumentUtil.writeInRunUndoTransparentAction {
                if (!TextUtils.isEmpty(text)) {
                    editor.document.insertString(startOffset, text)
                }
                if (isSelect&&!editor.isDisposed){
                    editor.selectionModel.setSelection(startOffset,startOffset+text.length)
                }
                if (color!=null){
                    val lineNumber = editor.document.getLineNumber(startOffset)
                    val textAttributes = TextAttributes()
                    textAttributes.backgroundColor = color
                    editor.markupModel.addRangeHighlighter(
                            startOffset,
                            editor.document.getLineEndOffset(lineNumber),
                            HighlighterLayer.ADDITIONAL_SYNTAX,
                            textAttributes,
                            HighlighterTargetArea.EXACT_RANGE
                    )
                }
            }
        }
    }

    /**
     * 代码删除
     * @param editor 编辑器
     * @param line 删除的行数 line the line number (from 0 to getLineCount()-1)
     */
    fun deleteLine(editor: Editor, lineNum: Int) {
        if (editor.isDisposed) {
            return
        }
        ApplicationManager.getApplication().invokeLater {
            DocumentUtil.writeInRunUndoTransparentAction {
                if (lineNum >= 0 && lineNum < editor.document.lineCount) {
                    val startOffset = editor.document.getLineStartOffset(lineNum)
                    val endOffset = min(editor.document.getLineEndOffset(lineNum) + 1, editor.document.textLength)
                    editor.document.deleteString(startOffset, min(endOffset, editor.document.textLength))
                }
            }
        }
    }

    /**
     * 代码删除
     * @param editor 编辑器
     * @param startOffset 删除的起始偏移量
     * @param endOffset 删除的结束偏移量
     */
    fun deleteLineByOffset(editor: Editor, startOffset: Int, endOffset: Int, cancel: Boolean) {
        if (editor.isDisposed&&!cancel) {
            return
        }
       if (!checkOffset(editor,startOffset,endOffset)){
           return
       }
        ApplicationManager.getApplication().invokeLater {
            WriteCommandAction.runWriteCommandAction(editor.project) {
                editor.document.deleteString(startOffset, endOffset)
            }
        }
    }

    private fun checkOffset(editor: Editor, startOffset: Int, endOffset: Int):Boolean{
        if (startOffset>=endOffset){
            return false
        }
        if (startOffset<0||startOffset>editor.document.textLength){
            return false
        }
        if (endOffset<0||endOffset>editor.document.textLength){
            return false
        }
        return true
    }
    /**
     * 选中代码行-为了实现流式动态效果
     * @param editor 编辑器
     * @param lineNumber 选中的行数
     */
     fun selectLine(editor: Editor, lineNumber: Int) {
        ApplicationManager.getApplication().invokeLater {
            WriteCommandAction.runWriteCommandAction(editor.project) {
                if (editor.isDisposed) {
                    return@runWriteCommandAction
                }
                val document = editor.document
                // 安全性检查
                if (lineNumber < 0 || lineNumber >= document.lineCount) {
                    return@runWriteCommandAction
                }
                // 获取行的起始和结束偏移量
                val startOffset = document.getLineStartOffset(lineNumber)
                val endOffset = document.getLineEndOffset(lineNumber)
                if (!checkOffset(editor,startOffset,endOffset)){
                    return@runWriteCommandAction
                }
                // 设置选中
                editor.selectionModel.setSelection(startOffset, min( endOffset+1,editor.document.textLength))
                editor.scrollingModel.scrollTo(editor.offsetToLogicalPosition(startOffset), ScrollType.CENTER);
            }
        }
    }

    /**
     * 滚动到指定行-流式diff滚动到当前比对位置
     * @param editor 编辑器
     * @param lineNumber 滚动的行数
     */
     fun scrollToLine(editor: Editor, lineNumber: Int) {
        ApplicationManager.getApplication().invokeLater{
            if (editor.isDisposed) {
                return@invokeLater
            }
            val document = editor.document
            if (lineNumber < 0 || lineNumber >= document.lineCount){
                return@invokeLater
            }
            editor.scrollingModel.scrollTo(
                    LogicalPosition(lineNumber, 0),
                    ScrollType.MAKE_VISIBLE
            )
        }
    }



    override fun dispose() {
    }

    /**
     * 移除选中
     */
    fun removeSelection(editor: Editor) {
        if (editor.isDisposed) {
            return
        }
        ApplicationManager.getApplication().invokeLater{
            editor.selectionModel.removeSelection()
        }
    }
}