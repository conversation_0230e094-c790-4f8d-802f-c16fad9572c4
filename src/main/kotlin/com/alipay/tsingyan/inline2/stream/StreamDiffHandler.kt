package com.alipay.tsingyan.inline2.stream

import com.alipay.tsingyan.inline2.BaseIntent
import com.alipay.tsingyan.inline2.InlineChatService
import com.alipay.tsingyan.model.enums.FastApplyType
import com.alipay.tsingyan.model.enums.TABKey
import com.alipay.tsingyan.services.completion.TsingYanProdService
import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.alipay.tsingyan.services.completion.edit.model.AntRequestTypeEnum
import com.alipay.tsingyan.services.completion.edit.service.EditorManagerService
import com.alipay.tsingyan.services.completion.edit.util.AntEditorUtil
import com.alipay.tsingyan.services.composer.CodeApplyIntent
import com.alipay.tsingyan.services.file.FilePathUtils
import com.alipay.tsingyan.utils.AppConstant
import com.alipay.tsingyan.utils.FlowTaskExecutor
import com.alipay.tsingyan.utils.NotificationUtils
import com.alipay.tsingyan.utils.ui.UIUtils
import com.alipay.tsingyan.webview.service.JSApiService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import org.apache.http.util.TextUtils
import java.awt.Color

class StreamDiffHandler(
    val project: Project,
    val editor: Editor,
    val startLine: Int,
    val endLine: Int,
    val inlineChatIntent: BaseIntent,
    var type: FastApplyType = FastApplyType.FAST_APPLY
) {

    var index = startLine
    private val codeLineService: CodeLineService = service<CodeLineService>()

    private val inlineChatService = project.service<InlineChatService>()
    val tsingYanProdService: TsingYanProdService = service<TsingYanProdService>()
    private val editorManagerService: EditorManagerService = service<EditorManagerService>()
    private val documentManager = FileDocumentManager.getInstance()

    init {
        //异常情况，未拿到起始行，选中代码等数据
       if (startLine<0){
           ApplicationManager.getApplication().invokeLater{
               LogUtil.info("流式输出异常，未拿到起始行等数据")
               val selectionModel = editor.selectionModel
               val document =editor.document
               val selectionStart = selectionModel.selectionStart
               index = document.getLineNumber(selectionStart)
               val startLineOffSet = document.getLineStartOffset(index)
               inlineChatIntent.startOffset = startLineOffSet
               inlineChatIntent.newCodeStartOffSet = startLineOffSet
           }
       }
    }

    fun onStart() {
        val job = Runnable {
            ApplicationManager.getApplication().invokeLater {
                if (type == FastApplyType.FAST_APPLY){
                    // 第一个位置：流式输出前，文件的第一个位置
                    editorManagerService.mockEditModifiedStart(editor)
                }
                //取消选中
                codeLineService.removeSelection(editor)
                val virtualFile = FileDocumentManager.getInstance().getFile(editor.document)
                val filePath = FilePathUtils.getRelativePath(virtualFile!!.path, project.basePath!!)
                val language = AntEditorUtil.getLanguage(editor)

                if (inlineChatIntent !is CodeApplyIntent){
                    //发送取消选中消息给H5
                    project.service<JSApiService>()
                        .selectedCodeMsg(filePath, virtualFile?.name, null, language, startLine, endLine)
                }
            }
        }
        FlowTaskExecutor.submitTask(job)
    }
    fun onSame(line: String) {
        val job = Runnable {
            try {
                inlineChatIntent.newCode += line + "\n"
                inlineChatIntent.afterStreamOldCode += line + "\n"
                codeLineService.selectLine(editor, index - 1)
                index++
            } catch (e: Exception) {
                LogUtil.info("onSame error: ${e.message}")
            }
        }
        FlowTaskExecutor.submitTask(job)
    }

    fun onOld(line: String) {
        val job = Runnable {
            try {
                inlineChatIntent.newCodeEndOffSet -= line.length
                inlineChatIntent.afterStreamOldCode += line + "\n"
                codeLineService.deleteLine(editor, index)
                codeLineService.removeSelection(editor)
            } catch (e: Exception) {
                LogUtil.info("onOld error: ${e.message}")
            }
        }
        FlowTaskExecutor.submitTask(job)
    }

    fun onNew(line: String) {
        val job = Runnable {
            try {
                if (TextUtils.isEmpty(inlineChatIntent.selectedCode) &&
                        index == inlineChatIntent.startLine) {
                    //首行 没有选中代码 从当前光标位置开始输出结果
                    codeLineService.addLineByOffset(editor, line + "\n", inlineChatIntent.startOffset, false, Color(0x3000FF00, true), false)
                } else {
                    codeLineService.addLine(editor, line, index, Color(0x3000FF00, true))
                }
                inlineChatIntent.newCode += line + "\n"
                codeLineService.removeSelection(editor)
                index++
            } catch (e: Exception) {
                LogUtil.info("onNew error: ${e.message}")
            }
        }
        FlowTaskExecutor.submitTask(job)
    }

    fun onEnd(showOption : Boolean = true) {
        val job = Runnable {
            try {
                inlineChatIntent.isMarkup = true
                getNewCodeEndLine()
                if (editor.isDisposed) {
                    return@Runnable
                }
                //关闭inlineChat
                inlineChatService.closeInlineChat(editor)
                //展示操作栏
                if (showOption){
                    inlineChatService.showOptionViewInEditor(project, editor)
                }
                //获得当前用户选中的editor
                ApplicationManager.getApplication().invokeLater {
                    editor.selectionModel.removeSelection()
                    val currentEditor = FileEditorManager.getInstance(project).selectedTextEditor
                    //如果这两个editor不是同一个
                    if (showOption && currentEditor != null && currentEditor != editor) {
                        //关闭当前editor的chatWindow
                        NotificationUtils.notifyInlineChatMessageSuccess(project)
                    }
                    if (type == FastApplyType.FAST_APPLY){
                        // 第二个位置：流式输出后，文件的最后一个位置
                        editorManagerService.mockEditModifiedEnd(editor)
                    }
                }
            } catch (e: Exception) {
                LogUtil.info("onEnd error: ${e.message}")
            }
        }
        FlowTaskExecutor.submitTask(job)
        closeStreamResponse()
    }


    private fun getNewCodeEndLine(){
        inlineChatIntent.newCodeEndLine = index - 1
    }

    fun onFailed(showOption : Boolean = true) {
        val job = Runnable {
            try {
                //关闭旧的窗口
                inlineChatService.closeInlineChat(editor)
                inlineChatIntent.isProcessing = false
                AppConstant.ENABLE_SEND_SELECTED_CODE_MSG=true
                ApplicationManager.getApplication().invokeLater {
                    NotificationUtils.notifyInlineChatFailed(editor.project!!)
                    val lineStartOffset = inlineChatIntent.oldStartOffset
                    val lineEndOffset = inlineChatIntent.oldEndOffset
                    if (lineStartOffset in 0..editor.document.textLength
                            && lineEndOffset in 0..editor.document.textLength
                            && lineStartOffset < lineEndOffset) {
                        //重新选中代码
                        editor.selectionModel.setSelection(lineStartOffset, lineEndOffset)
                    }
                    if (showOption){
                        inlineChatService.showInlineChat(editor.project!!, editor, inlineChatIntent.clickType!!, inlineChatIntent.questionStr)
                    }
                }
            } catch (e: Exception) {
                LogUtil.info("onFailed error: ${e.message}")
            }
        }
        FlowTaskExecutor.submitTask(job)
        closeStreamResponse()
    }

    /**
     * 最后的自动修复逻辑
     */
    fun autoFix(finalCode : String,callFun : (() -> Unit)? = null, refreshDiff : (() -> Unit)? = null){
        val job = Runnable {
            // 老的高亮信息逻辑
            inlineChatIntent.newCode = finalCode
            runInEdt {
                editor.markupModel.removeAllHighlighters()
                // 告知回调函数，变更的行数
                callFun?.invoke()
                WriteCommandAction.runWriteCommandAction(editor.project) {
                    editor.document.setText(finalCode)
                    documentManager.saveDocument(editor.document)
                    refreshDiff?.invoke()
                }
                editor.caretModel.currentCaret.removeSelection()
                // 设置光标重置到ToolWindow 窗口
                editor.project?.let { project ->
                    // 移除当前编辑器的光标
                    UIUtils.switchToolWindowTab(project, TABKey.AI_PARTNER_HOME)
                }
            }
        }
        FlowTaskExecutor.submitTask(job)
    }

    fun closeStreamResponse(){
        //bugfix：关闭流式的streamResponse的流式响应，不然会一直占用线程
        val streamResponse = tsingYanProdService.getFromCache(inlineChatIntent.uuid)
        if (streamResponse != null){
            LogUtil.info("close streamResponse body")
            streamResponse.body()?.close()
            tsingYanProdService.removeFromCache(inlineChatIntent.uuid)
        }
    }

}