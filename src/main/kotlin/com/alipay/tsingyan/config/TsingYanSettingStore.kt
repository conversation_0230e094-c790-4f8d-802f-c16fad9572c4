package com.alipay.tsingyan.ui.config

import com.alipay.tsingyan.services.completion.edit.LogUtil
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage

/**
 * 青燕配置持久化
 */
@State(name = "AntTsingYanSettingStore",storages=[(Storage("tsingyan_setting.xml" ))])
class TsingYanSettingStore :PersistentStateComponent<TsingYanSetting>{
    private var myState = TsingYanSetting()

    /**
     * 获取配置
     */
    override fun getState(): TsingYanSetting {
        return myState
    }

    /**
     * 加载新的配置
     */
    override fun loadState(state: TsingYanSetting) {
       myState = state
    }

}