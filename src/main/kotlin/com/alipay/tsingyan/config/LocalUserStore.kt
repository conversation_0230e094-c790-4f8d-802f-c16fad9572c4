package com.alipay.tsingyan.ui.config

import com.alibaba.fastjson.JSONObject
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage

/**
 * 用户本地存储信息
 * <AUTHOR>
 * 创建时间 2023-06-21
 */
@State(name = "codeFuseUser", storages = [(Storage("codeFuseUser.xml"))])
class LocalUserStore : PersistentStateComponent<UserState> {


    private var userState: UserState? = null


    fun getUserInfoModel(): UserInfoModel? {
        if (userState == null) {
            return null
        }
        if (userState!!.userInfoStr == null) {
            return null
        }

        return JSONObject.parseObject(userState!!.userInfoStr, UserInfoModel::class.java)
    }

    fun setUserInfoModel(userState: UserState,userInfoModel: UserInfoModel?) {
        if (userInfoModel == null) {
            return
        }
        userState.userInfoStr = JSONObject.toJSONString(userInfoModel)
        setUserState(userState)
    }


    fun setUserState(userState: UserState?) {
        this.userState = userState
    }


    override fun loadState(state: UserState) {
        this.userState = state
    }

    override fun getState(): UserState? {
        return userState
    }


}