package com.alipay.tsingyan.ui.config

class TsingYanSetting {
    /***************** 代码扫描配置 start *****************/
    /**
     * 是否开启智能补全
     */
    var codegenSwitch: Boolean = true


    var codegenSecureSwitch: Boolean = false

    /**
     * 是否开启插件自动升级
     */
    var autoUpdatePluginSwitch: Boolean = true

    var enableLineMarkerSwitch: Boolean = true

    /**
     * 悬浮窗是否展示
     */
    var enableFloatingBarShowCb:Boolean = true

    /**
     * 是否离线模式(测试用）
     */
    var offLineSwitch: Boolean = false

    /**
     * 是否开启插件自动升级
     */
    var onLineSwitch: Boolean = true

    /***************** 代码b配置 start *****************/

    /**
     * 设置是否点击过
     */
    var NOTIFY_V012_HAS_CLICKED: Boolean = false

    /**
     * 设置是否点备份过
     */
    var HAS_BACKUP_VERSION_STATE: Boolean = false


    /**
     * 设置是否显示过
     */
    var NOTIFY_PERMISSION_HAS_SHOWN: Boolean = false

    /**
     * 采纳过的次数
     */
    var ACCEPTED_COUNT: Int = 0

    var SHOW_H5_BOOTOM_TIPS :Boolean = true

    var SHOW_TEST_TIPS :Boolean = true

    /**
     * 是否反射关闭过idea的补全
     */
    var HAS_SET_FLCC_ENBALE: Boolean = false

    /**
     * 注释的默认语言偏向
     */
    var ANNOTATION_LANGUAGE_SELECTOR = "中文"

    /**
     * 提交消息的默认语言偏向
     */
    var COMMIT_MSG_LANGUAGE_SELECTOR = "中文"

    /**
     * 提交消息的默认长度
     */
    var COMMIT_MSG_LEHGTH_SELECTOR = "200"

    /**
     * 智能问答默认语言偏向
     */
    var AI_DIALOG_LANGUAGE_SELECTOR = "中文"

    /**
     * 安全过滤提醒是否点击过不再提醒
     */
    var SECURE_NO_NOTICE_CLICKED: Boolean = false

    /**
     * 插件第一次安装时生成
     */
    var CLIENT_ID: String = ""

    /**
     * 悬浮窗是否展示
     */
    var codeEditSwitch:Boolean = true

    /**
     * 自动修复的选择框
     */
    var enableAutoFix : Boolean = false

    /**
     * 搜索增强 - 仓库问答开关
     */
    var enableRepoQASearch: Boolean = true

    /**
     * 搜索增强 - 解释代码开关
     */
    var enableExplainCodeSearch: Boolean = true

    /**
     * 搜索增强 - A Par Agent开关
     */
    var enableAParAgentSearch: Boolean = true

    /**
     * 项目规则自动更新开关
     */
    var enableProjectRuleAutoUpdate: Boolean = true
}
