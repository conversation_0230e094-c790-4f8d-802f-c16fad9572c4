package com.alipay.tsingyan.config

import com.alipay.tsingyan.BuildInfo
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.ToggleAction
import com.intellij.openapi.project.DumbAware

/**
 * Debug 开关按钮
 */
class DebugToggleAction : ToggleAction(), DumbAware {
    override fun isSelected(e: AnActionEvent): Boolean {
        return BuildInfo.isDebug
    }

    override fun setSelected(e: AnActionEvent, state: <PERSON><PERSON>an) {
        BuildInfo.isDebug = state
    }
}