<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
    <id>com.alipay.codefuse</id>
    <name>CodeFuse</name>
    <vendor>CodeFuse</vendor>

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.modules.lang</depends>
    <depends optional="true" config-file="myPluginId-terminal.xml">org.jetbrains.plugins.terminal</depends>
    <depends optional="true" config-file="pluginbak.xml">com.intellij.modules.java</depends>
    <depends optional="true" config-file="myPluginId-withPython.xml">com.intellij.modules.python</depends>
    <depends optional="true" config-file="myPluginId-withJavaScript.xml">JavaScript</depends>

    <!-- idea need -->
<!--    <depends>com.intellij.modules.java</depends>-->
<!--    <depends>com.intellij.java</depends>-->
    <!-- pycharm need -->
<!--    <depends>com.intellij.modules.python</depends>-->
    <!-- webstorm need -->
<!--    <depends>JavaScript</depends>-->


    <extensions defaultExtensionNs="com.intellij">
        <errorHandler implementation="com.alipay.tsingyan.utils.ui.ErrorSubmitter"/>
        <fileEditorProvider implementation="com.alipay.tsingyan.utils.editor.povider.McpEditorProvider"/>

        <applicationService serviceImplementation="com.alipay.tsingyan.services.completion.impl.TsingYanProdServiceImpl"
                            serviceInterface="com.alipay.tsingyan.services.completion.TsingYanProdService"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.completion.CompletionServiceImpl"
                            serviceInterface="com.alipay.tsingyan.completion.CompletionService"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.agent.AgentServiceImpl"
                            serviceInterface="com.alipay.tsingyan.agent.interface.AgentService"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.ui.config.TsingYanSettingStore"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.ui.config.LocalUserStore"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.config.VersionControlsStore"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.services.input.InputTrackingServiceImpl"
                            serviceInterface="com.alipay.tsingyan.services.input.InputTrackingService"/>

        <applicationConfigurable id="AntTsingyan.Configurable"
                                 provider="com.alipay.tsingyan.application.PluginConfigProvider"
                                 displayName="CodeFuse"/>
        <applicationConfigurable
                parentId="AntTsingyan.Configurable"
                id="com.alipay.tsingyan.mcp.ui.McpServerConfigurable"
                instance="com.alipay.tsingyan.mcp.ui.McpServerConfigurable"
                displayName="MCP Servers"/>

        <applicationConfigurable
                parentId="AntTsingyan.Configurable"
                id="com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable"
                instance="com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable"
                displayName="规则"/>

        <applicationConfigurable
                parentId="AntTsingyan.Configurable"
                id="com.alipay.tsingyan.mcp.ui.AgentModelConfigurable"
                provider="com.alipay.tsingyan.mcp.ui.AgentModelConfigurableProvider"
                displayName="AI Partner"
        />

        <applicationConfigurable
                parentId="tools"
                id="com.alipay.tsingyan.application.TsingYanConfig"
                instance="com.alipay.tsingyan.application.TsingYanConfig"
                displayName="CodeFuse"/>

        <applicationConfigurable
                parentId="com.alipay.tsingyan.application.TsingYanConfig"
                id="com.alipay.tsingyan.mcp.ui.McpServerConfigurable.tools"
                instance="com.alipay.tsingyan.mcp.ui.McpServerConfigurable"
                displayName="MCP Servers"/>

        <applicationConfigurable
                parentId="com.alipay.tsingyan.application.TsingYanConfig"
                id="com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable.tools"
                instance="com.alipay.tsingyan.context.ui.ContextEnhancementConfigurable"
                displayName="规则"/>

        <applicationConfigurable
                parentId="com.alipay.tsingyan.application.TsingYanConfig"
                id="com.alipay.tsingyan.mcp.ui.AgentModelConfigurable.tools"
                provider="com.alipay.tsingyan.mcp.ui.AgentModelConfigurableProvider"
                displayName="AI Partner"
        />
        <applicationService
                serviceImplementation="com.alipay.tsingyan.services.completion.edit.service.impl.AntCompletionServiceImpl"
                serviceInterface="com.alipay.tsingyan.services.completion.edit.service.AntCompletionService"/>
<!--        <applicationService serviceImplementation="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseEditorActionTracker"/>-->

        <applicationService
                serviceImplementation="com.alipay.tsingyan.codeedits.CodeEditsServiceImpl"
                serviceInterface="com.alipay.tsingyan.codeedits.CodeEditsService"/>

        <applicationService
                serviceImplementation="com.alipay.tsingyan.services.completion.edit.service.impl.EditorManagerServiceImpl"
                serviceInterface="com.alipay.tsingyan.services.completion.edit.service.EditorManagerService"/>
        <editorFactoryListener implementation="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseEditorListener"/>

        <!--生产注释 start-->
        <applicationService
                serviceImplementation="com.alipay.tsingyan.services.comment.service.AddCommentServiceImpl"
                serviceInterface="com.alipay.tsingyan.services.comment.service.AddCommentService"/>
        <!--生产注释 end-->

        <!-- 埋点的服务 start -->
        <applicationService serviceImplementation="com.alipay.tsingyan.services.trace.CodeFuseTracer"
                            serviceInterface="com.alipay.tsingyan.services.trace.CodeFuseTracerService"/>
        <!-- 埋点的服务 end -->

        <applicationService serviceImplementation="com.alipay.tsingyan.services.newcompletion.cache.CacheServiceImpl"
                            serviceInterface="com.alipay.tsingyan.services.newcompletion.cache.CacheService"/>

        <!--codefuse statusbar widget start-->
        <applicationService serviceImplementation="com.alipay.tsingyan.view.status.CodeFuseStatusService"/>

        <!-- codefuse mcp server end -->

        <statusBarWidgetFactory implementation="com.alipay.tsingyan.view.statusbar.CodeFuseWidgetFactory"/>
        <!--codefuse statusbar widget end-->

        <!--        对话框 start-->
        <toolWindow id="CodeFuse" anchor="right" secondary="false" canCloseContents="false" icon="/icons/toolWindow.svg"
                    factoryClass="com.alipay.tsingyan.talk.CodeFuseToolWindowFactory">
        </toolWindow>

        <!--        对话框 end-->


        <notificationGroup id="CodeFuse Notification Group"
                           displayType="BALLOON"
                           key="CodeFuse.group"/>

        <projectService serviceImplementation="com.alipay.tsingyan.view.jcef.api.CodeFuseTalkApi" preload="true"/>

        <!--   JCEF WebSocket 数据    -->
        <!-- JCEF websocket通信端口监听-->
        <projectService serviceImplementation="com.alipay.tsingyan.view.jcef.common.websocket.WebSocketInitService"
                        preload="true"/>
        <!--JCEF webSocket消息处理器-->
        <projectService serviceImplementation="com.alipay.tsingyan.view.jcef.common.websocket.JcefSocketHandler"
                        preload="true"/>
        <!--处理器工厂-->
        <projectService serviceImplementation="com.alipay.tsingyan.view.jcef.common.websocket.IdeHandlerFactory"
                        preload="true"/>
        <!--客户端连接管理-->
        <projectService serviceImplementation="com.alipay.tsingyan.view.jcef.common.websocket.ChannelManager"
                        preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.services.chat.service.AnalysisCodeService"
                preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.services.codesuggestion.service.CodeSuggestionService"
                preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.services.codesimple.service.CodeSimpleService"
                preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.services.test.action.TestService"
                preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.services.debugfilter.DebugFilterService"
                preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.inline2.InlineChatService"
                preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.floating.service.FloatingToolBarService"
                        preload="true"/>
        <projectService
                serviceImplementation="com.alipay.tsingyan.inline2.markup.MarkupCodeManager"
                preload="true"/>
        <projectService
            serviceImplementation="com.alipay.tsingyan.services.file.RecentFilesService"
            preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.services.search.FileSearchService"
                        preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.talk.WebViewService"  preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.prompt.service.PromptCacheService"  preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.webview.service.JSApiService"  preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.services.composer.ComposerService" preload="true"/>
        <projectService serviceImplementation="com.alipay.tsingyan.context.services.ProjectRuleAutoUpdateService" preload="true"/>
        <actionPromoter order="last" implementation="com.alipay.tsingyan.action.PromoterAction"/>
        <actionPromoter order="last" implementation="com.alipay.tsingyan.services.ask.action.CodefuseAskActionPromoter"/>
        <actionPromoter order="last" implementation="com.alipay.tsingyan.inline2.action.InlineChatActionPromoter"/>
        <editorNotificationProvider implementation="com.alipay.tsingyan.services.composer.ComposerEditorProvider"/>


        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.TestRpcApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.ServerStreamApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.ServerStreamResponseApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.mcp.api.ChatRequestHandlerApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.mcp.api.ExecuteToolHandlerApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.ide.MarkUpRpcApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.ide.OpenSettingApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.common.CommonRpcApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.view.jcef.socket.api.mcp.McpToolApi" preload="true"/>
        <applicationService serviceImplementation="com.alipay.tsingyan.mcp.api.apply.ApplyStatusApi" preload="true"/>
    </extensions>


    <actions>
        <group id="com.alipay.tsingyan.group.CodeFuseGroup" class="com.alipay.tsingyan.group.CodeFuseGroup" popup="true" icon="/images/codefuseAI.svg"
               text="CodeFuse" description="CodeFuse">
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
            <action id="codefuseInlineChat" class="com.alipay.tsingyan.inline2.action.InlineChatAction"
                    text="Text to Code" description="Text to Code">
                <keyboard-shortcut first-keystroke="control I" keymap="$default"/>
                <keyboard-shortcut first-keystroke="meta I" keymap="Mac OS X" replace-all="true"/>
                <keyboard-shortcut first-keystroke="meta I" keymap="Mac OS X 10.5+" replace-all="true"/>
            </action>
            <action id="codefuseAddComment" class="com.alipay.tsingyan.services.comment.action.AddCommentAction"
                    text="添加注释" description="对选中代码生成注释">
<!--                <add-to-group group-id="com.alipay.tsingyan.group.CodeFuseGroup" anchor="first"/>-->
                <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift ctrl L"/>-->
            </action>
            <separator/>
            <action id="codefuseChat" class="com.alipay.tsingyan.services.chat.action.ChatAction"
                    text="解释代码" description="解释选中代码意图">
<!--                <add-to-group group-id="com.alipay.tsingyan.group.CodeFuseGroup" relative-to-action="codefuseAddComment" anchor="after"/>-->
                <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift ctrl J"/>-->
            </action>
            <action id="codefuseCodeSuggestion" class="com.alipay.tsingyan.services.codesuggestion.action.CodeSuggestionAction"
                    text="优化代码" description="对选中代码生成优化建议">
<!--                <add-to-group group-id="com.alipay.tsingyan.group.CodeFuseGroup" relative-to-action="codefuseChat" anchor="after"/>-->
                <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift ctrl h"/>-->
            </action>
            <action id="codefuseTest" class="com.alipay.tsingyan.services.test.action.TestAction"
                    text="生成单测" description="对选中代码生成单测">
<!--                <add-to-group group-id="com.alipay.tsingyan.group.CodeFuseGroup" relative-to-action="codefuseCodeSuggestion" anchor="after"/>-->
                <!--            <keyboard-shortcut keymap="$default" first-keystroke="shift ctrl h"/>-->
            </action>
            <separator/>
            <action id="codefuseAsk" class="com.alipay.tsingyan.services.ask.action.CodefuseAskAction"
                    text="在会话中提问" description="在会话中提问">
<!--                <add-to-group group-id="com.alipay.tsingyan.group.CodeFuseGroup" relative-to-action="codefuseTest" anchor="after"/>-->
                <keyboard-shortcut first-keystroke="control Y" keymap="$default"/>
                <keyboard-shortcut first-keystroke="meta Y" keymap="Mac OS X" replace-all="true"/>
                <keyboard-shortcut first-keystroke="meta Y" keymap="Mac OS X 10.5+" replace-all="true"/>
            </action>
            <separator/>
<!--            <action id="inputTracking" class="com.alipay.tsingyan.action.InputTrackingAction"-->
<!--                    text="输入追踪" description="开启/关闭输入变化追踪">-->
<!--                <keyboard-shortcut first-keystroke="control alt T" keymap="$default"/>-->
<!--                <keyboard-shortcut first-keystroke="meta alt T" keymap="Mac OS X" replace-all="true"/>-->
<!--                <keyboard-shortcut first-keystroke="meta alt T" keymap="Mac OS X 10.5+" replace-all="true"/>-->
<!--            </action>-->
        </group>
        <action id="codefuseApply" class="com.alipay.tsingyan.services.completion.edit.action.CodeApplyAction">
            <keyboard-shortcut first-keystroke="TAB" keymap="$default"/>
            <override-text place="MainMenu" text="Apply Completions to Editor"/>
            <override-text place="EditorPopup" text="Accept"/>
        </action>

        <action class="com.alipay.tsingyan.services.completion.edit.action.DisposableAction">
            <keyboard-shortcut first-keystroke="ESCAPE" keymap="$default"/>
            <override-text place="MainMenu" text="Hide Completions in Editor"/>
        </action>


        <action id="codefuseCompletion"
                class="com.alipay.tsingyan.services.newcompletion.action.CodeFuseCompletion"
                text="CodeFuse: 代码补全" description="CodeFuse代码补全">
            <keyboard-shortcut first-keystroke="alt BACK_SLASH" keymap="$default"/>
        </action>
        <action id="codefuseNextEditorInlays"
                class="com.alipay.tsingyan.services.newcompletion.action.NextEditorInlays"
                text="CodeFuse: 下一个补全结果">
            <keyboard-shortcut keymap="$default" first-keystroke="alt CLOSE_BRACKET"/>
        </action>
        <action id="codefusePreviousEditorInlays"
                class="com.alipay.tsingyan.services.newcompletion.action.PreviousEditorInlays"
                text="CodeFuse: 上一个补全结果">
            <keyboard-shortcut keymap="$default" first-keystroke="alt OPEN_BRACKET"/>
        </action>

        <action id="codefuseInlineChatAccept"
                class="com.alipay.tsingyan.inline2.action.InlineChatAcceptAction"
                text="CodeFuse: inlineChat采纳代码">
            <keyboard-shortcut keymap="$default" first-keystroke="alt A"/>
        </action>
        <action id="codefuseInlineChatDiff"
                class="com.alipay.tsingyan.inline2.action.InlineChatDiffAction"
                text="CodeFuse: inlineChat查看变更">
            <keyboard-shortcut keymap="$default" first-keystroke="alt D"/>
        </action>
        <action id="codefuseInlineChatRedo"
                class="com.alipay.tsingyan.inline2.action.InlineChatRedoAction"
                text="CodeFuse: inlineChat重做">
            <keyboard-shortcut keymap="$default" first-keystroke="alt R"/>
        </action>

        <action id="codefuseApplyInlaysNextLine"
                class="com.alipay.tsingyan.services.newcompletion.action.LineApplyInlayAction"
                text="CodeFuse: 逐行采纳" description="CodeFuse逐行采纳">
            <keyboard-shortcut first-keystroke="control DOWN" keymap="$default"/>
            <keyboard-shortcut first-keystroke="meta DOWN" keymap="Mac OS X" replace-all="true"/>
            <keyboard-shortcut first-keystroke="meta DOWN" keymap="Mac OS X 10.5+" replace-all="true"/>
        </action>

        <action id="CodefuseCommitMessageGenerationAction" class="com.alipay.tsingyan.services.commit.CodefuseCommitMessageGenerationAction" icon="/images/commit_icon_start.svg" description="CodeFuse: 生成提交信息" text="CodeFuse: 生成提交信息">
            <add-to-group group-id="Vcs.MessageActionGroup" anchor="first"/>
        </action>

        <action class="com.alipay.tsingyan.action.TabActiveAction"
                id="CodeFuse.TabActive"
                text="CodeFuse.TabActive"
                description="CodeFuse.TabActive">
            <keyboard-shortcut first-keystroke="shift control I" keymap="$default"/>
            <keyboard-shortcut first-keystroke="shift meta I" keymap="Mac OS X"/>
            <keyboard-shortcut first-keystroke="shift meta I" keymap="Mac OS X 10.5+"/>
        </action>

        <action id="CodeFuse.Debug"
                text="CodeFuse.Debug"
                class="com.alipay.tsingyan.config.DebugToggleAction"/>

<!--        <action class="com.alipay.tsingyan.action.TopTestAction"-->
<!--                text="CodeFuse-Test"-->
<!--                id="CodeFuse.TestAction">-->
<!--            <add-to-group group-id="HelpMenu" anchor="first"/>-->
<!--        </action>-->
    </actions>

    <applicationListeners>
        <listener class="com.alipay.tsingyan.application.ProjectManagerListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
        <listener class="com.alipay.tsingyan.application.ProjectUpdateApplicationListener"
                  topic="com.intellij.ide.AppLifecycleListener"/>
        <listener class="com.alipay.tsingyan.application.AppCommonListener"
                  topic="com.intellij.ide.AppLifecycleListener"/>
<!--        <listener topic="com.intellij.openapi.actionSystem.ex.AnActionListener"-->
<!--                  class="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseEditorActionTracker$ActionListener"-->
<!--                  activeInTestMode="true" activeInHeadlessMode="true"/>-->
    </applicationListeners>
    <projectListeners>
        <listener class="com.alipay.tsingyan.talk.ToolWindowListener"
                  topic="com.intellij.openapi.wm.ex.ToolWindowManagerListener"/>
        <listener class="com.alipay.tsingyan.talk.UIThemeListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
        <listener topic="com.intellij.openapi.command.CommandListener"
                  class="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseCommandListener"/>
        <listener topic="com.intellij.codeInsight.lookup.LookupManagerListener"
                  class="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseLookupListener"/>
        <listener topic="com.intellij.openapi.fileEditor.FileEditorManagerListener"
                  class="com.alipay.tsingyan.services.newcompletion.listener.CodeFuseEditorFocusListener"/>
        <listener class="com.alipay.tsingyan.services.newcompletion.EditorBeforeListener"
                  topic="com.intellij.openapi.fileEditor.FileEditorManagerListener$Before"/>
    </projectListeners>
</idea-plugin>
