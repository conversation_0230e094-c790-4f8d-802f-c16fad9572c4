group.id=ChatGPT Notification Group

action.refresh.manually.text=Manual setting
action.refresh.automatic.text=Automatic setting
action.refresh=Reset Thread
action.settings=Settings
action.open_online_chatgpt=Open Online ChatGPT
action.gift=Activities
action.plugins=More Plugins from Author
action.code.optimize.menu=Optimize
action.code.test.menu=Add Test Case
action.code.wrong.menu=Find Bug
action.code.explain.menu=Explain
action.code.custom.action=Custom Actions

toolwindows.content.search=Search
toolwindows.content.online=Online ChatGPT

notify.config.title=ChatGPT: Config Initialization
notify.config.text=Please configure ChatGPT first
notify.common.error.title=ChatGPT: Request server error
notify.common.error.text=Http response code: {0}, response message: {1}
notify.token_expired.error.title=ChatGPT: Token has expired
notify.token_expired.error.text=The token for the URL origin you are running has expired
notify.timeout.error.title=ChatGPT: Read timed out
notify.timeout.error.text=The connection to the remote server timed out
notify.too_many_request.error.title=ChatGPT: Too many requests
notify.too_many_request.error.text=Too many requests, please slow down
notify.unavailable.title=ChatGPT: This service is temporarily unavailable
notify.unavailable.text=Temporarily unavailable, it is recommended to use the official source

ui.toolwindow.send=Send
ui.setting.menu.text=ChatGPT
ui.setting.url.title=Server Settings
ui.setting.url.default.label=Default
ui.setting.url.default.remark=Temporarily unavailable, it is recommended to use the official source
ui.setting.url.default.token.label=API Key:
ui.setting.url.default.token.empty_text=Fill in the API Key
ui.setting.url.official.label=Official
ui.setting.url.official.remark=This option is to use the official URL. Email and password will only be saved locally and will not be stored in the cloud, please feel free to use them.
ui.setting.url.official.token.label=API Key:
ui.setting.url.official.token.empty_text=Login Open AI, access https://beta.openai.com/account/api-keys
ui.setting.url.customize.label=Customize
ui.setting.url.customize.remark=If you have your own backend server, you can fill in your API address here
ui.setting.url.customize.url.label=URL:
ui.setting.url.customize.url.empty_text=Set your own server URL
ui.setting.url.cloudflare.label=Cloudflare
ui.setting.url.cloudflare.remark=Use the cloudflare service
ui.setting.url.cloudflare.url.label=URL:
ui.setting.url.cloudflare.url.empty_text=Set the Cloudflare Workers domain
ui.setting.connection.title=Connection Settings
ui.setting.connection.read_timeout.label=Read Timeout (ms):
ui.setting.connection.read_timeout.remark=Time for reading data from the server
ui.setting.connection.read_timeout.empty_text=10 seconds by default
ui.setting.connection.connection_timeout.label=Connection Timeout (ms):
ui.setting.connection.connection_timeout.remark=Time for establishing a connection with the server
ui.setting.connection.connection_timeout.empty_text=10 seconds by default
ui.jcef.not_support=The current IDE does not support online Chat GPT, please try to upgrade to the latest version. \n\n(Note: Android Studio is temporarily unavailable due to official issues(Not supported JECF). Need to wait for Android Studio official to fix it.)
ui.conversation.me=Me
