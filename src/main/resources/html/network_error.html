<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接错误</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            background-color: #f5f7f9;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            animation: fadeIn 0.6s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            fill: #f56c6c;
        }
        
        h1 {
            color: #1989fa;
            font-weight: 600;
            margin-bottom: 16px;
            font-size: 28px;
        }
        
        p {
            margin-bottom: 20px;
            color: #606266;
            font-size: 16px;
        }
        
        .card {
            background-color: #f2f6fc;
            border-left: 4px solid #1989fa;
            padding: 16px;
            margin: 20px 0;
            text-align: left;
            border-radius: 4px;
        }
        
        .card-title {
            font-weight: 600;
            color: #1989fa;
            margin-bottom: 10px;
        }
        
        ul, ol {
            text-align: left;
            padding-left: 20px;
            margin-top: 10px;
        }
        
        li {
            margin-bottom: 8px;
            color: #606266;
        }
        
        .help {
            font-size: 14px;
            color: #909399;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <svg class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 11c-.55 0-1-.45-1-1V8c0-.55.45-1 1-1s1 .45 1 1v4c0 .55-.45 1-1 1zm1 4h-2v-2h2v2z" fill="#f56c6c"/>
        </svg>
        
        <h1>前端静态资源加载失败</h1>
        <p>无法连接到所需的资源，这可能是由于网络环境问题导致的。</p>
        <p>请点击面板右上角【...】按钮尝试重载插件，若仍无法修复，请排查以下事项。</p>

        <div class="card">
            <div class="card-title">请确认以下事项：</div>
            <ul>
                <li>您当前是否在内网环境下使用该功能</li>
                <li>如果在内网环境下，请确认您是否已关闭 IDEA 代理</li>
                <li>网络连接是否稳定</li>
                <li>防火墙或网络策略是否限制了相关连接</li>
            </ul>
        </div>
        
        <div class="card">
            <div class="card-title">如何关闭 IDEA 代理：</div>
            <ol>
                <li>打开 IDEA 设置 (Ctrl+Alt+S 或 Command+,)</li>
                <li>搜索 "HTTP Proxy" 或进入 "Appearance & Behavior > System Settings > HTTP Proxy"</li>
                <li>选择 "No proxy" 以关闭代理</li>
                <li>重启 IDEA </li>
            </ol>
        </div>
        
        <p class="help">如果问题仍然存在，请进钉群 30085003758 反馈。</p>
    </div>
</body>
</html>