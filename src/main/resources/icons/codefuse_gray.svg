<?xml version="1.0" encoding="UTF-8"?>
<svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>lineMarkerIcon备份 2</title>
    <defs>
        <linearGradient x1="30.1737981%" y1="109.291146%" x2="76.110006%" y2="-25.4698016%" id="linearGradient-1">
            <stop stop-color="#5C6CF7" offset="0%"></stop>
            <stop stop-color="#6372F7" offset="12%"></stop>
            <stop stop-color="#7582F8" offset="29%"></stop>
            <stop stop-color="#939EFA" offset="49%"></stop>
            <stop stop-color="#BDC3FC" offset="71%"></stop>
            <stop stop-color="#F2F3FE" offset="95%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="27.563955%" y1="87.5093993%" x2="78.7243396%" y2="-41.3150639%" id="linearGradient-2">
            <stop stop-color="#5C6CF7" offset="0%"></stop>
            <stop stop-color="#6372F7" offset="12%"></stop>
            <stop stop-color="#7582F8" offset="29%"></stop>
            <stop stop-color="#939EFA" offset="49%"></stop>
            <stop stop-color="#BDC3FC" offset="71%"></stop>
            <stop stop-color="#F2F3FE" offset="95%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="4.42640728%" y1="223.94854%" x2="138.028778%" y2="-157.496289%" id="linearGradient-3">
            <stop stop-color="#5C6CF7" offset="0%"></stop>
            <stop stop-color="#6372F7" offset="12%"></stop>
            <stop stop-color="#7582F8" offset="29%"></stop>
            <stop stop-color="#939EFA" offset="49%"></stop>
            <stop stop-color="#BDC3FC" offset="71%"></stop>
            <stop stop-color="#F2F3FE" offset="95%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" opacity="0.50104559">
        <g id="3.3-IDEA底部状态栏增加插件设置快捷菜单入口" transform="translate(-1049, -883)" fill-rule="nonzero">
            <g id="lineMarkerIcon备份-2" transform="translate(1049, 883)">
                <rect id="矩形" fill-opacity="0" fill="#D8D8D8" x="7.07922452e-07" y="0" width="13.9999993" height="14"></rect>
                <g id="CODEFUSE-logo-彩图" transform="translate(0, 1.75)">
                    <path d="M14,0.463196592 C12.0198644,0.784276046 9.20283071,1.66680591 7.80031152,4.08542903 C7.80031152,4.08542903 6.15973314,6.59177876 6.36734288,10.5 L10.3460684,10.5 C10.3691362,9.15778261 10.4457211,7.57607152 11.0842364,6.36018047 C11.3842549,5.78650581 11.8021688,5.27582145 12.3132862,4.85830061 C12.8216993,4.45124906 13.3882433,4.23895062 13.9953864,4.00033419 L14,0.463196592 Z" id="路径" fill="url(#linearGradient-1)"></path>
                    <path d="M5.97334571,7.30236444 C5.74082279,7.35675495 5.49445923,7.32341883 5.23517771,7.29534631 C4.19357147,7.18571532 3.38051954,6.38841187 3.29748671,5.39518757 C3.25525987,4.82627043 3.46345179,4.26623356 3.87231535,3.84889688 C4.28117888,3.43156022 4.85398408,3.19441492 5.45385999,3.19412649 C6.15142875,3.19412649 6.78902136,3.4073022 7.18394123,3.89593951 L7.18394123,3.89593951 C7.73203098,3.01867325 8.45820374,2.11070265 9.32555114,1.51065252 L9.32555114,1.51065252 C8.32071996,0.526359763 7.0058582,0 5.44832372,0 C2.42646684,0.0385519741 -0.00151356146,2.37931017 1.11015697e-16,5.25259803 C0.00151639447,8.12588588 2.43196396,10.4643285 5.45385999,10.5 C5.54613098,10.5 5.64578367,10.5 5.74082279,10.4929819 L5.74082279,10.4412232 C5.65532723,9.38958944 5.73493732,8.33171367 5.97703655,7.30236444 L5.97334571,7.30236444 Z" id="路径" fill="url(#linearGradient-2)"></path>
                    <path d="M14,8.23928482 L14,4.69337455 C13.551563,4.87584594 13.17602,5.05042193 12.8503034,5.30746093 C12.3849308,5.6886853 12.0049328,6.15498211 11.7329015,6.67862812 C11.4856434,7.1716023 11.3138174,7.69566541 11.2226429,8.23489849 L14,8.23928482 Z" id="路径" fill="url(#linearGradient-3)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>