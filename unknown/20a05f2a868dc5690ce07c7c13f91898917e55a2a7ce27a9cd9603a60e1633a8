package com.alipay.tsingyan.mcp.server.ide.tools.file

import com.alipay.tsingyan.mcp.server.base.*
import com.alipay.tsingyan.mcp.server.ide.tools.IdeFunTool
import com.alipay.tsingyan.utils.ProjectStructureGenerator
import com.alipay.tsingyan.utils.vfs.VfsUtils
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import java.io.File
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 列出目录内容的工具
 * 展示指定路径下的所有文件和目录，便于用户探索代码库结构
 */
@ToolDesc(
    name = "list_dir",
    resultType = ResultType.PATH_LIST,
    desc = "列出目录的内容。这是一个快速工具，用于在使用更有针对性的工具（如语义搜索或文件读取）之前进行发现。在深入研究特定文件之前，这有助于理解文件结构。可以用来探索代码库。\n" +
            "如果一个文件夹下的只有一个子文件夹，那么将会进行递归直至文件夹下不只有一个子文件夹"
)
@Service(Service.Level.PROJECT)
class ListDirTool(project: Project) : IdeFunTool<DirInfo>(project) {
    override fun execute(param: DirInfo, sessionUid: String, questionUid: String): ToolResult {
        val toolResult = ToolResult()
        val absoluteFilePath = VfsUtils.getAbsoluteFilePath(param.relative_workspace_path, project)
        val vfsFile = VfsUtils.getLocalVfsFile(absoluteFilePath)
        if (null == vfsFile) {
            toolResult.buildPathError(
                nlResult = "Error calling tool: Could not resolve URI: '$absoluteFilePath'",
                desc = "路径不存在"
            )
        } else {
            // 遍历文件夹及子集
            if (vfsFile.isDirectory) {
                val fileList = mutableListOf<PathResult>()
                val dirBuilder = StringBuilder()
                val currFile = recursivelyGetDir(vfsFile, dirBuilder, fileList, max(param.depth, 3))
                val prefix = if (currFile != vfsFile) {
                    "It was found that there is only one folder under this folder. It has been automatically expanded to the level where there are multiple folders or files in the subfolder. The path of the expanded subfolder is:${currFile.path}\n"
                } else {
                    ""
                }
                // 描述中增加深度信息
                val depthInfo = if (param.depth > 1) "（递归深度：${param.depth}）" else ""
                toolResult.buildPathSuccess(
                    nlResult = prefix + dirBuilder.toString(),
                    desc = "已查看 ${VfsUtils.getRelativeFilePath(currFile, project)}${depthInfo}",
                    data = fileList
                )
            } else {
                toolResult.buildPathError(
                    nlResult = "This is a file",
                    desc = "这个路径是一个文件"
                )
            }
        }
//        LogUtil.info("获取文件夹自然语言描述的结果信息为：[${toolResult.nlResult}]")
        return toolResult
    }

    /**
     * 递归获取子文件夹
     * 如果一个目录只包含一个子目录，则继续递归到有多个子项的层级
     */
    private fun recursivelyGetDir(
        vfsFile: VirtualFile,
        builder: StringBuilder,
        fileList: MutableList<PathResult>,
        depth: Int,
    ): VirtualFile {
        val childrenFiles = getIgnoredChildren(vfsFile)
        if (vfsFile.isDirectory
            && (childrenFiles.size == 1)
            && (true == childrenFiles.firstOrNull()?.isDirectory)
        ) {
            return recursivelyGetDir(childrenFiles.first()!!, builder, fileList, depth)
        } else {
            builder.appendLine("Contents of directory: ${VfsUtils.getRelativeFilePath(vfsFile, project)}")
            builder.appendLine()
            // 遍历目录中的所有子项
            getDepthDir(childrenFiles, builder, fileList, depth)
            return vfsFile
        }
    }


    /**
     * 获取子文件夹
     * @param childrenFiles 当前目录下的子文件/文件夹列表
     * @param builder 用于构建输出文本的StringBuilder
     * @param fileList 收集所有路径结果的列表
     * @param depth 递归深度，1表示只显示当前层级，大于1则递归显示子目录
     * @param indentation 当前缩进级别，用于格式化输出
     */
    private fun getDepthDir(
        childrenFiles: List<VirtualFile>,
        builder: StringBuilder,
        fileList: MutableList<PathResult>,
        depth: Int,
        indentation: String = "",
    ) {
        // 对文件夹和文件进行排序，先显示文件夹，再显示文件
        val sortedChildren = childrenFiles.sortedWith(compareBy({ !it.isDirectory }, { it.name }))
        // 遍历目录中的所有子项
        sortedChildren.forEach { childVfs ->
            val relativeFilePath = VfsUtils.getRelativeFilePath(childVfs, project)
            val currFileName = childVfs.name
            val ioFile = File(childVfs.path)
            if (childVfs.isDirectory) {
                // 处理文件夹
                val ignoredChildrenFiles = getIgnoredChildren(childVfs)
                val itemCount = ignoredChildrenFiles.size.toString()
                val displayPath = "$relativeFilePath/"
                val displayText = "$indentation[dir]  $currFileName (${itemCount} items)"
                fileList.add(PathResult(type = "dir", path = displayPath))
                builder.appendLine(displayText)
                // 如果深度大于1并且有子项，则递归展示子目录
                if (ignoredChildrenFiles.size > 20) {
                    builder.appendLine("The number of sub-files under $currFileName has exceeded 20. We are not performing active acquisition. Please obtain them manually.")
                } else {
                    if (depth > 1 && ignoredChildrenFiles.isNotEmpty()) {
                        getDepthDir(
                            ignoredChildrenFiles,
                            builder,
                            fileList,
                            depth - 1,
                            "$indentation  ", // 增加缩进
                        )
                    }
                }
            } else {
                // 处理文件
                val sizeStr = formatFileSize(ioFile.length())
                val lineCount = countFileLines(ioFile)
                val displayText = "$indentation[file] $currFileName ($sizeStr, $lineCount lines)"
                fileList.add(PathResult(type = "file", path = relativeFilePath))
                builder.appendLine(displayText)
            }
        }
    }

    private fun getIgnoredChildren(parentVfs: VirtualFile): List<VirtualFile> {
        val parentPath = parentVfs.toNioPath()
        val rules = ProjectStructureGenerator.createIgnoreRules(parentPath)
        // 根据过滤规则过滤子目录
        return parentVfs.children
            .filter { file ->
                val relPath = parentPath.relativize(file.toNioPath()).toString()
                if (relPath.isNotBlank()) {
                    val normalized = relPath.replace('\\', '/')
                    true != rules.checkIgnored(normalized, file.isDirectory)
                } else {
                    true
                }
            }
    }

    /**
     * 格式化文件大小显示
     * 将字节数转换为更易读的格式（B, KB, MB等）
     */
    private fun formatFileSize(size: Long): String {
        if (size < 1024) return "${size}B"
        val kiloBytes = size / 1024.0
        if (kiloBytes < 1024) return "${kiloBytes.roundToInt()}KB"
        val megaBytes = kiloBytes / 1024.0
        return String.format("%.1fMB", megaBytes)
    }

    /**
     * 统计文件行数
     * 通过读取文件内容计算总行数
     */
    private fun countFileLines(file: File): Int {
        return try {
            file.bufferedReader().use { reader ->
                reader.lines().count().toInt()
            }
        } catch (e: Exception) {
            // 如果无法读取文件内容，返回"?"
            0
        }
    }
}

/**
 * 目录信息数据类
 * 包含查询目录的路径及其他参数
 */
data class DirInfo(
    @ParamDesc("相对于工作空间根的列出内容的路径。例子 aaa/bbb/ccc,如果是当前项目的根路径，则传空")
    val relative_workspace_path: String,

    @ParamDesc(required = false, desc = "递归深度，不传默认为1,最大默认递归深度为3")
    val depth: Int = 1,

    @ParamDesc(required = false, desc = "一个句子解释了为什么要使用此工具，以及它如何贡献目标。")
    val explanation: String = "",
)
